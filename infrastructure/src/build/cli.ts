import { Argument, Command } from 'commander';
import { CONFIG, Configuration, ENVIRONMENTS } from '../utils';

type CLIParams = {
    environment: string;
    config: Configuration;
    noColor: boolean;
    operation: FixS3PermissionsOperation;
};
export type FixS3PermissionsOperation = {
    type: 'fixS3permissions';
};

export function parseCLI(): CLIParams {
    const program = new Command();
    let result: CLIParams | null = null;
    program
        .command('fixS3permissions')
        .name('fixS3permissions')
        .description('Fix S3 permissions to allow CloufFront access')
        .addArgument(new Argument('<environment>', 'environment name').choices(ENVIRONMENTS))
        .option('--noColor', 'do not use colored output')
        .action((environment: string, options: { noColor?: boolean }) => {
            const config = CONFIG[environment];
            result = {
                environment,
                noColor: !!options.noColor,
                config,
                operation: {
                    type: 'fixS3permissions',
                },
            };
        });

    program.parse();

    if (!result) {
        throw new Error('Unexpected, no CLI result determined');
    }
    return result;
}
