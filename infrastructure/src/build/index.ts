import { addOAIToBucketPolicy, CLITools, readCFStackOutputs } from '@balsamiq/iac/lib/cli/tools';
import { resolve as resolvePath } from 'path';
import { DATA_RESIDENCIES } from '../environment-variables-schemas';
import { parseCLI } from './cli';

export const PROJECT_DIR = resolvePath(`${__dirname}/../../..`);

const { environment, config, noColor, operation } = parseCLI();
const tools = new CLITools({ noColor, color: 'green', baseDir: PROJECT_DIR });

main().catch(async (err) => {
    console.error(err);
    process.exit(1);
});

async function main() {
    if (operation.type === 'fixS3permissions') {
        await fixS3PermissionsOperation();
    }
}

async function fixS3PermissionsOperation() {
    tools.title('Read stack outputs');

    const outputs = await readCFStackOutputs(`bas-${environment}-app`, config.vpc);
    for (let dataRedidencyKey of DATA_RESIDENCIES) {
        const oaiId = outputs.get(`bas-${environment}-OAI-${dataRedidencyKey.toUpperCase()}`);
        if (!oaiId) {
            throw new Error(`Missing OAI for data residency ${dataRedidencyKey}`);
        }
        await addOAIToBucketPolicy(
            config.dataResidencies[dataRedidencyKey].bucketName,
            config.dataResidencies[dataRedidencyKey].region,
            oaiId,
            null,
            tools
        );
    }
}
