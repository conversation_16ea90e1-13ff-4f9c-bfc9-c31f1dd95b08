import { BALSAMIQ_VPCS } from '@balsamiq/serverconfig/lib/balsamiq-vpcs';
import { Configuration } from '../utils';

export const CONFIGURATION = {
    vpc: BALSAMIQ_VPCS.llc,
    manualResources: {
        mysql: {
            subnetIds: BALSAMIQ_VPCS.internal.subnets.private,
            config: {
                instanceClass: 'db.r5.4xlarge',
                optionsGroup: 'default:mysql-8-0',
                engineVersion: '8.0.41',
                backupWindow: '02:30-03:00',
                maintenanceWindow: 'sun:05:01-sun:06:31',
                subnetGroupName: 'convox-prod-bas-mysql-subnetgroup',
                securityGroupIdPrefix: 'convox-prod-bas-mysql-SecurityGroup',
                caCertificateIdentifier: 'rds-ca-ecc384-g1',
                storageInGB: 783,
                storageType: 'gp3',
                multiAZCluster: true,
            },
            credentialsSecretArn: 'arn:aws:secretsmanager:us-east-1:924748183198:secret:bas-production-mysql-djyOb2',
        },
        redis: {
            subnetIds: BALSAMIQ_VPCS.internal.subnets.private,
            config: {
                multiAZ: true,
                backupsEnabled: true,
                maintenanceWindowUTC: 'wed:04:00-wed:05:00',
                nodeClass: 'cache.t3.medium',
                numNodes: 3,
                subnetGroupNamePrefix: 'bas-production-subnet-group-public',
                securityGroupIdPrefix: 'redis-production',
            },
        },
    },

    subdomain: 'bas20',
    reduceLogging: true,
    metricsRegion: 'us-east-1',
    createDnsRecords: false,
    dataResidencies: {
        us: {
            region: 'us-east-1',
            bucketName: 'convox-rack-production-bas-s3',
            subdomain: 'share',
            zone: BALSAMIQ_VPCS.llc.availableZones.balsamiq_com,
        },
        eu: {
            region: 'eu-west-1',
            bucketName: 'com-balsamiq-bas-eu',
            subdomain: 'share-eu',
            zone: BALSAMIQ_VPCS.llc.availableZones.balsamiq_com,
        },
    },
    defaultDataResidency: 'us',
    kms: {
        environment: 'production',
    },
    mysql: {
        region: 'us-east-1',
        endpoint: 'convox-prod-bas-mysql-encrypted.cc5xfgbtx6kw.us-east-1.rds.amazonaws.com',
    },
    redis: {
        endpoint: 'bas-production2.spdovv.ng.0001.use1.cache.amazonaws.com',
    },
    proxyConfig: [
        {
            prefix: '/bw-trello/',
            host: 'https://s3.amazonaws.com',
            path: '/bw-trello/',
        },
        {
            prefix: '/bw-atlassian/',
            host: 'https://s3.amazonaws.com',
            path: '/bw-atlassian/',
        },
        {
            prefix: '/media/',
            host: 'https://s3.amazonaws.com',
            path: '/editor.balsamiq.com/',
        },
        {
            prefix: '/editor/',
            host: 'https://s3.amazonaws.com',
            path: '/editor.balsamiq.com/',
        },
    ],
    confluenceNamespace: 'com.balsamiq.mockups.confluence',
    jiraNamespace: 'com.balsamiq.mockups.jira',
    serverApiSecretsArn: 'arn:aws:secretsmanager:us-east-1:924748183198:secret:bas-production-managedServerApiSecrets-MiJJR7',
    rtc: {
        secretArn: 'arn:aws:secretsmanager:us-east-1:924748183198:secret:rtc-production-managedConfig-KdUJpW',
        websocketsUri: 'rtcws.balsamiq.com',
    },
    i2w: {
        serviceUrl: 'https://api.balsamiq.com/i2w/',
    },
    w2i: {
        region: 'us-east-1',
        lambdaFunctionArn: 'arn:aws:lambda:us-east-1:924748183198:function:w2i-production-ExportFunction-ompUiKZ35fYH',
    },
    cloud: {
        baseUrl: 'https://balsamiq.cloud',
        secretArn: 'arn:aws:secretsmanager:us-east-1:924748183198:secret:cloud-production-secrets-flsZdL',
        projectsMaxAgeInDays: 30,
        timeDeltaForSavingLiveProjectsInMinutes: 30,
    },
    logRetentionDays: 90,
    processes: {
        server: {
            numberOfProcesses: 8,
            healthCheck: {
                path: '/health?db=true',
                intervalSeconds: 30,
                graceSeconds: 60,
            },
            resources: {
                cpu: 512,
                memoryHardLimitMiB: 4600, // hard limit, and anything that hits it gets destroyed
                sharedMemorySizeMiB: 1024, // size of the /dev/shm volume
            },
        },
        appsWatcher: {
            resources: {
                cpu: 256,
                memoryHardLimitMiB: 512, // hard limit, and anything that hits it gets destroyed
                sharedMemorySizeMiB: 1024, // size of the /dev/shm volume
            },
        },
        mainGardening: {
            resources: {
                cpu: 512,
                memoryHardLimitMiB: 4600, // hard limit, and anything that hits it gets destroyed
                sharedMemorySizeMiB: 1024, // size of the /dev/shm volume
            },
            schedule: '0 * * * ? *', // every hour
        },
        cloudGardening: {
            resources: {
                cpu: 512,
                memoryHardLimitMiB: 4600, // hard limit, and anything that hits it gets destroyed
                sharedMemorySizeMiB: 1024, // size of the /dev/shm volume
            },
            schedule: '*/15 * * * ? *', // every 15 minutes
        },
    },
    slackNotifications: {
        alarms: {
            emergency: ['fd_bas'],
            caution: ['bot_apps_alarms'],
            informative: ['bot_apps_dev'],
        },
    },
    inspectWithVanta: true,
} satisfies Configuration;
