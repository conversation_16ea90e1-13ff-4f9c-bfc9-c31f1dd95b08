import { BALSAMIQ_VPCS } from '@balsamiq/serverconfig/lib/balsamiq-vpcs';
import { Configuration } from '../utils';

export const CONFIGURATION = {
    vpc: BALSAMIQ_VPCS.internal,
    manualResources: {
        mysql: {
            subnetIds: BALSAMIQ_VPCS.internal.subnets.private,
            config: {
                instanceClass: 'db.t4g.large',
                optionsGroup: 'default:mysql-8-0',
                engineVersion: '8.0.40',
                backupWindow: '07:30-08:30',
                maintenanceWindow: 'sun:05:30-sun:06:30',
                subnetGroupName: 'bas-staging-mysql-subnetgroup',
                securityGroupIdPrefix: 'bas-staging-mysql-securityGroup',
                caCertificateIdentifier: 'rds-ca-rsa2048-g1',
                storageInGB: 50,
                storageType: 'gp3',
                multiAZCluster: false,
            },
            credentialsSecretArn: 'arn:aws:secretsmanager:eu-west-1:717726050199:secret:bas-staging-mysql-2N9Qxg',
        },
        redis: {
            subnetIds: BALSAMIQ_VPCS.internal.subnets.private,
            config: {
                multiAZ: false,
                backupsEnabled: false,
                maintenanceWindowUTC: 'sun:06:30-sun:07:30',
                nodeClass: 'cache.t4g.micro',
                numNodes: 1,
                subnetGroupNamePrefix: 'bas-staging-redis-redissubnetgroup',
                securityGroupIdPrefix: 'bas-staging-redis-sec-group',
            },
        },
    },

    subdomain: `bas20-staging`,
    reduceLogging: false,
    metricsRegion: 'eu-west-1',
    createDnsRecords: true,
    dataResidencies: {
        us: {
            region: 'us-east-1',
            bucketName: 'com-balsamiqstaff-bas20-staging-us',
            subdomain: 'share-staging-us',
            zone: BALSAMIQ_VPCS.internal.availableZones.balsamiqstaff_com,
        },
        eu: {
            region: 'eu-west-1',
            bucketName: 'com-balsamiqstaff-bas20-staging',
            subdomain: 'share-staging',
            zone: BALSAMIQ_VPCS.internal.availableZones.balsamiqstaff_com,
        },
    },
    defaultDataResidency: 'eu',
    kms: {
        environment: 'internal-staging',
    },
    mysql: {
        region: 'eu-west-1',
        endpoint: 'bas-staging.cfl8emb144hi.eu-west-1.rds.amazonaws.com',
    },
    redis: {
        endpoint: 'bas-staging.epbfco.0001.euw1.cache.amazonaws.com',
    },
    proxyConfig: [
        {
            prefix: '/bw-trello/',
            host: `https://s3.eu-west-1.amazonaws.com`,
            path: `/com-balsamiqstaff-bas20-staging-bw-trello/`,
        },
        {
            prefix: '/bw-atlassian/',
            host: `https://s3.eu-west-1.amazonaws.com`,
            path: `/com-balsamiqstaff-bas20-staging-bw-atlassian/`,
        },
        {
            prefix: '/media/',
            host: 'https://s3.amazonaws.com',
            path: '/editor-staging.balsamiq.com/',
        },
        {
            prefix: '/editor/',
            host: 'https://s3.amazonaws.com',
            path: '/editor-staging.balsamiq.com/',
        },
    ],
    confluenceNamespace: 'com.balsamiq.mockups.confluence.staging',
    jiraNamespace: 'com.balsamiq.mockups.jira.staging',
    serverApiSecretsArn: 'arn:aws:secretsmanager:eu-west-1:717726050199:secret:bas-staging-managedServerApiSecrets-hyqyPQ',
    rtc: {
        secretArn: 'arn:aws:secretsmanager:eu-west-1:717726050199:secret:rtc-staging-managedConfig-4JljKl',
        websocketsUri: 'rtcws-staging.balsamiqstaff.com',
    },
    i2w: {
        serviceUrl: 'https://api.balsamiqstaff.com/i2w-staging/',
    },
    w2i: {
        region: 'eu-west-1',
        lambdaFunctionArn: 'arn:aws:lambda:eu-west-1:717726050199:function:w2i-staging-ExportFunction-WJKZ9V226OsI',
    },
    cloud: {
        baseUrl: 'https://cloud-staging.balsamiqstaff.com',
        secretArn: 'arn:aws:secretsmanager:eu-west-1:717726050199:secret:cloud-staging-secrets-u43pGy',
        projectsMaxAgeInDays: 0.007,
        timeDeltaForSavingLiveProjectsInMinutes: 20,
    },
    logRetentionDays: 14,
    processes: {
        server: {
            numberOfProcesses: 1,
            healthCheck: {
                path: '/health?db=true',
                intervalSeconds: 30,
                graceSeconds: 60,
            },
            resources: {
                cpu: 512,
                memoryHardLimitMiB: 4600, // hard limit, and anything that hits it gets destroyed
                sharedMemorySizeMiB: 1024, // size of the /dev/shm volume
            },
        },
        appsWatcher: {
            resources: {
                cpu: 256,
                memoryHardLimitMiB: 512, // hard limit, and anything that hits it gets destroyed
                sharedMemorySizeMiB: 1024, // size of the /dev/shm volume
            },
        },
        mainGardening: {
            resources: {
                cpu: 256,
                memoryHardLimitMiB: 1024, // hard limit, and anything that hits it gets destroyed
                sharedMemorySizeMiB: 1024, // size of the /dev/shm volume
            },
            schedule: '*/5 * * * ? *', // every 5 minutes
        },
        cloudGardening: {
            resources: {
                cpu: 256,
                memoryHardLimitMiB: 1024, // hard limit, and anything that hits it gets destroyed
                sharedMemorySizeMiB: 1024, // size of the /dev/shm volume
            },
            schedule: '*/10 * * * ? *', // every 10 minutes
        },
    },
    slackNotifications: {
        alarms: {
            emergency: ['bot_apps_dev'],
            caution: ['bot_apps_dev'],
            informative: ['bot_apps_dev'],
        },
    },
    inspectWithVanta: false,
} satisfies Configuration;
