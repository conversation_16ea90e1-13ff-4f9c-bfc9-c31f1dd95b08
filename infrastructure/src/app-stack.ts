import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';

import {
    Annotations,
    aws_cloudfront,
    aws_cloudwatch,
    aws_iam,
    aws_kms,
    aws_lambda,
    aws_route53,
    aws_route53_targets,
    aws_certificatemanager as <PERSON>Manager,
    CfnOutput,
    aws_cloudfront_origins as CloudFrontOrigins,
    Duration,
    aws_s3 as S3,
    Stack,
    Tags,
} from 'aws-cdk-lib';
import { Construct, IConstruct } from 'constructs';

import { BikApplicationConstruct, ZoneLookup } from '@balsamiq/iac/lib/cli/ecs-app';
import { AlarmsConstruct } from '@balsamiq/iac/lib/slackStack';
import { BALSAMIQ_CERTS } from '@balsamiq/serverconfig/lib/balsamiq-certs';

import { APP_SPECS } from './ecs-app';
import { BasAppConfig, DATA_RESIDENCIES } from './environment-variables-schemas';
import { CONFIG, Configuration, getBasAppConfig } from './utils';

export class BasAppStack extends Stack {
    constructor(scope: IConstruct, id: string, { environment, buildId }: { environment: string; buildId: string }) {
        const appSettings = APP_SPECS.specs({ environment });
        const config = CONFIG[environment];

        super(scope, id, {
            stackName: `${APP_SPECS.appName}-${environment}-${id}`,
            env: { region: appSettings.vpc.region, account: appSettings.vpc.account.accountId },
            description: `Service ${APP_SPECS.appName} (env ${environment}): stateless application stack`,
        });

        const stackPrefix = `${APP_SPECS.appName}-${environment}`;

        const jwtKeyAliasName = `alias/user-identity-api-secret`;
        const jwtKey = aws_kms.Key.fromLookup(this, 'jwtKey', { aliasName: jwtKeyAliasName });

        const basAppConfig: BasAppConfig = {
            ...getBasAppConfig(environment),
            kms: {
                region: jwtKey.env.region,
                key_alias: jwtKeyAliasName,
                environment: CONFIG[environment].kms.environment,
            },
        };

        const bikApp = new BikApplicationConstruct(this, id, {
            appSpecs: APP_SPECS,
            deploymentSpecs: {
                vpc: appSettings.vpc,
                environment,
                logRetentionDays: config.logRetentionDays,
                streamLogsToLBC: true,
                environmentVariables: {
                    APP_NAME: `${APP_SPECS.appName}-${environment}`,
                    BAS_ENV: environment,
                    BUILD_NUMBER: buildId,
                    BAS_FULL_DOMAIN_NAME: `${config.subdomain}.${appSettings.vpc.domainName}`,
                    BAS_APP_CONFIG: JSON.stringify(basAppConfig),
                },
                managedPolicies: [
                    new aws_iam.ManagedPolicy(this, 'jwtKeyPolicy', {
                        description: `Policies for the BAS runtime application, environment "${environment}"`,
                        statements: [
                            // Policy for KMS key to generate user-identity JWT tokens
                            new aws_iam.PolicyStatement({
                                effect: aws_iam.Effect.ALLOW,
                                actions: ['kms:Encrypt'],
                                resources: [jwtKey.keyArn],
                            }),
                            // Policy for metrics
                            new aws_iam.PolicyStatement({
                                effect: aws_iam.Effect.ALLOW,
                                actions: ['cloudwatch:PutMetricData'],
                                resources: ['*'],
                            }),
                            // Policy for wireframes2image
                            new aws_iam.PolicyStatement({
                                effect: aws_iam.Effect.ALLOW,
                                actions: ['lambda:InvokeFunction'],
                                resources: [config.w2i.lambdaFunctionArn],
                            }),
                            // Policy for S3 buckets
                            new aws_iam.PolicyStatement({
                                effect: aws_iam.Effect.ALLOW,
                                actions: ['s3:PutObject', 's3:GetObject', 's3:DeleteObject'],
                                resources: DATA_RESIDENCIES.map(
                                    (residency) => `arn:aws:s3:::${config.dataResidencies[residency].bucketName}/*`
                                ),
                            }),
                            // BAS MySQL credentials secret policy
                            new aws_iam.PolicyStatement({
                                effect: aws_iam.Effect.ALLOW,
                                actions: ['secretsmanager:GetSecretValue'],
                                resources: [config.manualResources.mysql.credentialsSecretArn],
                            }),
                        ],
                    }),
                ],
            },
        });

        const alarms = bikApp.createAlarmsConstruct(this, 'alarms', { notifications: config.slackNotifications.alarms });

        const cdnSharesResult = this.cdnShares(config, environment, stackPrefix);

        this.setupAlarms(alarms);
        this.setupEdgeAlarms(
            cdnSharesResult.lambdaEdgeStack
                ? bikApp.createAlarmsConstruct(this, 'edgeAlarms', {
                      notifications: config.slackNotifications.alarms,
                      snsScope: cdnSharesResult.lambdaEdgeStack,
                  })
                : alarms,
            cdnSharesResult.lambdaEdgeStack ?? this,
            cdnSharesResult.edgeFunctions
        );
    }

    private setupEdgeAlarms(alarms: AlarmsConstruct, scope: Construct, edgeFunctions: aws_cloudfront.experimental.EdgeFunction[]) {
        for (const edgeFunction of edgeFunctions) {
            alarms.addAlarm(
                'emergency',
                // We create this alarm in the edge function's scope, because the errors metric will
                // always be created in the edge region (i.e. us-east-1) because of how Lambda@Edge works,
                // and the alarm and the metric must be in the same region.
                new aws_cloudwatch.Alarm(scope, `${edgeFunction.node.id}FunctionErrorsAlarm`, {
                    metric: edgeFunction.metricErrors(),
                    threshold: 1,
                    evaluationPeriods: 1,
                    treatMissingData: aws_cloudwatch.TreatMissingData.NOT_BREACHING,
                })
            );
        }
    }

    private setupAlarms(_alarms: AlarmsConstruct) {}

    private cdnShares(
        config: Configuration,
        environment: string,
        stackPrefix: string
    ): { lambdaEdgeStack: Stack | null; edgeFunctions: aws_cloudfront.experimental.EdgeFunction[] } {
        // Since Lambda@Edge doesn't allow proper environment variables, in
        // order to customize runtime based on the environment, we're doing a
        // little bit of a "hack" and replace some placeholders, which contain
        // domain names, URLs, and such, that depend on the environment:
        const tempLambdaEdgeDirectory = fs.mkdtempSync(path.join(os.tmpdir(), 'bas-lambdaEdge-share-'));
        fs.cpSync(path.join(__dirname, '../../src/lambdaEdge/share'), tempLambdaEdgeDirectory, { recursive: true });
        const filePaths = fs
            .readdirSync(tempLambdaEdgeDirectory, { encoding: 'utf8', recursive: true })
            .map((p) => path.join(tempLambdaEdgeDirectory, p))
            .filter((p) => fs.statSync(p).isFile());
        for (const file of filePaths) {
            let body = fs.readFileSync(file, 'utf8');
            body = body.replace(/__PLACEHOLDER__DOMAIN_NAME__/g, `${config.subdomain}.${config.vpc.domainName}`);
            body = body.replace(
                /__PLACEHOLDER__SHARE_URL__/g,
                `https://${`share${environment === 'production' ? '' : `-${environment}`}.${config.vpc.domainName}`}`
            );
            fs.writeFileSync(file, body, 'utf8');
        }

        // NOTE: in production we had to add a 'Cdn' suffix in order to avoid overlapping with older lambdas used in the legacy Convox stack
        const lambdaEdgeStackName = `${stackPrefix}-lambdaEdge${environment === 'production' ? 'Cdn' : ''}`;

        const edgeLambdas: {
            eventType: aws_cloudfront.LambdaEdgeEventType;
            edgeFunction: aws_cloudfront.experimental.EdgeFunction;
        }[] = [
            // {
            //     eventType: CloudFront.LambdaEdgeEventType.VIEWER_REQUEST,
            //     edgeFunction: new CloudFront.experimental.EdgeFunction(this, `${lambdaEdgeStackName}-share-viewerRequest`, {
            //         functionName: `${lambdaEdgeStackName}-share-viewerRequest`,
            //         // `CloudFront.experimental.EdgeFunction` will internally
            //         // create a separate stack (i.e., `<function>.lambda.stack`)
            //         // and this is how we name that stack:
            //         stackId: lambdaEdgeStackName,
            //         logRetention: config.logRetentionDays,
            //         /* Placeholder for the future */
            //     }),
            // },
            {
                eventType: aws_cloudfront.LambdaEdgeEventType.ORIGIN_REQUEST,
                edgeFunction: new aws_cloudfront.experimental.EdgeFunction(this, `${lambdaEdgeStackName}-share-originRequest`, {
                    functionName: `${lambdaEdgeStackName}-share-originRequest`,
                    // `CloudFront.experimental.EdgeFunction` will internally
                    // create a separate stack (i.e., `<function>.lambda.stack`)
                    // and this is how we name that stack:
                    stackId: lambdaEdgeStackName,
                    logRetention: config.logRetentionDays,
                    runtime: aws_lambda.Runtime.NODEJS_22_X,
                    code: aws_lambda.Code.fromAsset(path.join(tempLambdaEdgeDirectory, 'originRequest')),
                    handler: 'index.handler',
                }),
            },
            {
                eventType: aws_cloudfront.LambdaEdgeEventType.ORIGIN_RESPONSE,
                edgeFunction: new aws_cloudfront.experimental.EdgeFunction(this, `${lambdaEdgeStackName}-share-originResponse`, {
                    functionName: `${lambdaEdgeStackName}-share-originResponse`,
                    // `CloudFront.experimental.EdgeFunction` will internally
                    // create a separate stack (i.e., `<function>.lambda.stack`)
                    // and this is how we name that stack:
                    stackId: lambdaEdgeStackName,
                    logRetention: config.logRetentionDays,
                    runtime: aws_lambda.Runtime.NODEJS_22_X,
                    code: aws_lambda.Code.fromAsset(path.join(tempLambdaEdgeDirectory, 'originResponse')),
                    handler: 'index.handler',
                }),
            },
            {
                eventType: aws_cloudfront.LambdaEdgeEventType.VIEWER_RESPONSE,
                edgeFunction: new aws_cloudfront.experimental.EdgeFunction(this, `${lambdaEdgeStackName}-share-viewerResponse`, {
                    functionName: `${lambdaEdgeStackName}-share-viewerResponse`,
                    // `CloudFront.experimental.EdgeFunction` will internally
                    // create a separate stack (i.e., `<function>.lambda.stack`)
                    // and this is how we name that stack:
                    stackId: lambdaEdgeStackName,
                    logRetention: config.logRetentionDays,
                    runtime: aws_lambda.Runtime.NODEJS_22_X,
                    code: aws_lambda.Code.fromAsset(path.join(tempLambdaEdgeDirectory, 'viewerResponse')),
                    handler: 'index.handler',
                    timeout: Duration.seconds(1),
                }),
            },
        ];

        let lambdaEdgeStack: Stack | null = null;
        if (edgeLambdas.length && edgeLambdas[0].edgeFunction.lambda.stack.stackId !== this.stackId) {
            lambdaEdgeStack = edgeLambdas[0].edgeFunction.lambda.stack;
            const tags = Tags.of(lambdaEdgeStack);
            tags.add(
                'balsamiq-notes',
                `This resource was created in ${lambdaEdgeStack.region} because of how Lambda@Edge works. All other resources of this app can be found in ${this.region} instead.`,
                { applyToLaunchedInstances: true }
            );
        }

        // CDK is warning us that CloudFormation is not able to update the bucket policy to add this OAI for CloudFront.
        // We know that, so we suppress the warning. We're updating the bucket policy later in the deployment process, via SDK.
        Annotations.of(this).acknowledgeWarning(
            '@aws-cdk/aws-cloudfront-origins:updateImportedBucketPolicyOai',
            'The S3 bucket policy is updated via SDK by a later step during deployment'
        );

        const zones = new ZoneLookup(this, config.vpc.vpcId);

        for (const dataResidencyName of DATA_RESIDENCIES) {
            const scope = new Construct(this, `DataResidency${dataResidencyName.toUpperCase()}`);
            const regionInfo = config.dataResidencies[dataResidencyName];
            const fullDomainName = `${regionInfo.subdomain}.${regionInfo.zone.name}`;

            const distributionId = `shareDistribution${dataResidencyName.toUpperCase()}`;
            const originId = `${stackPrefix}-${distributionId}Origin`;

            // CloudFront certificates must be located in N. Virginia (us-east-1):
            const certificate = BALSAMIQ_CERTS[config.vpc.account.key]['us-east-1'][config.vpc.domainName];
            if (!certificate) {
                throw new Error(
                    `Could not find the SSL certificate for '${config.vpc.account.key}' account and '${config.vpc.domainName}' domain`
                );
            }

            const bucket = S3.Bucket.fromBucketAttributes(scope, 'bucket', {
                region: regionInfo.region,
                bucketName: regionInfo.bucketName,
            });

            const originAccessIdentity = new aws_cloudfront.OriginAccessIdentity(scope, 'OAI', {
                // This will set the "Name" column in AWS console:
                comment: `Identity for ${originId}`,
            });
            new CfnOutput(scope, `OAI${dataResidencyName.toUpperCase()}`, {
                exportName: `${stackPrefix}-OAI-${dataResidencyName.toUpperCase()}`,
                value: originAccessIdentity.originAccessIdentityId,
                description: `CF Origin Access Identity for S3 (${dataResidencyName.toUpperCase()})`,
            });

            const shareDistribution = new aws_cloudfront.Distribution(scope, 'shareDistribution', {
                defaultBehavior: {
                    // Only reason why we're specifying a custom `originId` and
                    // `originAccessIdentity` is to give prettier names and
                    // descriptions to resources
                    origin: CloudFrontOrigins.S3BucketOrigin.withOriginAccessIdentity(bucket, {
                        originId,
                        // Creating the origin access identity with the scope of
                        // `this` [stack] creates a cyclic reference with the
                        // user data stack (no bueno), therefore we're creating
                        // it with the scope of the user data stack instead:
                        originAccessIdentity,
                    }),
                    allowedMethods: aws_cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
                    cachePolicy: new aws_cloudfront.CachePolicy(scope, 'cachePolicy', {
                        cachePolicyName: `${stackPrefix}-${distributionId}CachePolicy`,
                        comment: `Policy for ${distributionId}, environment "${environment}"`,
                        cookieBehavior: aws_cloudfront.CacheCookieBehavior.all(),
                        queryStringBehavior: aws_cloudfront.CacheCookieBehavior.all(),
                    }),
                    viewerProtocolPolicy: aws_cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
                    edgeLambdas: edgeLambdas.map((edgeLambda) => ({
                        eventType: edgeLambda.eventType,
                        functionVersion: edgeLambda.edgeFunction.currentVersion,
                    })),
                },
                certificate: CertificateManager.Certificate.fromCertificateArn(scope, 'certificate', certificate.arn),
                comment: `${stackPrefix}-${distributionId}`, // `comment` becomes the "Description" on the AWS console, which is sort of a human-readable "name"
                domainNames: [fullDomainName],
                minimumProtocolVersion: aws_cloudfront.SecurityPolicyProtocol.TLS_V1_2_2021,
            });

            if (config.createDnsRecords) {
                new aws_route53.ARecord(scope, 'ARecord', {
                    recordName: fullDomainName,
                    zone: zones.getZone(regionInfo.zone),
                    target: aws_route53.RecordTarget.fromAlias(new aws_route53_targets.CloudFrontTarget(shareDistribution)),
                });
            }
        }

        return { lambdaEdgeStack, edgeFunctions: edgeLambdas.map((x) => x.edgeFunction) };
    }
}
