import { cli } from '@balsamiq/iac/lib/cli/cli';

import { APP_SPECS } from './ecs-app';

import { CONFIG, MYSQL_PORT, REDIS_PORT } from './utils';

cli({
    appSpecs: APP_SPECS,
    proxies: {
        names: ['mysql', 'redis'],
        specs({ environment }) {
            const deploymentConfig = CONFIG[environment];

            if (!deploymentConfig.mysql) {
                throw new Error('Missing MySQL configuration in infrastructure/config.ts');
            }

            if (!deploymentConfig.redis) {
                throw new Error('Missing Redis configuration in infrastructure/config.ts');
            }

            return [
                {
                    key: 'mysql',
                    localPort: MYSQL_PORT, // a positional number will be prepended to this
                    serviceUrl: deploymentConfig.mysql.endpoint,
                    servicePort: MYSQL_PORT.toString(),
                },
                {
                    key: 'redis',
                    localPort: REDIS_PORT, // a positional number will be prepended to this
                    serviceUrl: deploymentConfig.redis.endpoint,
                    servicePort: REDIS_PORT.toString(),
                },
            ].map((item, index) => {
                if (index > 5) {
                    throw new Error(`\
    You cannot use this positional index prepending "trick" when you have more \
    than 5 resources, because you'd exceed the maximum allowed port number 65535!\
    `);
                }

                return {
                    ...item,
                    // it is sort of a best-practice local ports to resemble their defaults:
                    // localPort: Number(`${index + 1}${item.localPort}`),
                    localPort: Number(item.localPort) + 20000,
                };
            });
        },
    },
});
