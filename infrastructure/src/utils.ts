import { MySQLClusterConfig, RedisClusterConfig } from '@balsamiq/iac/lib/cli/resources';
import { NotificationsConfiguration } from '@balsamiq/iac/lib/slack';
import { BalsamiqVpc, DnsZoneDetails } from '@balsamiq/serverconfig/lib/balsamiq-vpcs';
import * as fs from 'fs';
import * as path from 'path';
import { BasAppConfig, DATA_RESIDENCIES, DataResidencyName } from './environment-variables-schemas';

export const CONFIG: { [K in string]: Configuration } = {};
export const ENVIRONMENTS: string[] = [];

const configurationFolder = path.join(__dirname, 'configuration');

fs.readdirSync(configurationFolder).forEach((file) => {
    const filePath = path.join(configurationFolder, file);
    if (fs.statSync(filePath).isFile() && file.endsWith('.ts')) {
        const environmentName = file.replace('.ts', '');
        const conf = require(filePath).CONFIGURATION;
        ENVIRONMENTS.push(environmentName);
        CONFIG[environmentName] = conf;
    }
});

export const REDIS_PORT = 6379;
export const MYSQL_PORT = 3306;

export type Configuration = {
    vpc: BalsamiqVpc;
    manualResources: {
        mysql: {
            subnetIds: string[];
            config: MySQLClusterConfig;
            credentialsSecretArn: string;
        };
        redis: {
            subnetIds: string[];
            config: RedisClusterConfig;
        };
    };
    subdomain: string;
    createDnsRecords: boolean;
    dataResidencies: {
        [K in DataResidencyName]: {
            region: string;
            bucketName: string;
            subdomain: string;
            zone: DnsZoneDetails;
        };
    };
    defaultDataResidency: DataResidencyName;
    reduceLogging: boolean;
    mysql: {
        region: string;
        endpoint: string;
    };
    redis: {
        endpoint: string;
    };
    proxyConfig: {
        prefix: string;
        host: string;
        path: string;
    }[];
    confluenceNamespace: string;
    jiraNamespace: string;
    serverApiSecretsArn: string;
    metricsRegion: string;
    kms: {
        environment: string;
    };
    rtc: {
        secretArn: string;
        websocketsUri: string;
    };
    i2w: {
        serviceUrl: string;
    };
    w2i: {
        region: string;
        lambdaFunctionArn: string;
    };
    cloud: {
        baseUrl: string;
        secretArn: string;
        projectsMaxAgeInDays: number;
        timeDeltaForSavingLiveProjectsInMinutes: number;
    };
    logRetentionDays: 14 | 30 | 60 | 90;
    processes: {
        server: {
            numberOfProcesses: number;
            healthCheck: {
                path: string;
                intervalSeconds: number;
                graceSeconds: number;
            };
            resources: ProcessResourcesConfig;
        };
        appsWatcher: {
            resources: ProcessResourcesConfig;
        };
        mainGardening: {
            resources: ProcessResourcesConfig;
            schedule: string;
        };
        cloudGardening: {
            resources: ProcessResourcesConfig;
            schedule: string;
        };
    };
    slackNotifications: {
        alarms: NotificationsConfiguration;
    };
    inspectWithVanta: boolean;
};

type ProcessResourcesConfig = {
    cpu: number;
    memoryHardLimitMiB: number;
    sharedMemorySizeMiB: number;
};

export function getBasAppConfig(environment: string): Omit<BasAppConfig, 'kms'> {
    const config = CONFIG[environment];
    return {
        reduceLogging: config.reduceLogging,
        redis: {
            url: config.redis.endpoint,
            port: REDIS_PORT,
        },
        mysql: {
            region: config.mysql.region,
        },
        permalinkStorage: {
            eu: {
                bucketRegion: config.dataResidencies['eu'].region,
                bucketName: config.dataResidencies['eu'].bucketName,
                baseDir: 'permalinks',
            },
            us: {
                bucketRegion: config.dataResidencies['us'].region,
                bucketName: config.dataResidencies['us'].bucketName,
                baseDir: 'permalinks',
            },
        },
        dataResidencyShares: {
            eu: `https://${config.dataResidencies['eu'].subdomain}.${config.dataResidencies['eu'].zone.name}`,
            us: `https://${config.dataResidencies['us'].subdomain}.${config.dataResidencies['us'].zone.name}`,
        },
        defaultDataResidencyName: config.defaultDataResidency,
        rtc: {
            websocketsUri: config.rtc.websocketsUri,
        },
        cloud: {
            baseUrl: config.cloud.baseUrl,
            projectsMaxAgeInDays: config.cloud.projectsMaxAgeInDays,
            timeDeltaForSavingLiveProjectsInMinutes: config.cloud.timeDeltaForSavingLiveProjectsInMinutes,
        },
        proxyConfig: config.proxyConfig,
        confluenceNamespace: config.confluenceNamespace,
        jiraNamespace: config.jiraNamespace,
        metricsRegion: config.metricsRegion,
        clusterSize: config.processes.server.numberOfProcesses,
        i2w: {
            serviceUrl: config.i2w.serviceUrl,
        },
        w2i: {
            region: config.w2i.region,
            lambdaFunctionArn: config.w2i.lambdaFunctionArn,
        },
    };
}
