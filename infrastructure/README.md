# BAS Infrastructure

Build and manage BAS infrastructure as code.

## Quick start

    npm install

## Things to know

The `cdk.json` file tells the CDK Toolkit how to execute your app.

Useful commands:

* `npm run cdk list -- --context env=<production|staging|ondeck|stefano|...>`    to list the CF stacks that would be created with this code (no actual interaction with AWS)
* `npm run cdk synth <stackname> -- --context env=<production|staging|ondeck|stefano|...>`  generate a stack CF template and print it to stdout

Other commands from the CDK documentation:

* `npm run build`   compile typescript to js
* `npm run watch`   watch for changes and compile
* `npm run test`    perform the jest unit tests
* `npm run cdk deploy -- --context env=<production|staging|ondeck|stefano|...>`      deploy this stack to your default AWS account/region
* `npm run cdk diff -- --context env=<production|staging|ondeck|stefano|...>`        compare deployed stack with current state
* `npm run cdk synth -- --context env=<production|staging|ondeck|stefano|...>`       emits the synthesized CloudFormation template


## Documentation

- [CDK Guide](https://docs.aws.amazon.com/cdk/v2/guide/home.html)

    npm -g install typescript aws-cdk

API Reference: https://docs.aws.amazon.com/cdk/api/v2/docs/aws-construct-library.html

    aws sts get-caller-identity --output json

    npx aws-cdk init --language typescript
    npm run build
    npm run cdk ls
    npm run cdk synth

## NOTE for M1 Macs

Bundling happens with `esbuild` which is made of native code. CDK runs it inside Docker to "solve" the cross-compilation issue, but it isn't
smart enough to figure out M1s, so the build typically fails with an error like this:

    The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8) and no specific platform was requested

You can fix it by setting the following environment variable, which instructs Docker to pin the platform type rather than guessing it basing on the host:

    export DOCKER_DEFAULT_PLATFORM="linux/amd64"
