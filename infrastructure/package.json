{"name": "bas", "version": "0.1.0", "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "format": "npx prettier --config .prettierrc --write --check \"./**/*.ts\""}, "devDependencies": {"@types/jest": "^29.5.4", "@types/node": "^20.6.0", "aws-cdk": "^2.1015.0", "jest": "^29.6.4", "prettier": "^3.0.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "dependencies": {"@aws-sdk/client-cloudformation": "^3.758.0", "@aws-sdk/client-s3": "^3.758.0", "@balsamiq/iac": "^8.56.0", "@balsamiq/serverconfig": "^9.16.1", "aws-cdk-lib": "^2.184.1", "commander": "^13.1.0", "constructs": "^10.2.70", "source-map-support": "^0.5.21", "zod": "^3.24.4"}}