#!/bin/bash

# This script ensures that the Node.js version specified in .nvmrc is used
# Source nvm if it exists
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh"

# Use the version specified in .nvmrc
cd "$(dirname "$0")"
NODE_VERSION=$(cat .nvmrc)

# Find the latest installed version of Node that matches the major version
LATEST_MATCHING=$(nvm ls $NODE_VERSION | grep -o "v$NODE_VERSION\.[0-9]*\.[0-9]*" | sort -V | tail -n 1)

if [ -z "$LATEST_MATCHING" ]; then
  echo "No Node.js version matching $NODE_VERSION found. Please run 'nvm install $NODE_VERSION'"
  exit 1
fi

# Use the matched version
NODE_PATH="$NVM_DIR/versions/node/$LATEST_MATCHING/bin/node"
if [ ! -f "$NODE_PATH" ]; then
  echo "Node.js binary not found at $NODE_PATH. Please check your nvm installation."
  exit 1
fi

# Execute Node with all the arguments passed to this script
exec "$NODE_PATH" "$@"