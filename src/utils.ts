/** @prettier */
import jwtModule from 'atlassian-jwt';
import { type SessionData, SessionManager } from './session-manager.ts';
import type { Logger } from '@balsamiq/logging';
import type { Request, Response } from 'express';
import { type BASLegacyCallback, isBASLegacyErrorObject } from './calling-style.ts';
import * as uuid from 'uuid';
import xss from 'xss';
import path from 'path';
import type { RedisAdapter } from './redisAdapter.ts';
import { Readable } from 'stream';
import barMod from '@balsamiq/bmpr/lib/BalsamiqArchive.js';
import type { OutgoingHttpHeaders, OutgoingHttpHeader } from 'http';
import superagent from 'superagent';
import assert from 'assert';
import type { Config } from './configLoader.ts';
import { SQLiteAdapter } from './sqlite-adapter.ts';
import Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js';
import type { DBConnector } from './db_connector.js';
import * as jwt from './connectors/lib/jwt.js';
import { callWithLegacyCallback } from './calling-style.ts';

export function verifyAtlassianJWT(
    logger: Logger,
    req: Request,
    sessionManager: SessionManager,
    callback: BASLegacyCallback<{ success: 'ok' }>
) {
    if (!req.query.jwt) {
        callback({ error: 'missing JWT token' });
        return;
    }

    let jwtToken: string;
    let alg: any; // Unexported type from atlassian-jwt
    let claims: any; // Unexported type from atlassian-jwt
    try {
        jwtToken = `${req.query.jwt}`;
        alg = jwtModule.getAlgorithm(jwtToken);
        claims = jwtModule.decodeSymmetric(jwtToken, '', alg, true);
    } catch (e) {
        callback({ error: 'cannot validate the JWT token, error parsing the token: ' + req.query.jwt });
        return;
    }

    // const req2 = jwtModule.fromExpressRequest(req);
    // console.log(req2.pathname);
    const hash = jwtModule.createQueryStringHash(jwtModule.fromExpressRequest(req));

    if (hash !== claims.qsh) {
        callback({ error: 'invalid JWT token, QSH verification failed' });
        return;
    }

    let connectorKind;
    // TODO: find better way to derive connector type
    if (req.path.includes('/confluence/')) {
        connectorKind = 'confluence';
    } else if (req.path.includes('/jira/')) {
        connectorKind = 'jira';
    } else {
        callback({ error: 'cannot validate the JWT token, unexpected connector type: ' + req.path });
        return;
    }

    sessionManager.createSession(logger, 'validate JWT', function (obj) {
        if (isBASLegacyErrorObject(obj)) {
            callback({ error: 'cannot validate the JWT token, unable to establish a db connection' });
            return;
        }
        const sessionData = obj;
        const dbConnector = sessionData.dbConnector;

        dbConnector.getConnectorData(connectorKind, claims.iss, function (instanceObj) {
            sessionManager.releaseSession(sessionData, function (_) {
                if (isBASLegacyErrorObject(instanceObj) || 'error' in instanceObj) {
                    callback({ error: 'cannot validate the JWT token, unable to retrieve connector data' });
                    return;
                }

                if (!('sharedSecret' in instanceObj) || typeof instanceObj.sharedSecret !== 'string') {
                    callback({ error: 'cannot validate the JWT token, missing shared secret ' + claims.iss });
                    return;
                }

                try {
                    jwtModule.decodeSymmetric(jwtToken, instanceObj.sharedSecret, alg, false);
                    callback({ success: 'ok' });
                } catch (error) {
                    callback({ error: 'invalid JWT signature ' });
                }
            });
        });
    });
}

// TODO: move this function into confluence connector after it has been converted to typescript
export function returnRenderConfluenceImage(
    data: { width: string; alignment: string; src: string },
    res: Response,
    sessionData: SessionData
) {
    const sessionManager = sessionData.sessionManager;
    const maxWidth = 600;

    let width;
    if (data.width) {
        width = Number.parseInt(data.width);
    }

    let alignment = 'center';
    if (data.alignment == 'Left') {
        alignment = 'left';
    } else if (data.alignment == 'Right') {
        alignment = 'right';
    }

    const imageTag = `<img alt="Balsamiq Wireframes" style="max-width: ${maxWidth}px; ${width ? 'width:' + width + 'px' : ''}" src="${data.src}">`;
    const content = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <title>Balsamiq Wireframes</title>
            <meta charset="UTF-8">
        </head>
        <body>
            <div style="width: 100%; height: 100%; text-align:${alignment};">
            ${xss(imageTag)}
            </div>
        </body>
        </html>
    `;
    sessionManager.releaseSession(sessionData, function (_) {
        res.setHeader('Content-Type', 'text/html');
        res.send(Buffer.from(content, 'utf8'));
    });
}

export function sanitizeAndNormalizePath(inputPath: string) {
    // Normalize the path to remove any ../ or ./ sequences
    const normalizedPath = path.posix.normalize(inputPath);

    // Prevent path from navigating outside the root by removing leading slashes
    const safePath = normalizedPath.replace(/^(\.\.(\/|\\|$))+/, '');

    return safePath;
}

export function getSessionIdFromBearerToken(req: { headers: { authorization?: string } }): string {
    const authHeader = req.headers.authorization || '';
    if (!authHeader.startsWith('Bearer ')) {
        throw new Error('Missing credentials or wrong auth token');
    }
    return authHeader.split(' ', 2)[1];
}

export function getUUID() {
    return uuid.v1().replace(/-/g, '_');
}

export function parseRedisKey(tag: string, key: string) {
    if (key.startsWith(tag + '::')) {
        const keyPart = key.split('::');
        if (keyPart.length === 3) {
            return {
                type: tag,
                sessionToken: keyPart[1],
                channelId: keyPart[2],
            };
        }
    }
    return null;
}

export async function doAddKeyInRedis(
    keyMakerFn: (sessionToken: string, channelId: string) => string,
    redisAdapter: RedisAdapter,
    logger: Logger,
    sessionToken: string,
    expirationInSecs = 60 * 10
): Promise<{ success: 'ok' } | { missingKey: true }> {
    const channelId = uuid.v1();
    const key = keyMakerFn(sessionToken, channelId);
    await redisAdapter.sadd(key, sessionToken);
    const found = await redisAdapter.expire(key, expirationInSecs);
    logger.info(`Key ${key} added with expiration ${expirationInSecs} secs`);
    return found ? { success: 'ok' } : { missingKey: true };
}

export function base64ToStream(base64: string) {
    const img = Buffer.from(base64, 'base64');
    const readable = new Readable();
    readable.push(img);
    readable.push(null);
    return { stream: readable, length: img.byteLength };
}

export const getThumbnailFromDump = barMod.getThumbnailFromDump;

const CHARACTER_SET = '0123456789abcdefghijklmnopqrstuvwxyz'.split('');

export function encodeBase36(integer: number) {
    if (integer === 0) {
        return '0';
    }
    let s = '';
    while (integer > 0) {
        s = CHARACTER_SET[integer % 36] + s;
        integer = Math.floor(integer / 36);
    }
    return s;
}

export function getShortIDFromResourceOrBranchID(id: string) {
    return id.substring(0, 4);
}

export function pipeStreamToResponse(stream: Readable, res: Response, headers: OutgoingHttpHeaders | OutgoingHttpHeader[]) {
    return new Promise<{}>((resolve, reject) => {
        stream
            .on('readable', () => {
                // Send response headers only when data is ready to be read
                if (!res.headersSent) {
                    res.writeHead(200, headers);
                }
                stream.read();
            })
            .on('error', (err) => {
                // Handles errors on the read stream
                reject(err);
            })
            .pipe(res)
            .on('error', () => {
                // Handles errors on the write stream
                resolve({}); // do not reject on client or network error
            })
            .on('close', () => {
                resolve({});
            });
    });
}

export function pipeToNewRequest(
    incomingReq: Request,
    outgoingRes: Response,
    options: { url: string; headers?: object; timeout?: number; incomingResHandler?: (incomingRes: superagent.Response) => void },
    logger: Logger,
    callback?: BASLegacyCallback<{}>
) {
    logger = logger.getLogger({ module: 'pipeToNewRequest' });

    let callbackCalled = false;
    function invokeCallbackIfNeeded(result: { error: string } | {}) {
        if (callback && !callbackCalled) {
            callbackCalled = true;
            callback(result);
        }
    }

    // The following are headers that should not be forwarded to the target URL
    const excludedHeaders = ['host', 'referer', 'referrer', 'origin', 'connection', 'content-length', 'transfer-encoding', 'upgrade'];
    const incomingReqHeaders = Object.fromEntries(
        Object.entries(incomingReq.headers).filter(([key]) => {
            return (
                !excludedHeaders.includes(key.toLowerCase()) &&
                !key.toLowerCase().startsWith('proxy-') &&
                !key.toLowerCase().startsWith('sec-')
            );
        })
    );

    const outgoingReq = superagent(incomingReq.method, options.url).set({ ...incomingReqHeaders, ...options.headers });

    if (options.timeout !== undefined) {
        outgoingReq.timeout(options.timeout);
    }

    outgoingReq.on('error', (err: Error) => {
        logger.error('outgoing request error: ' + err.message + ' ' + options.url);
        invokeCallbackIfNeeded({ error: err.message });
        outgoingRes.end();
    });

    outgoingReq.on('response', (incomingRes: superagent.Response) => {
        if (options.incomingResHandler) {
            options.incomingResHandler(incomingRes);
        }
        outgoingRes.writeHead(incomingRes.statusCode, incomingRes.headers);
    });

    try {
        outgoingReq.pipe(outgoingRes);
    } catch (err) {
        assert(err instanceof Error, 'Error is not an instance of Error');
        logger.error('Pipe error: ' + err.message + ' ' + options.url);
        invokeCallbackIfNeeded({ error: err.message });
        outgoingRes.end();
        return;
    }

    let outgoingReqFinished = false;
    outgoingRes
        .on('finish', () => {
            logger.info('pipeToNewRequest: client received all data');
            outgoingReqFinished = true;
        })
        .on('close', () => {
            logger.info('pipeToNewRequest: client closed connection');
            if (outgoingReqFinished) {
                invokeCallbackIfNeeded({});
            } else {
                logger.error('pipeToNewRequest: unexpected error piping the request');
                invokeCallbackIfNeeded({ error: 'aborted' });
            }
        })
        .on('error', () => {
            logger.error('pipeToNewRequest: client closed connection');
            outgoingRes.end();
        });

    return outgoingReq;
}

const validWebUriRegexp = new RegExp(
    '^' +
        // protocol identifier
        '(?:(?:https?|ftp)://)' +
        // user:pass authentication
        '(?:\\S+(?::\\S*)?@)?' +
        '(?:' +
        // IP address exclusion
        // private & local networks
        '(?!(?:10|127)(?:\\.\\d{1,3}){3})' +
        '(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})' +
        '(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})' +
        // IP address dotted notation octets
        // excludes loopback network 0.0.0.0
        // excludes reserved space >= *********
        // excludes network & broacast addresses
        // (first & last IP address of each class)
        '(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])' +
        '(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}' +
        '(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))' +
        '|' +
        // host name
        '(?:(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)' +
        // domain name
        '(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*' +
        // TLD identifier
        '(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))' +
        // TLD may end with dot
        '\\.?' +
        ')' +
        // port number
        '(?::\\d{2,5})?' +
        // resource path
        '(?:[/?#]\\S*)?' +
        '$',
    'i'
);
export function isWebUri(url: string) {
    return validWebUriRegexp.test(url);
}

export function queryStringToJSON(queryString: string) {
    const pairs = queryString.slice(1).split('&');

    let result: Record<string, string> = {};
    pairs.forEach(function (pair: string) {
        const pairTuple = pair.split('=');
        result[pairTuple[0]] = decodeURIComponent(pair[1] || '');
    });

    return JSON.parse(JSON.stringify(result));
}

export function removePrefix(str: string, prefix: string) {
    let i = str.indexOf(prefix);
    if (i == 0) {
        return str.substring(prefix.length);
    } else {
        return str;
    }
}

export function isImage(mimeType: string) {
    return mimeType == 'image/jpeg' || mimeType == 'image/png' || mimeType == 'image/gif';
}

// returns an sqlite buffer
// TODO: remove the any type once types are available in BalsamiqArchive
export function object2sqliteBuffer(config: Config, obj: unknown, callback: BASLegacyCallback<{ buffer: Buffer }>) {
    let sqlDriverInstance = new SQLiteAdapter({
        archivesPath: config.archivesPath,
    });
    let bar = new barMod.BalsamiqArchive(sqlDriverInstance);
    let tempID = uuid.v1();
    bar.createFromDump(tempID, obj, function (obj: any) {
        if (obj.error) {
            callback(obj);
        } else {
            bar.getBuffer(tempID, function (obj: any) {
                if (obj.error) {
                    callback(obj);
                } else {
                    const buffer = obj.buffer;
                    bar.destroy(tempID, function (obj: any) {
                        if (obj.error) {
                            callback(obj);
                        } else {
                            callback({ buffer: buffer });
                        }
                    });
                }
            });
        }
    });
}

// TODO: remove the any type once types are available in BalsamiqArchive
export function sqliteBuffer2object(config: Config, buffer: Buffer, kind: string, callback: BASLegacyCallback<{ dump: unknown }>) {
    var tempID = uuid.v1();
    var sqlDriverInstance = new SQLiteAdapter({
        archivesPath: config.archivesPath,
    });
    var bar = new barMod.BalsamiqArchive(sqlDriverInstance);
    bar.createFromBuffer(tempID, buffer, function (obj: any) {
        let migrated: any;
        let ret: any;
        if (obj.error) {
            callback(obj);
        } else {
            migrated = obj.migrated;
            bar.dump(Consts.Branch.AllBranches, function (obj: any) {
                if (obj.error) {
                    callback(obj);
                } else {
                    var dump = obj.dump;
                    var resourceIDs = [];
                    var i;

                    dump.migrated = migrated;

                    if (kind === 'cloud' || kind === 'wd') {
                        // strip the thumbnail image for Cloud and Webdemo
                        stripThumbnailImageFromDump(dump);
                    }

                    for (i = 0; i < dump.Resources.length; i++) {
                        resourceIDs.push(dump.Resources[i].ID);
                    }
                    bar.getResourcesData(resourceIDs, Consts.Branch.AllBranches, function (obj: any) {
                        if (obj.error) {
                            // try to delete the temp file in any case
                            ret = obj;
                            bar.destroy(tempID, function (obj: any) {
                                if (obj.error) {
                                    ret.error += ' additional error on destroying ' + obj.error;
                                }
                                callback(ret);
                            });
                        } else {
                            for (i = 0; i < dump.Resources.length; i++) {
                                dump.Resources[i].DATA = obj.data[dump.Resources[i].ID + '|' + dump.Resources[i].BRANCHID];
                            }

                            bar.destroy(tempID, function (obj: any) {
                                if (obj.error) {
                                    callback(obj);
                                } else {
                                    callback({ dump: dump });
                                }
                            });
                        }
                    });
                }
            });
        }
    });
}

// TODO: remove the any type once types are available in BalsamiqArchive
export function stripThumbnailImageFromDump(dump: any) {
    const thumbnails = dump.Thumbnails || [];
    for (let thumbnail of thumbnails) {
        let attributes = (thumbnail && thumbnail.ATTRIBUTES) || {};
        delete attributes.image;
    }
}

export async function decodeJWT({
    connectorId,
    dbConnector,
    platformToken,
    noVerify,
    ignoreExpiredToken,
    secretsCache,
}: {
    connectorId: string;
    dbConnector: DBConnector;
    platformToken: string;
    noVerify?: boolean;
    ignoreExpiredToken?: boolean;
    secretsCache?: Record<string, string>;
}) {
    let claims: { exp?: number; iss?: string } = {};
    claims = jwt.decode(platformToken, '', true);

    if (noVerify) {
        return claims;
    }

    if (!claims.exp) {
        throw new Error('Missing exp claim in JWT token');
    }

    if (!claims.iss) {
        throw new Error('Missing iss claim in JWT token');
    }

    let secondsValidityLeft = claims.exp - new Date().getTime() / 1000;
    if (secondsValidityLeft < 0) {
        if (!ignoreExpiredToken) {
            throw new Error('PlatformToken expired (' + -Math.round(secondsValidityLeft) + ' seconds ago)');
        }
    }

    const secretKey = `${connectorId}::${claims.iss}`;
    let sharedSecret;
    if (secretsCache && secretsCache[secretKey]) {
        sharedSecret = secretsCache[secretKey];
    } else {
        const connectorData = await callWithLegacyCallback<Object>((cb) => dbConnector.getConnectorData(connectorId, `${claims.iss}`, cb));
        if (!('sharedSecret' in connectorData) || !connectorData.sharedSecret) {
            throw new Error(`No "sharedSecret" key found in the ${connectorId} connectorData for ISS ${claims.iss}`);
        }
        if (typeof connectorData.sharedSecret !== 'string') {
            throw new Error(`"sharedSecret" key in the ${connectorId} connectorData for ISS ${claims.iss} is not a string`);
        }
        sharedSecret = connectorData.sharedSecret;
        if (secretsCache) {
            secretsCache[secretKey] = sharedSecret;
        }
    }

    return { claims: jwt.decode(platformToken, sharedSecret, false) };
}
