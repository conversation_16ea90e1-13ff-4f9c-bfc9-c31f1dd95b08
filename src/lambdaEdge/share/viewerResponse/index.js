// =============================================================================
// IMPORTANT: DO _NOT_ EDIT THIS FILE IN AWS CONSOLE!
// =============================================================================

exports.handler = async (event, context) => {
    const response = event.Records[0].cf.response;
    const headers = response.headers;
    const x_amz_meta_pattern = /^x-amz-meta-/;


    Object.keys(headers).forEach(function(key){
        if (key.match(x_amz_meta_pattern)) {
            delete event.Records[0].cf.response.headers[key];
        }
    });


    return response;
};
