/** @prettier */
// =============================================================================
// IMPORTANT: DO _NOT_ EDIT THIS FILE IN AWS CONSOLE!
// =============================================================================

const fs = require('fs');
const page404Body = fs.readFileSync('./404.html', 'utf8');

const permalinksPattern = /^\/permalinks\//;
const snapshotsPattern = /^\/snapshots\//;

exports.handler = async (event) => {
    const response = event.Records[0].cf.response;
    const request = event.Records[0].cf.request;

    const isPermalinkResponse = request.uri.match(permalinksPattern);
    const isSnapshotResponse = request.uri.match(snapshotsPattern);

    if (response.status >= 400 && response.status <= 499 && (isPermalinkResponse || isSnapshotResponse)) {
        response.status = 404;
        response.statusDescription = 'Not Found';
        response.body = page404Body;
        response.headers['content-type'] = [
            {
                key: 'Content-Type',
                value: 'text/html; charset=utf-8',
            },
        ];
        response.headers['content-encoding'] = [
            {
                key: 'Content-Encoding',
                value: 'UTF-8',
            },
        ];
    } else {
        response.headers['access-control-allow-origin'] = [
            {
                key: 'Access-Control-Allow-Origin',
                value: '*',
            },
        ];
        response.headers['access-control-allow-methods'] = [
            {
                key: 'Access-Control-Allow-Methods',
                value: 'GET, OPTIONS',
            },
        ];
        response.headers['access-control-allow-headers'] = [
            {
                key: 'Access-Control-Allow-Headers',
                value: 'Content-Type',
            },
        ];
    }

    return response;
};
