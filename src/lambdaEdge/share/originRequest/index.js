// =============================================================================
// IMPORTANT: DO _NOT_ EDIT THIS FILE IN AWS CONSOLE!
// =============================================================================

const PLATFORM_TO_S3DIR = {
    '/c/': '/cloud/',
    '/g/': '/gd/',
    '/j/': '/jira/',
    '/a/': '/confluence/',
};

const PLATFORM_SNAPSHOT_TO_S3DIR = {
     '/slack/': '/slack/',
};


const queryPattern = /(query=|q=)/;
const platformPattern = /^\/[a-z]\//;
const platformSnapshotPattern = /^\/[a-z]+\//;

exports.handler = async (event) => {
    const request = event.Records[0].cf.request;

    const platformMatch = request.uri.match(platformPattern);
    const platform = platformMatch !== null ? platformMatch[0] : null;

    const platformSnapshotMatch = request.uri.match(platformSnapshotPattern);
    const platformSnapshot = platformSnapshotMatch !== null ? platformSnapshotMatch[0] : null;

    if (platform !== null && platform in PLATFORM_TO_S3DIR && !queryPattern.test(request.querystring)) {
        const platformDir = PLATFORM_TO_S3DIR[platform];
        request.uri = request.uri.replace(platformMatch, '/permalinks' + platformDir);
    } else if (platformSnapshot !== null && platformSnapshot in PLATFORM_SNAPSHOT_TO_S3DIR) {
        const platformDir = PLATFORM_SNAPSHOT_TO_S3DIR[platformSnapshot];
        request.uri = request.uri.replace(platformSnapshotMatch, '/snapshots' + platformDir);
    } else {
        request.headers.host = [{key: 'Host', value: '__PLACEHOLDER__DOMAIN_NAME__'}];
        request.origin = {
            custom: {
                customHeaders: {},
                domainName: '__PLACEHOLDER__DOMAIN_NAME__',
                keepaliveTimeout: 5,
                path: '',
                port: 443,
                protocol: "https",
                readTimeout: 30,
                sslProtocols: [
                    "TLSv1",
                    "TLSv1.1",
                    "TLSv1.2"
                ]
            }
        };
    }
    return request;
};
