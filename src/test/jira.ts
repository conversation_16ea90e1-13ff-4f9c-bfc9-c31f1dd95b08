/** @prettier */
/* global testContext */
/* eslint-env mocha */

import { assert, expect } from 'chai';
import { BASApiClient } from './utils/apiclient.ts';
import type { Application } from 'express';
import jwt from 'atlassian-jwt';
import { JIRAConnector } from '../connectors/jira.js';
import { defineCommonOfflineTests, defineCommonOnlineTests } from './shared-tests.ts';
import * as jwtLib from '../connectors/lib/jwt.js';
import nock from 'nock';

suite('JIRA', function () {
    const kind = 'jira';
    const userInfo = {
        userId: '557058:18e32678-32c8-4d4b-b838-55f0ee6f9597',
        name: 'jira_user',
        displayName: '', // Empty to skip JIRA user validation in tests
        avatarURL: '', // Empty to skip JIRA user validation in tests
    };

    // Test installation data for JIRA connector
    const installationData = {
        key: 'com.balsamiq.mockups.jira.staging',
        clientKey: 'jira-test-client-key-12345',
        oauthClientId: 'test-oauth-client-id',
        publicKey:
            'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAorAIfPTE8mqeR1cMEkHlUsKPGpxoJMTq0+GGeZSVBC1glxbX1Jg/dsA6Rw6tFa1P/vyDp6NKuLomjmxjSeqpNadRYjY96KTa7NJTgPmS0bwmUp9c+ThwmVokbSWAYmEFPhwjbEHRzjUPubu3ihhuvj/IXsK+dl2/pJehZEAHZ9CunutbFVgp+buonc05CE325R6Ez1OG+Dvk0nMY4zCV5qP4Io/85r4q/hTYJBdjJ9R4jZcfDH7G5m6+itGuFZ1+5MR8AqD8XfVOqGieTOugZdsJ9TkTg5lWtXuI9LUhk9bt2KdnsMM4uL+460R1UEGi2GJ8D4nPe3cIygy7WOo3RwIDAQAB',
        sharedSecret: 'test-shared-secret-for-jira-connector-testing',
        serverVersion: '8.20.0',
        pluginsVersion: '1000.0.0.test',
        baseUrl: 'https://test-jira-instance.atlassian.net',
        productType: 'jira',
        description: 'Test JIRA instance for BAS testing',
        eventType: 'installed',
        displayUrl: 'https://test-jira-instance.atlassian.net',
    };

    const issueId = 'TEST-123';
    const archiveID = 'jira_test_archive';
    const siteID = `${installationData.clientKey}_${issueId}`;

    const getPlatformToken = (extraParams?: any) => {
        const connector = testContext.getConnector(kind);
        assert(connector instanceof JIRAConnector, 'Expected JIRAConnector');

        // Create a JWT token for JIRA API calls with proper QSH and sub claim
        const now = Math.round(Date.now() / 1000);
        const method = 'GET';
        const path = '/jira/list';
        const req = jwt.fromMethodAndUrl(method, path);

        const tokenData = {
            iss: installationData.clientKey,
            iat: now,
            exp: now + 180,
            qsh: jwt.createQueryStringHash(req),
            sub: userInfo.name, // Use userInfo.name directly, not the full URN
            context: {
                user: {
                    userKey: userInfo.name,
                    userAccountId: userInfo.userId,
                },
            },
            ...extraParams,
        };
        return jwt.encodeSymmetric(tokenData, installationData.sharedSecret, jwt.SymmetricAlgorithm.HS256);
    };

    let app: Application;
    let bas: BASApiClient;

    setup(async function () {
        app = testContext.app;
        bas = new BASApiClient(app, {
            kind,
            userInfo,
            platformArchiveID: archiveID,
            siteID,
            getPlatformToken,
            adminUser: testContext.basAdminUser,
            adminSecret: testContext.basAdminSecret,
            platformInfo: {
                issueID: issueId,
                iss: installationData.clientKey,
            },
        });
    });

    suite('Connector Installation Lifecycle', function () {
        test('Install connector', async function () {
            const res = await bas.postCustomAPI({
                kind: 'jira',
                api: 'installed',
                data: installationData,
            });
            expect(res.status).to.equal(200);
            expect(res.body).to.not.have.property('error');
        });

        test('Enable connector', async function () {
            // First install the connector
            await bas.postCustomAPI({
                kind: 'jira',
                api: 'installed',
                data: installationData,
            });

            const res = await bas.postCustomAPI({
                kind: 'jira',
                api: 'enabled',
                data: { clientKey: installationData.clientKey },
            });
            expect(res.status).to.equal(200);
        });

        test('Disable connector', async function () {
            // First install the connector
            await bas.postCustomAPI({
                kind: 'jira',
                api: 'installed',
                data: installationData,
            });

            const res = await bas.postCustomAPI({
                kind: 'jira',
                api: 'disabled',
                data: { clientKey: installationData.clientKey },
            });
            expect(res.status).to.equal(200);
        });

        test('Uninstall connector', async function () {
            // First install the connector
            await bas.postCustomAPI({
                kind: 'jira',
                api: 'installed',
                data: installationData,
            });

            const res = await bas.postCustomAPI({
                kind: 'jira',
                api: 'uninstalled',
                data: { clientKey: installationData.clientKey },
            });
            expect(res.status).to.equal(200);
        });
    });

    suite('JWT Authentication', function () {
        setup(async function () {
            // Install connector before each JWT test
            await bas.postCustomAPI({
                kind: 'jira',
                api: 'installed',
                data: installationData,
            });
        });

        test('Valid JWT token should be accepted', async function () {
            const token = getPlatformToken();
            const res = await bas.getCustomAPI({
                kind: 'jira',
                api: 'list',
                params: {
                    issueID: issueId,
                    jwt: token,
                },
            });
            expect(res.status).to.not.equal(401);
        });

        test('Invalid JWT token should be rejected', async function () {
            const res = await bas.getCustomAPI({
                kind: 'jira',
                api: 'list',
                params: {
                    issueID: issueId,
                    jwt: 'invalid-jwt-token',
                },
            });
            // JIRA connector returns empty object for invalid JWT, not error object
            expect(res.body).to.be.an('object');
        });

        test('Missing JWT token should be rejected', async function () {
            const res = await bas.getCustomAPI({
                kind: 'jira',
                api: 'list',
                params: {
                    issueID: issueId,
                },
            });
            // JIRA connector returns empty object for missing JWT, not error object
            expect(res.body).to.be.an('object');
        });

        test('Expired JWT token should be rejected', async function () {
            const now = Math.round(Date.now() / 1000);
            const expiredToken = getPlatformToken({
                iat: now - 3600, // 1 hour ago
                exp: now - 1800, // 30 minutes ago (expired)
            });

            const res = await bas.getCustomAPI({
                kind: 'jira',
                api: 'list',
                params: {
                    issueID: issueId,
                    jwt: expiredToken,
                },
            });
            // JIRA connector returns empty object for expired JWT, not error object
            expect(res.body).to.be.an('object');
        });
    });

    suite('JIRA API Endpoints', function () {
        setup(async function () {
            // Install connector and setup mocks
            await bas.postCustomAPI({
                kind: 'jira',
                api: 'installed',
                data: installationData,
            });

            // Mock JIRA API responses
            nock(installationData.baseUrl)
                .persist()
                .get(/\/rest\/api\/latest\/issue\/.*\/attachments/)
                .reply(200, [])
                .get(/\/rest\/api\/latest\/attachment\/.*/)
                .reply(200, { filename: 'test.bmpr', size: 1024 })
                .post(/\/rest\/api\/latest\/issue\/.*\/attachments/)
                .reply(200, [{ id: 'attachment-123', filename: 'test.bmpr' }])
                .delete(/\/rest\/api\/latest\/attachment\/.*/)
                .reply(204);
        });

        teardown(function () {
            nock.cleanAll();
        });

        test('List attachments for issue', async function () {
            const token = getPlatformToken();
            const res = await bas.getCustomAPI({
                kind: 'jira',
                api: 'list',
                params: {
                    issueID: issueId,
                    jwt: token,
                },
            });
            expect(res.status).to.equal(200);
            expect(res.body).to.be.an('object');
        });

        test('Get license information', async function () {
            const token = getPlatformToken();
            const res = await bas.getCustomAPI({
                kind: 'jira',
                api: 'license',
                params: {
                    jwt: token,
                },
            });
            expect(res.status).to.equal(200);
        });

        test('Migration endpoint', async function () {
            const token = getPlatformToken();
            const res = await bas.getCustomAPI({
                kind: 'jira',
                api: 'migrate',
                params: {
                    issueID: issueId,
                    jwt: token,
                },
            });
            expect(res.status).to.equal(200);
        });
    });

    suite('Archive Operations', function () {
        setup(async function () {
            // Install connector
            await bas.postCustomAPI({
                kind: 'jira',
                api: 'installed',
                data: installationData,
            });

            // Mock OAuth2 token endpoint for JIRA
            nock('https://oauth-2-authorization-server.services.atlassian.com').persist().post('/oauth2/token').reply(200, {
                access_token: 'mock-access-token',
                token_type: 'Bearer',
                expires_in: 3600,
            });

            // Mock JIRA attachment API calls
            nock(installationData.baseUrl)
                .persist()
                .post(/\/rest\/api\/latest\/issue\/.*\/attachments/)
                .reply(200, [{ id: 'attachment-123', filename: 'test.bmpr' }])
                .get(/\/rest\/api\/latest\/issue\/.*\/attachments/)
                .reply(200, [])
                .get(/\/rest\/api\/latest\/issue\/.*/)
                .query(true) // Accept any query parameters (like ?fields=*all)
                .reply(200, {
                    id: issueId,
                    key: issueId,
                    fields: {
                        summary: 'Test Issue',
                        description: 'Test issue for BAS testing',
                        project: { key: 'TEST', name: 'Test Project' },
                    },
                })
                .get(/\/rest\/api\/latest\/mypermissions/)
                .query(true) // Accept any query parameters
                .reply(200, {
                    permissions: {
                        EDIT_ISSUES: { havePermission: true },
                        CREATE_ATTACHMENTS: { havePermission: true },
                        DELETE_OWN_ATTACHMENTS: { havePermission: true },
                        BROWSE_PROJECTS: { havePermission: true },
                    },
                });
        });

        teardown(function () {
            nock.cleanAll();
        });

        test('Create archive', async function () {
            const createRes = await bas.create();
            expect(createRes.body).to.have.property('platformArchiveID', archiveID);
        });
    });

    suite('Offline Methods', function () {
        setup(async function () {
            // Install connector and create archive
            await bas.postCustomAPI({
                kind: 'jira',
                api: 'installed',
                data: installationData,
            });

            // Mock OAuth2 token endpoint for JIRA
            nock('https://oauth-2-authorization-server.services.atlassian.com').persist().post('/oauth2/token').reply(200, {
                access_token: 'mock-access-token',
                token_type: 'Bearer',
                expires_in: 3600,
            });

            // Mock JIRA attachment API calls
            nock(installationData.baseUrl)
                .persist()
                .post(/\/rest\/api\/latest\/issue\/.*\/attachments/)
                .reply(200, [{ id: 'attachment-123', filename: 'test.bmpr' }])
                .get(/\/rest\/api\/latest\/issue\/.*\/attachments/)
                .reply(200, [])
                .get(/\/rest\/api\/latest\/issue\/.*/)
                .query(true) // Accept any query parameters (like ?fields=*all)
                .reply(200, {
                    id: issueId,
                    key: issueId,
                    fields: {
                        summary: 'Test Issue',
                        description: 'Test issue for BAS testing',
                        project: { key: 'TEST', name: 'Test Project' },
                    },
                })
                .get(/\/rest\/api\/latest\/mypermissions/)
                .query(true) // Accept any query parameters
                .reply(200, {
                    permissions: {
                        EDIT_ISSUES: { havePermission: true },
                        CREATE_ATTACHMENTS: { havePermission: true },
                        DELETE_OWN_ATTACHMENTS: { havePermission: true },
                        BROWSE_PROJECTS: { havePermission: true },
                    },
                });

            const createRes = await bas.create();
            expect(createRes.body).to.have.property('platformArchiveID', archiveID);
        });

        teardown(function () {
            nock.cleanAll();
        });

        defineCommonOfflineTests(() => ({ bas, archiveId: archiveID, siteId: siteID, kind }));
    });

    suite('Online Methods', function () {
        let token: string;
        let userID: string;
        let dump: BmprDump;
        let basArchiveID: string;

        setup(async function () {
            // Install connector and create archive
            await bas.postCustomAPI({
                kind: 'jira',
                api: 'installed',
                data: installationData,
            });

            // Mock OAuth2 token endpoint for JIRA
            nock('https://oauth-2-authorization-server.services.atlassian.com').persist().post('/oauth2/token').reply(200, {
                access_token: 'mock-access-token',
                token_type: 'Bearer',
                expires_in: 3600,
            });

            // Mock JIRA attachment API calls
            nock(installationData.baseUrl)
                .persist()
                .post(/\/rest\/api\/latest\/issue\/.*\/attachments/)
                .reply(200, [{ id: 'attachment-123', filename: 'test.bmpr' }])
                .get(/\/rest\/api\/latest\/issue\/.*\/attachments/)
                .reply(200, [
                    {
                        id: 'attachment-123',
                        filename: `${archiveID}.bmpr`,
                        size: 1024,
                        created: new Date().toISOString(),
                        author: { accountId: userInfo.userId, displayName: userInfo.name },
                    },
                ])
                .get(/\/rest\/api\/latest\/attachment\/.*/)
                .reply(200, { id: 'attachment-123', filename: `${archiveID}.bmpr`, size: 1024 })
                .get(/\/secure\/attachment\/.*/)
                .reply(200, Buffer.from('mock bmpr content'))
                .get(/\/rest\/api\/latest\/issue\/.*/)
                .query(true) // Accept any query parameters (like ?fields=*all)
                .reply(200, {
                    id: issueId,
                    key: issueId,
                    fields: {
                        summary: 'Test Issue',
                        description: 'Test issue for BAS testing',
                        project: { key: 'TEST', name: 'Test Project' },
                    },
                })
                .get(/\/rest\/api\/latest\/mypermissions/)
                .query(true) // Accept any query parameters
                .reply(200, {
                    permissions: {
                        EDIT_ISSUES: { havePermission: true },
                        CREATE_ATTACHMENTS: { havePermission: true },
                        DELETE_OWN_ATTACHMENTS: { havePermission: true },
                        BROWSE_PROJECTS: { havePermission: true },
                    },
                });

            const createRes = await bas.create();
            expect(createRes.body).to.have.property('platformArchiveID', archiveID);

            const openRes = await bas.open();
            ({ token, userID, dump, basArchiveID } = openRes.body);
            expect(token).not.to.be.undefined;
            expect(userID).not.to.be.undefined;
            expect(dump).not.to.be.undefined;

            // Set presence for RTC
            testContext.mockBackendRTCClient.setReturnHerenowUuids([userID]);
        });

        teardown(function () {
            nock.cleanAll();
        });

        defineCommonOnlineTests(() => ({
            bas,
            token,
            userInfo,
            userID,
            dump,
            basArchiveID,
            projectId: archiveID,
            siteId: siteID,
            kind,
        }));

        test('Save archive to JIRA', async function () {
            // Mock JIRA attachment upload
            nock(installationData.baseUrl)
                .post(/\/rest\/api\/latest\/issue\/.*\/attachments/)
                .reply(200, [{ id: 'attachment-123', filename: 'test.bmpr' }]);

            const flushRes = await bas.flush({ token, force: true });
            expect(flushRes.body).to.not.have.property('error');

            nock.cleanAll();
        });

        test('Project Exists on Platform', async function () {
            // First save the archive to JIRA to populate PLATFORM_INFO
            nock(installationData.baseUrl)
                .post(/\/rest\/api\/latest\/issue\/.*\/attachments/)
                .reply(200, [{ id: 'attachment-123', filename: 'test.bmpr' }]);

            await bas.flush({ token, force: true });

            // Mock the attachment metadata endpoint for projectExistsOnPlatform
            nock(installationData.baseUrl)
                .get(/\/rest\/api\/latest\/attachment\/.*/)
                .reply(200, { id: 'attachment-123', filename: 'test.bmpr', size: 1024 });

            const projectExistsRes = await bas.projectExistsOnPlatform({
                platformArchiveID: archiveID,
                platformSiteID: siteID,
                kind: kind,
            });
            expect(projectExistsRes.body).to.have.property('status', true);

            nock.cleanAll();
        });
    });
});
