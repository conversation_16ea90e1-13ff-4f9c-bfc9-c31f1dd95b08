/** @prettier */
/* global testContext */
/* eslint-env mocha */
import assert from 'assert';
import { expect } from 'chai';
import type { Application } from 'express';
import nock from 'nock';
import path from 'path';
import request from 'supertest';
import { fileURLToPath } from 'url';
import * as uuid from 'uuid';
import type { DataResidencyName } from '../environment-variables-schemas.ts';
import { BASApiClient } from './utils/apiclient.ts';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

suite('Other APIs', function () {
    let app: Application;

    setup(async function () {
        app = testContext.app;
    });

    test('/crossdomain.xml', async function () {
        const res = await request(app).get('/crossdomain.xml').set('Referer', 'https://www.google.com');
        expect(res.status).to.equal(200);
        expect(res.text).to.include('<allow-access-from domain="www.google.com"');
    });
});

export async function createPermalinkOnFirstResource({
    kind,
    dump,
    token,
    dataResidency,
    bas,
}: {
    kind: string;
    dump: BmprDump;
    token: string;
    dataResidency: DataResidencyName;
    bas: BASApiClient;
}) {
    const resource = dump.Resources[0];
    const { ID: resourceID, BRANCHID: branchID } = resource;

    let setPermalinkRes = await bas.createOrUpdateImageLink({
        token,
        resourceID,
        branchID,
        permalinkInfo: {},
        imagePath: path.join(__dirname, 'image_640x480.png'),
    });

    const { permalinkImageStorageAdapter } = testContext;
    const permalinkBucket = 'us-permalink-bucket';
    const permalinkKeyInBucket = permalinkImageStorageAdapter._getPermalinkKey({
        permalinkID: setPermalinkRes.body.permalinkID,
        platformKind: kind,
        dataResidency,
    });

    expect(testContext.s3Mock.buckets).to.have.property(permalinkBucket);
    expect(testContext.s3Mock.buckets[permalinkBucket]).to.have.property(permalinkKeyInBucket);
    return setPermalinkRes;
}

export function defineCommonOfflineTests(
    getTestContext: () => {
        bas: BASApiClient;
        archiveId: string;
        siteId: string;
        kind: string;
    }
) {
    suite('Common', function () {
        let bas: BASApiClient;
        let archiveId: string;
        let siteId: string;
        let kind: string;

        setup(async function () {
            const testContext = getTestContext();
            bas = testContext.bas;
            archiveId = testContext.archiveId;
            siteId = testContext.siteId;
            kind = testContext.kind;
        });

        test('Open', async function () {
            let openRes = await bas.open();
            const { token, userID, dump, heuristicArchiveSize } = openRes.body;
            expect(token).not.to.be.undefined;
            expect(userID).not.to.be.undefined;
            expect(dump).not.to.be.undefined;
            expect(heuristicArchiveSize).not.to.be.undefined;
        });

        test('Delete Offline', async function () {
            let deleteOfflineRes = await bas.deleteOffline({
                platformArchiveID: archiveId,
                platformSiteID: siteId,
                platformKind: kind,
            });
            expect(deleteOfflineRes.body).to.have.property('message', 'success');
        });

        test('Flush Offline', async function () {
            let flushOfflineRes = await bas.flushOffline({
                platformArchiveID: archiveId,
                platformSiteID: siteId,
            });
            expect(flushOfflineRes.body).to.not.have.key('error');
        });

        test('Unload Archive', async function () {
            let unloadArchiveRes = await bas.unloadArchive({
                platformArchiveID: archiveId,
                platformSiteID: siteId,
                kind: kind,
            });
            expect(unloadArchiveRes.body).to.not.have.key('error');
        });

        test('Unload Archive Offline', async function () {
            let unloadArchiveOfflineRes = await bas.unloadArchiveOffline({
                platformArchiveID: archiveId,
                platformSiteID: siteId,
            });
            expect(unloadArchiveOfflineRes.body).to.not.have.key('error');
        });

        test('Unload Archive if not in Sync', async function () {
            let unloadArchiveIfNotSyncRes = await bas.unloadArchiveIfNotSync({
                platformArchiveID: archiveId,
                platformSiteID: siteId,
                kind: kind,
                modifiedTimestamp: Date.now(),
            });
            expect(unloadArchiveIfNotSyncRes.body).to.not.have.key('error');
        });

        test('Get Users Info and Settings', async function () {
            let getUsersInfoAndSettingsRes = await bas.getUsersInfoAndSettings({ userId: 123 });
            // JIRA connector implements this method but requires server API credentials
            // Other connectors return "not implemented"
            if (kind === 'jira') {
                expect(getUsersInfoAndSettingsRes.body.error).to.equal('invalid credentials');
            } else {
                expect(getUsersInfoAndSettingsRes.body.error).to.equal('not implemented');
            }
        });
    });
}

export function defineCommonOnlineTests(
    getTestContext: () => {
        bas: BASApiClient;
        token: string;
        userInfo: { name: string; displayName: string; avatarURL: string; userId: string };
        userID: string;
        dump: any;
        basArchiveID: string;
        projectId: string;
        siteId: string;
        kind: string;
    }
) {
    suite('Common', function () {
        let bas: BASApiClient;
        let token: string;
        let userInfo: { name: string; displayName: string; avatarURL: string; userId: string };
        let userID: string;
        let dump: any;
        let basArchiveID: string;
        let projectId: string;
        let siteId: string;
        let kind: string;

        setup(async function () {
            const testContext = getTestContext();
            bas = testContext.bas;
            token = testContext.token;
            userInfo = testContext.userInfo;
            userID = testContext.userID;
            dump = testContext.dump;
            basArchiveID = testContext.basArchiveID;
            projectId = testContext.projectId;
            siteId = testContext.siteId;
            kind = testContext.kind;
        });

        test('getTOC', async function () {
            const getTOCRes = await bas.getTOC({ token, branchID: 'Master' });
            expect(getTOCRes.body.dump).to.not.be.undefined;
        });

        test('delete api', async function () {
            const deleteRes = await bas.delete({ token });
            expect(deleteRes.body).to.deep.eq({});

            expect(testContext.mockBackendRTCClient.published.length).to.be.gt(0);
            let lastMessagePublishedIndex = testContext.mockBackendRTCClient.published.length - 1;
            expect(testContext.mockBackendRTCClient.published[lastMessagePublishedIndex].payload).to.have.property(
                'operation',
                'archiveDeletedFromPlatform'
            );
        });

        test('get and set branch attributes', async function () {
            const branchID = 'Master';
            const getBranchAttributesRes = await bas.getBranchAttributes({ token, branchID });
            expect(getBranchAttributesRes.body).to.have.property('attributes');

            const newAttributes = {
                ...getBranchAttributesRes.body,
                branchDescription: 'new description',
            };

            const setBranchAttributesRes = await bas.setBranchAttributes({ token, branchID, attributes: newAttributes });
            expect(setBranchAttributesRes.body).to.have.property('archiveRevision');

            const getBranchAttributesRes2 = await bas.getBranchAttributes({ token, branchID });
            expect(getBranchAttributesRes2.body.attributes).to.have.property('branchDescription', 'new description');
        });

        test('Create / Update / Delete Thumbnail', async function () {
            const thumbnailID = uuid.v1();
            const branchID = dump.Resources[0].BRANCHID;
            const resourceID = dump.Resources[0].ID;
            let archiveRevision = dump.Info.ArchiveRevision;

            const imageData =
                'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABAQMAAAAl21bKAAAABlBMVEXMzMyWlpYU2uzLAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAACklEQVQImWNgAAAAAgAB9HFkpgAAAABJRU5ErkJggg==';
            const attributes = { branchID: branchID, resourceID: resourceID, image: imageData };

            const createThumbnailRes = await bas.createThumbnail({ token, thumbnailID, attributes });
            expect(createThumbnailRes.body).to.have.property('ID');

            const getThumbnailRes = await bas.getThumbnail({ token, thumbnailID });
            expect(getThumbnailRes.body).to.have.property('ATTRIBUTES');
            expect(getThumbnailRes.body.ATTRIBUTES).to.have.property('image', imageData);

            const newAttributes = { ...attributes, newAttributes: 1 };
            const setThumbnailRes = await bas.setThumbnail({ token, thumbnailID, attributes: newAttributes, archiveRevision });
            expect(setThumbnailRes.body).to.have.key('archiveRevision');

            const getThumbnailRes2 = await bas.getThumbnail({ token, thumbnailID });
            expect(getThumbnailRes2.body).to.have.property('ATTRIBUTES');
            expect(getThumbnailRes2.body.ATTRIBUTES).to.deep.eq(newAttributes);

            const deleteThumbnailsRes = await bas.deleteThumbnails({ token, thumbnailIDs: [thumbnailID] });
            expect(deleteThumbnailsRes.body).to.have.key('archiveRevision');

            // Try to get deleted thumbnail
            const getThumbnailRes3 = await bas.getThumbnail({ token, thumbnailID });
            expect(getThumbnailRes3.body).to.have.property('error');
        });

        test('refreshSession', async function () {
            const refreshSessionRes = await bas.refreshSession({ token });
            expect(refreshSessionRes.body).to.have.property('success', 'ok');
        });

        test('Log user event', async function () {
            let logUserEventRes = await bas.logUserEvent({ token, userEvent: { some: 'event' } });
            expect(logUserEventRes).to.not.have.key('error');
        });

        test('Get Heuristic Archive Size', async function () {
            let archiveSizeRes = await bas.getHeuristicArchiveSize({ token });
            expect(archiveSizeRes.body).to.have.property('heuristicArchiveSize');
            expect(archiveSizeRes.body).to.have.property('archiveRevision');
        });

        test('Get project members', async function () {
            let projectMembersRes = await bas.getProjectMembers({ token, data: {} });
            expect(projectMembersRes).to.not.have.key('error');
        });

        test('Broadcast Message', async function () {
            let broadcastMessageRes = await bas.broadcastMessage({ token, message: { text: 'anymessage' } });
            expect(broadcastMessageRes.body).to.have.property('ignore', true);
            expect(broadcastMessageRes.body).to.have.property('text', 'anymessage');

            expect(testContext.mockBackendRTCClient.published.length).to.be.gt(0);
            let lastMessagePublishedIndex = testContext.mockBackendRTCClient.published.length - 1;
            const payload = testContext.mockBackendRTCClient.published[lastMessagePublishedIndex].payload;
            expect('message' in payload && payload.message).to.have.property('text', 'anymessage');
        });

        test('Create/Fetch/Update User APIs', async function () {
            let createMyUserRes = await bas.createMyUser({ token, updatedUserInfo: userInfo });
            expect(createMyUserRes.body).to.have.key('archiveRevision');

            const lastItem = testContext.mockBackendRTCClient.published.pop();
            assert(lastItem, 'lastItem should not be undefined');
            assert('updatedUserInfo' in lastItem.payload, 'updatedUserInfo should be in payload');
            expect(lastItem.payload.updatedUserInfo).to.deep.equal(userInfo);

            let getArchiveUsersListRes = await bas.getArchiveUsersList({ token });
            expect(Array.isArray(getArchiveUsersListRes.body.users)).to.be.true;
            expect(getArchiveUsersListRes.body.users.length).to.be.gt(0);
            expect(getArchiveUsersListRes.body.users[0].ATTRIBUTES).to.deep.eq(userInfo);

            let updateMyUserRes = await bas.updateMyUser({ token, updatedUserInfo: { ...userInfo, displayName: 'another name' } });
            expect(updateMyUserRes.body).to.have.property('affectedRows', 1);

            testContext.mockBackendRTCClient.setReturnHerenowUuids([userID]);
            let usersListRes = await bas.getUsersList({ token });
            expect(Array.isArray(usersListRes.body.users)).to.be.true;
        });

        test('getResourcesData', async function () {
            let resourcesDataRes = await bas.getResourcesData({
                token,
                resources: dump.Resources,
                archiveRevision: dump.Info.ArchiveRevision,
            });
            expect(resourcesDataRes.error).to.be.false;
        });

        test('Comments APIs', async function () {
            // Create the user that'll own the comment
            let createMyUserRes = await bas.createMyUser({ token, updatedUserInfo: userInfo });
            const commentID = uuid.v4().toLocaleUpperCase();
            expect(createMyUserRes.body).to.have.key('archiveRevision');

            const lastItem = testContext.mockBackendRTCClient.published.pop();
            assert(lastItem, 'lastItem should not be undefined');
            assert('updatedUserInfo' in lastItem.payload, 'updatedUserInfo should be in payload');
            expect(lastItem.payload.updatedUserInfo).to.deep.equal(userInfo);

            const mockupResources = dump.Resources.filter((r: { ATTRIBUTES: Record<string, string> }) => r.ATTRIBUTES['kind'] === 'mockup');
            let mockupToComment = mockupResources[mockupResources.length - 1];
            let createCommentRes = await bas.createComment({
                token,
                commentID,
                resourceID: mockupToComment.ID,
                branchID: mockupToComment.BRANCHID,
                commentParentID: null,
                data: { text: 'some comment' },
            });
            expect(createCommentRes.body).to.have.property('commentID', commentID);
            expect(createCommentRes.body).to.have.property('resourceID', mockupToComment.ID);
            expect(createCommentRes.body).to.have.property('branchID', mockupToComment.BRANCHID);
            expect(createCommentRes.body.attributes).to.have.property('parentID', '');
            expect(createCommentRes.body.attributes).to.have.property('trashed', false);

            let getCommentsDataRes = await bas.getCommentsData({ token, commentIDs: [commentID] });
            expect(Array.isArray(getCommentsDataRes.body.comments)).to.be.true;
            expect(getCommentsDataRes.body.comments).to.be.lengthOf(1);
            expect(getCommentsDataRes.body.comments[0]).to.have.property('ID', commentID);
            expect(getCommentsDataRes.body.comments[0]).to.have.property('USERID', userInfo.name);

            let setCommentsDataRes = await bas.setCommentData({ token, commentID, data: { text: 'updated comment' } });
            expect(setCommentsDataRes.body).to.not.have.key('error');

            let updateCommentsAttributesRes = await bas.updateCommentsAttributes({
                token,
                commentIDs: [commentID],
                attributes: [{ setLikedStatus: true }],
            });
            expect(Array.isArray(updateCommentsAttributesRes.body.attributes)).to.be.true;
            expect(updateCommentsAttributesRes.body.attributes).to.be.lengthOf(1);
            expect(updateCommentsAttributesRes.body.attributes[0].commentID).to.eq(commentID);
            expect(updateCommentsAttributesRes.body.attributes[0].attributes.likedBy).to.contain(userInfo.name);

            let getCommentsDataRes2 = await bas.getCommentsData({ token, commentIDs: [commentID] });
            expect(getCommentsDataRes2.body.comments[0]).to.have.property('DATA', JSON.stringify({ text: 'updated comment' }));

            let deleteCommentsRes = await bas.deleteComments({ token, commentIDs: [commentID] });
            expect(deleteCommentsRes.body).to.not.have.key('error');

            let getCommentsDataRes3 = await bas.getCommentsData({ token, commentIDs: [commentID] });
            expect(getCommentsDataRes3.body.comments).to.be.empty;
        });

        test('getResourceDataWithCache uses ETag correctly', async function () {
            const branchID = 'Master';
            const resourceID = uuid.v4();
            const data = { mockup: { controls: {}, measuredH: '0', measuredW: '0', mockupH: '0', mockupW: '0', version: '1.0' } };
            const attributes = {
                name: 'New Wireframe 1',
                kind: 'mockup',
                trashed: false,
                importedFrom: '',
                creationDate: 0,
                modifiedBy: null,
                notes: null,
                order: 10776314.299002064,
            };

            const createResourceRes = await bas.createResource({
                token,
                branchID,
                resourceID,
                data: JSON.stringify(data),
                attributes,
            });
            expect(createResourceRes.body).to.have.property('ID', resourceID);

            // Get resource for the first time
            const getResourceDataRes = await bas.getResourceDataWithCache({ token, branchID, resourceID, basArchiveID });
            expect(getResourceDataRes.status).to.be.eq(200);

            // Response has the ETag header
            expect(getResourceDataRes.headers).to.have.property('etag');
            let etag = getResourceDataRes.headers['etag'];

            // Cache-Control header contains 'private' policy
            expect(getResourceDataRes.headers['cache-control']).to.match(/private/);

            // Receive Archive Revision
            const archiveRevision = parseInt(getResourceDataRes.headers['x-balsamiq-archive-revision'], 10);
            expect(archiveRevision).not.to.be.NaN;

            // set if-none-match and check response if 304
            bas.headers['if-none-match'] = `"b238fae501168d24097ce86d43d647c9515b1322"`;
            const getResourceDataRes2 = await bas.getResourceDataWithCache({ token, branchID, resourceID, basArchiveID });
            expect(getResourceDataRes2.status).to.be.eq(304);

            // Do something that increments the archiveRevision
            const createResourceRes2 = await bas.createResource({
                token,
                branchID,
                resourceID: uuid.v4(),
                data: JSON.stringify(data),
                attributes,
            });
            expect(createResourceRes2.body).to.have.property('archiveRevision', archiveRevision + 1);

            // AchiveRevision does not affect ETag
            const getResourceDataRes3 = await bas.getResourceDataWithCache({ token, branchID, resourceID, basArchiveID });
            expect(getResourceDataRes3.status).to.be.eq(304);

            // Update Resource
            const newData = { ...data, newData: 1 };
            const setResourceDataRes = await bas.setResourceData({
                token,
                branchID,
                resourceID,
                data: JSON.stringify(newData),
            });
            expect(setResourceDataRes.body).to.have.property('archiveRevision', archiveRevision + 2);

            // ETag changed after resource was updated
            const getResourceDataRes4 = await bas.getResourceDataWithCache({ token, branchID, resourceID, basArchiveID });
            expect(getResourceDataRes4.status).to.be.eq(200);
            expect(getResourceDataRes4.headers.etag).not.to.eq(etag);

            // Cache-Control in request is honored
            bas.headers['if-none-match'] = getResourceDataRes4.headers.etag;
            bas.headers['Cache-Control'] = `no-cache`;
            const getResourceDataRes5 = await bas.getResourceDataWithCache({ token, branchID, resourceID, basArchiveID });
            expect(getResourceDataRes5.status).to.be.eq(200);

            // Cannot access archiveID not linked to the token
            const getResourceDataRes6 = await bas.getResourceDataWithCache({
                token,
                branchID,
                resourceID,
                basArchiveID: 'some_other_archive_id',
            });
            expect(getResourceDataRes6.body).to.have.property('error');
        });

        test('Flush', async function () {
            let flushRes = await bas.flush({ token, force: true });
            expect(flushRes.body).to.not.have.key('error');
        });

        test('Close', async function () {
            let closeRes = await bas.close({ token });
            expect(closeRes.body).to.not.have.key('error');
        });

        test('create read update Resource and Resource Attributes', async function () {
            const branchID = 'Master';
            const resourceID = uuid.v4();
            const data = { mockup: { controls: {}, measuredH: '0', measuredW: '0', mockupH: '0', mockupW: '0', version: '1.0' } };
            const attributes = {
                name: 'New Wireframe 1',
                kind: 'mockup',
                trashed: false,
                importedFrom: '',
                creationDate: 0,
                modifiedBy: null,
                notes: null,
                order: 10776314.299002064,
            };

            const createResourceRes = await bas.createResource({
                token,
                branchID,
                resourceID,
                data: JSON.stringify(data),
                attributes,
            });
            expect(createResourceRes.body).to.have.property('ID', resourceID);

            const getResourceDataRes = await bas.getResourceData({ token, branchID, resourceID });
            expect(getResourceDataRes.body).to.have.property('data');
            expect(JSON.parse(getResourceDataRes.body.data)).to.deep.eq(data);

            const getResourceAttributesRes = await bas.getResourceAttributes({ token, branchID, resourceID });
            expect(getResourceAttributesRes.body).to.have.property('attributes');
            expect(getResourceAttributesRes.body.attributes).to.deep.eq(attributes);

            const newData = { ...data, newData: 1 };
            const setResourceDataRes = await bas.setResourceData({
                token,
                branchID,
                resourceID,
                data: JSON.stringify(newData),
            });
            expect(setResourceDataRes.body).to.have.key('archiveRevision');

            const getResourceDataRes2 = await bas.getResourceData({ token, branchID, resourceID });
            expect(getResourceDataRes2.body).to.have.property('data');
            expect(JSON.parse(getResourceDataRes2.body.data)).to.deep.eq(newData);

            const newAttributes = { ...attributes, newAttributes: 1 };
            const setResourceAttributesRes = await bas.setResourceAttributes({
                token,
                branchID,
                resourceID,
                attributes: newAttributes,
            });
            expect(setResourceAttributesRes.body).to.have.key('archiveRevision');

            const getResourceAttributesRes2 = await bas.getResourceAttributes({ token, branchID, resourceID });
            expect(getResourceAttributesRes2.body).to.have.property('attributes');
            expect(getResourceAttributesRes2.body.attributes).to.deep.eq(newAttributes);

            const deleteResourcesRes = await bas.deleteResources({
                token,
                branchID,
                resourceIDs: [resourceID],
            });
            expect(deleteResourcesRes.body).to.have.property('archiveRevision');
            expect(deleteResourcesRes.body).to.have.property('heuristicArchiveSize');

            const getResourceDataRes3 = await bas.getResourceData({ token, branchID, resourceID });
            expect(getResourceDataRes3.body).to.have.key('error');
        });

        test('move resource from one branch to another', async function () {
            const initialBranchID = 'Master';
            const resourceID = uuid.v4();
            const resourceData = { mockup: { controls: {}, measuredH: '0', measuredW: '0', mockupH: '0', mockupW: '0', version: '1.0' } };
            const resourceAttributes = {
                name: 'New Wireframe 1',
                kind: 'mockup',
                trashed: false,
                importedFrom: '',
                creationDate: 0,
                modifiedBy: null,
                notes: null,
                order: 10776314.299002064,
            };

            const createResourceRes = await bas.createResource({
                token,
                branchID: initialBranchID,
                resourceID,
                data: JSON.stringify(resourceData),
                attributes: resourceAttributes,
            });
            expect(createResourceRes.body).to.have.property('ID', resourceID);

            const branchAttributes = { branchName: 'Alternate 229v' };
            const newBranchID = uuid.v4();
            const createBranchRes = await bas.createBranch({
                token,
                branchID: newBranchID,
                attributes: branchAttributes,
            });
            expect(createBranchRes.body).to.have.key('archiveRevision');

            const setResourceBranchIDRes = await bas.setResourceBranchID({
                token,
                resourceID,
                oldBranchID: initialBranchID,
                newBranchID: newBranchID,
            });
            expect(setResourceBranchIDRes.body).to.have.key('archiveRevision');

            // Cannot delete branch without deleting resource
            const deleteBranchesRes = await bas.deleteBranches({
                token,
                branchIDs: [newBranchID],
            });
            expect(deleteBranchesRes.body).to.have.key('error');

            // Delete resource in new branch
            const deleteResourcesRes = await bas.deleteResources({
                token,
                branchID: newBranchID,
                resourceIDs: [resourceID],
            });
            expect(deleteResourcesRes.body).to.have.property('archiveRevision');
            expect(deleteResourcesRes.body).to.have.property('heuristicArchiveSize');

            // Retry to delete the same branch
            const deleteBranchesRes2 = await bas.deleteBranches({
                token,
                branchIDs: [newBranchID],
            });
            expect(deleteBranchesRes2.body).to.have.key('archiveRevision');
        });

        test('get user info', async function () {
            const getUserInfoRes = await bas.getUserInfo({ internalID: userID, token });
            expect(getUserInfoRes.body.userInfo).to.exist;
            // Check that the returned userInfo contains all expected properties
            expect(getUserInfoRes.body.userInfo).to.include(userInfo);
        });

        test('udpate user info', async function () {
            const newUserInfo = { ...userInfo, name: `Updated User` };

            const updateUserInfoRes = await bas.updateUserInfo({ token, userInfo: newUserInfo });
            expect(updateUserInfoRes.body).to.have.property('success', 'ok');

            const getUserInfoRes = await bas.getUserInfo({ internalID: userID, token });
            expect(getUserInfoRes.body.userInfo).to.exist;
            expect(getUserInfoRes.body.userInfo).to.deep.eq(newUserInfo);
        });

        test('getArchiveRevision', async function () {
            const getArchiveRevisionRes = await bas.getArchiveRevision({ token });
            expect(getArchiveRevisionRes.body).to.have.key('archiveRevision');
        });

        test('Download BMPR', async function () {
            let downloadRes = await bas.download({ token });
            expect(downloadRes.body.length).to.be.gte(1);
        });

        test('upload and download of temp file', async function () {
            // png 1x1 gray pixel
            const base64data =
                'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABAQMAAAAl21bKAAAABlBMVEXMzMyWlpYU2uzLAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAACklEQVQImWNgAAAAAgAB9HFkpgAAAABJRU5ErkJggg==';
            const uploadTempFileRes = await bas.uploadTempFile({
                token,
                filename: 'tempfile',
                mimetype: 'image/png',
                data: base64data,
            });
            expect(uploadTempFileRes.body).to.have.key('key');

            const tempFileKey = uploadTempFileRes.body.key;

            const downloadTempFileRes = await bas.downloadTempFile({ token, key: tempFileKey });
            expect(downloadTempFileRes.body.toString('base64')).to.eq(base64data);

            testContext.expectNoErrorsInLogs();
        });

        test('download unknown temp file', async function () {
            const downloadWrongFileRes = await bas.downloadTempFile({ token, key: `123` });
            expect(downloadWrongFileRes.status).to.be.eq(404);
            expect(downloadWrongFileRes.body).to.deep.eq({});

            testContext.expectNoErrorsInLogs();
        });

        test('download malformed temp file', async function () {
            const key = `malformed-redis-file-key`;
            const value = {};
            const redisAdapter = testContext.redisAdapter;
            redisAdapter.setex(key, 5, JSON.stringify(value));

            const downloadWrongFileRes = await bas.downloadTempFile({ token, key });
            expect(downloadWrongFileRes.status).to.be.eq(500);
        });

        test('Get users list', async function () {
            testContext.mockBackendRTCClient.setReturnHerenowUuids([userID]);
            let usersListRes = await bas.getUsersList({ token });
            expect(Array.isArray(usersListRes.body.users)).to.be.true;
            expect(usersListRes.body.users.length).to.eq(1);
        });

        test('Set Platform Archive Name', async function () {
            const setPlatformArchiveRes = await bas.setPlatformArchiveName({ token, platformArchiveName: 'newname' });
            expect(setPlatformArchiveRes.body).to.have.property('platformArchiveName', 'newname');
        });

        test('Fetch URL from web: 200', async function () {
            const base64data =
                'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABAQMAAAAl21bKAAAABlBMVEXMzMyWlpYU2uzLAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAACklEQVQImWNgAAAAAgAB9HFkpgAAAABJRU5ErkJggg==';
            const responseBuffer = Buffer.from(base64data, 'base64');

            const fetchNock = nock('https://convox-rack-staging-bas-s3.s3.amazonaws.com/')
                .get(`/test/test_image_do_not_delete.png`)
                .times(1)
                .reply(200, responseBuffer, { 'content-type': 'image/png' });
            const fetchRes = await bas.fetch({
                token,
                url: 'https://convox-rack-staging-bas-s3.s3.amazonaws.com/test/test_image_do_not_delete.png',
            });

            fetchNock.done();
            expect(fetchRes.body.length).to.be.gte(1);
        });

        test('Fetch URL from web: 404', async function () {
            const fetchNock = nock('https://convox-rack-staging-bas-s3.s3.amazonaws.com/')
                .get(`/test/test_image_do_not_delete.png`)
                .times(1)
                .reply(404, 'Not Found', { 'content-type': 'image/png' });

            const fetchRes = await bas.fetch({
                token,
                url: 'https://convox-rack-staging-bas-s3.s3.amazonaws.com/test/test_image_do_not_delete.png',
            });
            fetchNock.done();
            expect(fetchRes.status).to.eq(404);
        });

        test('Fetch URL from Flickr', async function () {
            const xmlResponseText = `<?xml version="1.0" encoding="utf-8" ?><rsp stat="ok"><method>flickr.test.echo</method><format>rest</format><api_key>248ff483d3f4326ec95a6734a3202366</api_key></rsp>`;
            const flickrNock = nock('https://api.flickr.com/')
                .get(`/services/rest`)
                .times(2)
                .query(true)
                .reply(200, xmlResponseText, { 'content-type': 'text/xml; charset=utf-8' });

            const queryParams = new URLSearchParams({
                method: 'flickr.test.echo',
                format: 'rest',
                api_key: '248ff483d3f4326ec95a6734a3202366',
            });
            let fetchRes = await bas.fetch({ token, url: `https://api.flickr.com/services/rest?${queryParams.toString()}` });

            flickrNock.done();
            expect(fetchRes.text).to.be.eq(xmlResponseText);
        });

        test('get all the permalinks data from archive', async function () {
            const resource1 = dump.Resources[0];
            const { ID: resourceID1, BRANCHID: branchID1 } = resource1;

            const resource2 = dump.Resources[1];
            const { ID: resourceID2, BRANCHID: branchID2 } = resource2;

            const editURL1 = 'https://url.for.editing.this.project/resource1';
            const editURL2 = 'https://url.for.editing.this.project/resource2';

            // create 2 permalinks
            let setPermalink1Res = await bas.setPermalink({
                token,
                branchID: branchID1,
                resourceID: resourceID1,
                permalinkInfo: {
                    edit: editURL1,
                },
            });
            expect(setPermalink1Res.body).to.have.property('permalinkID');

            let setPermalink2Res = await bas.setPermalink({
                token,
                branchID: branchID2,
                resourceID: resourceID2,
                permalinkInfo: {
                    edit: editURL2,
                    image: { format: 'jpg' },
                },
            });
            expect(setPermalink2Res.body).to.have.property('permalinkID');

            // make sure permalinks exist
            let getPermalinksDataRes = await bas.getPermalinksData({ token });
            expect(getPermalinksDataRes.body).to.have.property('permalinksData');

            let permalinksData = getPermalinksDataRes.body.permalinksData;
            expect(permalinksData).to.have.lengthOf(2);

            let permalinkIDs = permalinksData.map((p: any) => p.permalinkID);
            expect(permalinkIDs).to.contain(setPermalink1Res.body.permalinkID);
            expect(permalinkIDs).to.contain(setPermalink2Res.body.permalinkID);

            // Try to get permalinks from deleted archive
            const deleteRes = await bas.delete({ token });
            expect(deleteRes.body).to.deep.eq({});

            const getPermalinksDataRes2 = await bas.getPermalinksData({ token });
            expect(getPermalinksDataRes2.body).to.have.property('error');
        });

        suite('Permalinks Shared', async function () {
            test('get all the permalinks data from archive', async function () {
                const resource1 = dump.Resources[0];
                const { ID: resourceID1, BRANCHID: branchID1 } = resource1;

                const resource2 = dump.Resources[1];
                const { ID: resourceID2, BRANCHID: branchID2 } = resource2;

                const editURL1 = 'https://url.for.editing.this.project/resource1';
                const editURL2 = 'https://url.for.editing.this.project/resource2';

                // create 2 permalinks
                let setPermalink1Res = await bas.setPermalink({
                    token,
                    branchID: branchID1,
                    resourceID: resourceID1,
                    permalinkInfo: {
                        edit: editURL1,
                    },
                });
                expect(setPermalink1Res.body).to.have.property('permalinkID');

                let setPermalink2Res = await bas.setPermalink({
                    token,
                    branchID: branchID2,
                    resourceID: resourceID2,
                    permalinkInfo: {
                        edit: editURL2,
                        image: { format: 'jpg' },
                    },
                });
                expect(setPermalink2Res.body).to.have.property('permalinkID');

                // make sure permalinks exist
                let getPermalinksDataRes = await bas.getPermalinksData({ token });
                expect(getPermalinksDataRes.body).to.have.property('permalinksData');

                let permalinksData = getPermalinksDataRes.body.permalinksData;
                expect(permalinksData).to.have.lengthOf(2);

                let permalinkIDs = permalinksData.map((p: any) => p.permalinkID);
                expect(permalinkIDs).to.contain(setPermalink1Res.body.permalinkID);
                expect(permalinkIDs).to.contain(setPermalink2Res.body.permalinkID);

                // Try to get permalinks from deleted archive
                const deleteRes = await bas.delete({ token });
                expect(deleteRes.body).to.deep.eq({});

                const getPermalinksDataRes2 = await bas.getPermalinksData({ token });
                expect(getPermalinksDataRes2.body).to.have.property('error');
            });

            test('Create permalink dirty with payload null and delete project offline', async function () {
                const {
                    body: { permalinkID },
                } = await createPermalinkOnFirstResource({ kind, token, dump, dataResidency: 'us', bas });

                let getPermalinkRes = await bas.getPermalink({
                    id: permalinkID,
                    query: 'metadata',
                });
                expect(getPermalinkRes.body).to.not.have.property('error');

                const resource = dump.Resources[0];
                const {
                    ID: resourceID,
                    BRANCHID: branchID,
                    ATTRIBUTES: { thumbnailID },
                } = resource;
                let archiveRevision = dump.Info.ArchiveRevision;

                // Update thumbnail outdates permalink
                const attributes = {
                    branchID,
                    resourceID,
                    image: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABAQMAAAAl21bKAAAABlBMVEXMzMyWlpYU2uzLAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAACklEQVQImWNgAAAAAgAB9HFkpgAAAABJRU5ErkJggg==',
                };
                const setThumbnailRes = await bas.setThumbnail({ token, thumbnailID, attributes, archiveRevision });
                expect(setThumbnailRes.body).to.have.key('archiveRevision');

                getPermalinkRes = await bas.getPermalinkData({ token, resourceID, branchID });
                expect(getPermalinkRes.body).to.have.property('dirty', true);

                testContext.LambdaMock.payload = 'null';
                await bas.close({ token });

                let deleteOfflineRes = await bas.deleteOffline({
                    platformArchiveID: `${projectId}`,
                    platformSiteID: `${siteId}`,
                    platformKind: kind,
                });
                expect(deleteOfflineRes.body).to.have.property('message', 'success');

                let getPermalinkRes2 = await bas.getPermalink({
                    id: permalinkID,
                    query: 'metadata',
                });
                expect(getPermalinkRes2.body).to.have.property('error');
                expect(getPermalinkRes2.body).to.have.property('notFound', true);

                testContext.expectErrorsInLogs((err) =>
                    err.message.includes('Unexpected error in connector.generatePermalinkImage: {"Payload":null}')
                );
                testContext.expectErrorsInLogs((err) => err.message.includes('Error getting BAS secret'));
            });
            test('Create permalink dirty with payload null and delete project offline', async function () {
                const {
                    body: { permalinkID },
                } = await createPermalinkOnFirstResource({ kind, token, dump, dataResidency: 'us', bas });

                let getPermalinkRes = await bas.getPermalink({
                    id: permalinkID,
                    query: 'metadata',
                });
                expect(getPermalinkRes.body).to.not.have.property('error');

                const resource = dump.Resources[0];
                const {
                    ID: resourceID,
                    BRANCHID: branchID,
                    ATTRIBUTES: { thumbnailID },
                } = resource;
                let archiveRevision = dump.Info.ArchiveRevision;

                // Update thumbnail outdates permalink
                const attributes = {
                    branchID,
                    resourceID,
                    image: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABAQMAAAAl21bKAAAABlBMVEXMzMyWlpYU2uzLAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAACklEQVQImWNgAAAAAgAB9HFkpgAAAABJRU5ErkJggg==',
                };
                const setThumbnailRes = await bas.setThumbnail({ token, thumbnailID, attributes, archiveRevision });
                expect(setThumbnailRes.body).to.have.key('archiveRevision');

                getPermalinkRes = await bas.getPermalinkData({ token, resourceID, branchID });
                expect(getPermalinkRes.body).to.have.property('dirty', true);

                testContext.LambdaMock.payload = 'null';
                await bas.close({ token });

                let deleteOfflineRes = await bas.deleteOffline({
                    platformArchiveID: `${projectId}`,
                    platformSiteID: `${siteId}`,
                    platformKind: kind,
                });
                expect(deleteOfflineRes.body).to.have.property('message', 'success');

                let getPermalinkRes2 = await bas.getPermalink({
                    id: permalinkID,
                    query: 'metadata',
                });
                expect(getPermalinkRes2.body).to.have.property('error');
                expect(getPermalinkRes2.body).to.have.property('notFound', true);

                testContext.expectErrorsInLogs((err) =>
                    err.message.includes('Unexpected error in connector.generatePermalinkImage: {"Payload":null}')
                );
                testContext.expectErrorsInLogs((err) => err.message.includes('Error getting BAS secret'));
            });

            test('Create permalink and delete project offline', async function () {
                const {
                    body: { permalinkID },
                } = await createPermalinkOnFirstResource({ kind, token, dump, dataResidency: 'us', bas });
                await bas.close({ token });

                let getPermalinkRes = await bas.getPermalink({
                    id: permalinkID,
                    query: 'metadata',
                });
                expect(getPermalinkRes.body).to.not.have.property('error');

                let deleteOfflineRes = await bas.deleteOffline({
                    platformArchiveID: `${projectId}`,
                    platformSiteID: `${siteId}`,
                    platformKind: kind,
                });
                expect(deleteOfflineRes.body).to.have.property('message', 'success');

                let getPermalinkRes2 = await bas.getPermalink({
                    id: permalinkID,
                    query: 'metadata',
                });
                expect(getPermalinkRes2.body).to.have.property('error');
                expect(getPermalinkRes2.body).to.have.property('notFound', true);
                testContext.expectErrorsInLogs((err) => err.message.includes('Error getting BAS secret'));
            });
        });
    });
}
