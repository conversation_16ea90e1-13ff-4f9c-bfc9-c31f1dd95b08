/** @prettier */
/* global testContext */
/* eslint-env mocha */

import { expect } from 'chai';

import { BASApiClient } from './utils/apiclient.ts';
import { defineCommonOnlineTests, defineCommonOfflineTests } from './shared-tests.js';
import { RATE_LIMITERS } from '../rate-limitation.ts';
import assert from 'assert';
import type { Application } from 'express';
import { WebDemoConnector } from '../connectors/webdemo.ts';

suite('Webdemo', function () {
    const kind = `wd`;
    const userInfo = {
        userId: 'someUserId',
        name: `User`,
        displayName: `user`,
        avatarURL: `https://placehold.it/60x60`,
    };
    const getPlatformToken = () => {
        const connector = testContext.getConnector(kind);
        assert(connector instanceof WebDemoConnector, 'Expected WebDemoConnector');
        return connector.generalAccessPlatformToken;
    };
    const archiveID = 'wd_test';
    const siteId = 'somesite';

    let app: Application;
    /** @type BASApiClient */
    let bas: BASApiClient;

    setup(async function () {
        app = testContext.app;
        bas = new BASApiClient(app, {
            kind,
            userInfo,
            platformArchiveID: archiveID,
            siteID: siteId,
            getPlatformToken,
            adminUser: testContext.basAdminUser,
            adminSecret: testContext.basAdminSecret,
        });
    });

    test('Create', async function () {
        const createRes = await bas.create();
        expect(createRes.body).to.deep.equal({ platformArchiveID: archiveID });
    });

    suite('Offline Methods', async function () {
        setup(async function () {
            const createRes = await bas.create();
            expect(createRes.body).to.deep.equal({ platformArchiveID: archiveID });
        });

        defineCommonOfflineTests(() => ({ bas, kind, userInfo, archiveId: archiveID, siteId: siteId }));

        test('Authorized /health?webdemo=true', async function () {
            const res = await bas.health({ webdemo: true, basicAuth: true });
            expect(res.status).to.equal(200);
            expect(res.body.webdemo).to.equal('ok');
        });

        test('Rate limtation for /health?webdemo=true', async function () {
            const healthCheckRateLimiter = RATE_LIMITERS.find((rateLimiter) => rateLimiter.name === 'healthCheck');
            assert(healthCheckRateLimiter !== undefined, 'healthCheck rate limiter not found');
            for (let i = 0; i < healthCheckRateLimiter.maxValue; i++) {
                const res = await bas.health({ webdemo: true, basicAuth: true });
                expect(res.status).to.equal(200);
                expect(res.body.webdemo).to.equal('ok');
            }
            const res = await bas.health({ webdemo: true, basicAuth: true });
            expect(res.status).to.equal(429);
        });

        test('Open Template', async function () {
            const bas = new BASApiClient(app, {
                kind,
                userInfo,
                platformArchiveID: undefined, // This should trigger the loading of default template
                getPlatformToken,
                adminUser: testContext.basAdminUser,
                adminSecret: testContext.basAdminSecret,
            });
            let openRes = await bas.open();
            expect(openRes.body.error).to.equal('Call create first'); // This is expected for Webdemo connector
        });

        test('Project Exists of Platform', async function () {
            let projectExistsOnPlatformRes = await bas.projectExistsOnPlatform({
                platformArchiveID: archiveID,
                platformSiteID: siteId,
                kind: kind,
            });
            expect(projectExistsOnPlatformRes.body).to.have.property('status', true); // CAVEAT: WebDemo returns always true, even after deleting the project
        });
    });

    suite('Online Methods', async function () {
        let token: string;
        let userID: string;
        let dump: BmprDump;
        let basArchiveID: string;

        setup(async function () {
            const createRes = await bas.create();
            expect(createRes.body).to.deep.equal({ platformArchiveID: archiveID });

            let openRes = await bas.open();
            ({ token, userID, dump, basArchiveID } = openRes.body);
            expect(token).not.to.be.undefined;
            expect(userID).not.to.be.undefined;
            expect(dump).not.to.be.undefined;

            testContext.mockBackendRTCClient.setReturnHerenowUuids([userID]);
        });

        defineCommonOnlineTests(() => ({ bas, token, userInfo, userID, dump, basArchiveID, kind, projectId: archiveID, siteId }));

        test('Call API with bad token', async function () {
            let logUserEventRes = await bas.logUserEvent({ token: '123', userEvent: { some: 'event' } });
            expect(logUserEventRes.body).to.have.property('error', 'Session closed');
            expect(logUserEventRes.body).to.have.property('busy', true);
        });

        test('Call API without token', async function () {
            let logUserEventRes = await bas.logUserEvent({ userEvent: { some: 'event' } });
            expect(logUserEventRes.body).to.have.property('error', 'Session token is null');
        });
    });
});
