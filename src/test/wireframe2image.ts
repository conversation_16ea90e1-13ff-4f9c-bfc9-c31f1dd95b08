/** @prettier */
/* global testContext */
/* eslint-env mocha */

import { expect } from 'chai';

import { LambdaMock } from './utils/mocks.ts';
import { Wireframe2image_adapter } from '../wireframe2image_adapter.ts';

suite('Wireframe2Image Adapter', () => {
    let w2iAdapter;
    test('success', async function () {
        w2iAdapter = new Wireframe2image_adapter({
            lambda: new LambdaMock({ payload: JSON.stringify({ response: { success: true } }) }),
            name: '',
            dataResidencies: {
                us: { bucketRegion: '', bucketName: '', baseDir: '' },
                eu: { bucketRegion: '', bucketName: '', baseDir: '' },
            },
        });

        const res = await w2iAdapter.invoke({ dataResidency: 'us' } as any);
        expect(res).to.equal(true);
    });

    test('img base64', async function () {
        w2iAdapter = new Wireframe2image_adapter({
            lambda: new LambdaMock({
                payload: JSON.stringify({
                    response: { img: ['iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAFhAJ/wlseKgAAAABJRU5ErkJggg=='] },
                }),
            }),
            name: '',
            dataResidencies: {
                us: { bucketRegion: '', bucketName: '', baseDir: '' },
                eu: { bucketRegion: '', bucketName: '', baseDir: '' },
            },
        });

        let _image: string | boolean | null = null;
        w2iAdapter
            .invoke({ dataResidency: 'us' } as any)
            .then((image: string | boolean) => {
                _image = image;
                expect(image).to.equal('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAFhAJ/wlseKgAAAABJRU5ErkJggg==');
            })
            .catch((error) => {
                expect(_image).to.be.null;
            })
            .finally(() => {
                expect(_image).to.be.not.empty;
            });
    });

    test('exception - wrong payload format', async function () {
        w2iAdapter = new Wireframe2image_adapter({
            lambda: new LambdaMock({ payload: '{}' }),
            name: '',
            dataResidencies: {
                us: { bucketRegion: '', bucketName: '', baseDir: '' },
                eu: { bucketRegion: '', bucketName: '', baseDir: '' },
            },
        });

        let exception = false;
        const res = await w2iAdapter
            .invoke({ dataResidency: 'us' } as any)
            .catch((err) => {
                exception = true;
                expect(err).to.be.an('Error');
            })
            .finally(() => {
                expect(exception).to.equal(true);
            });
    });

    test('exception - payload null', async function () {
        w2iAdapter = new Wireframe2image_adapter({
            lambda: new LambdaMock({ payload: 'null' }),
            name: '',
            dataResidencies: {
                us: { bucketRegion: '', bucketName: '', baseDir: '' },
                eu: { bucketRegion: '', bucketName: '', baseDir: '' },
            },
        });

        let exception = false;
        const res = await w2iAdapter
            .invoke({ dataResidency: 'us' } as any)
            .catch((err) => {
                exception = true;
                expect(err).to.be.an('Error');
                expect(err.message).to.equal(JSON.stringify({ Payload: null }));
            })
            .finally(() => {
                expect(exception).to.equal(true);
            });
    });

    test('exception - error', async function () {
        w2iAdapter = new Wireframe2image_adapter({
            lambda: new LambdaMock({ error: { message: 'some errors' }, payload: '{}' }),
            name: '',
            dataResidencies: {
                us: { bucketRegion: '', bucketName: '', baseDir: '' },
                eu: { bucketRegion: '', bucketName: '', baseDir: '' },
            },
        });

        let exception = false;
        const res = await w2iAdapter
            .invoke({ dataResidency: 'us' } as any)
            .catch((err) => {
                exception = true;
                expect(err).to.be.an('Error');
                expect(err.message).to.equal(JSON.stringify({ message: 'some errors' }));
            })
            .finally(() => {
                expect(exception).to.equal(true);
            });
    });
});
