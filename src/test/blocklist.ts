/** @prettier */
/* global testContext */
/* eslint-env mocha */

import { expect } from 'chai';

import assert from 'assert';
import type { Application } from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import * as jwt from '../connectors/lib/jwt.js';
import { BASApiClient } from './utils/apiclient.ts';
import type { CloudServerMock } from './utils/mocks.ts';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

suite('Blocklist', function () {
    const kind = `cloud`;
    const cloudUserId = 10;
    const userInfo = {
        // User info
        name: `User`,
        displayName: `user`,
        avatarURL: `https://placehold.it/60x60`,
        userId: `${cloudUserId}`,
    };
    const adminRole: 'ADMIN' | 'COMMENTER' | 'VIEWER' = 'ADMIN';

    const jwtsecret = 'foobar';
    const getCloudPlatformToken = (extra: unknown) => {
        assert(extra !== null && typeof extra === 'object', 'extra must be an object');
        assert('siteId' in extra, 'siteId is required');
        assert('projectId' in extra, 'projectId is required');
        assert('role' in extra, 'role is required');
        const { siteId, projectId, role } = extra;
        return jwt.encode(
            {
                exp: Date.now() + 3600,
                sub: 'create_archive',
                iss: 'cloud-server',
                siteId,
                projectId,
                role,
                userId: userInfo.userId,
            },
            jwtsecret,
            'HS256'
        );
    };

    const atlassianClientKey = '************************03b68dbacb54';
    const getAtlassianPlatformToken = (extra = {}) => {
        return jwt.encode(
            {
                sub: '557058:c561a510-a897-4c8f-920d-b95bbea256aa',
                qsh: 'be19fbc7a018f74da5086d58a9977f031e7729b807ca18059b2ef41df9b3f24d',
                iss: atlassianClientKey,
                context: {},
                exp: Date.now() / 1000 + 3600,
                iat: Date.now() / 1000,
                ...extra,
            },
            jwtsecret,
            'HS256'
        );
    };
    const atlassianConnectorData = {
        sharedSecret: jwtsecret,
        clientKey: atlassianClientKey,
    };

    const projectId = 1;
    const siteID = 1000;

    const platformTokenExtra = {
        siteId: siteID,
        projectId: projectId,
        role: 'ADMIN',
    };

    const rtcKind = 'new_rtc';

    let app!: Application;
    let cloudServerMock!: CloudServerMock;

    let basAdminClient!: BASApiClient;
    let basUserClient!: BASApiClient;

    setup(async function () {
        app = testContext.app;
        cloudServerMock = testContext.cloudServerMock;

        basUserClient = new BASApiClient(app, {
            kind,
            userInfo,
            platformArchiveID: projectId,
            siteID,
            getPlatformToken: getCloudPlatformToken,
            platformTokenExtra: {
                ...platformTokenExtra,
                userId: cloudUserId,
            },
            headers: {
                'user-agent': 'Balsamiq Wireframes Windows/4.0.47 (OS/Microsoft Windows NT 10.0.18362.0 .NET/Core 3.1.3 (x64))',
            },
        });

        basAdminClient = new BASApiClient(app, {
            kind,
            userInfo,
            platformArchiveID: projectId,
            siteID,
            getPlatformToken: getCloudPlatformToken,
            adminUser: testContext.basAdminUser,
            adminSecret: testContext.basAdminSecret,
            platformTokenExtra,
        });
    });

    test('getUserKeyFromToken works for cloud, jira, confluence', async function () {
        // Cloud
        const getUserKeyRes = await basAdminClient.getUserKeyFromToken({
            platformToken: getCloudPlatformToken({
                siteId: siteID,
                projectId,
                userId: cloudUserId,
                role: adminRole,
            }),
            kind,
        });
        expect(getUserKeyRes.body).to.have.property('userKey');
        expect(getUserKeyRes.body.userKey).to.not.be.null;

        // Jira
        const postCustomAPIRes = await basAdminClient.postCustomAPI({
            kind: 'jira',
            api: 'installed',
            data: atlassianConnectorData,
        });
        const postCustomAPIResResult = JSON.parse(postCustomAPIRes.text);
        expect(postCustomAPIResResult).to.deep.eq(atlassianConnectorData);

        const getUserKeyRes2 = await basAdminClient.getUserKeyFromToken({
            platformToken: getAtlassianPlatformToken(),
            kind: 'jira',
        });
        expect(getUserKeyRes2.body).to.have.property('userKey');
        expect(getUserKeyRes2.body.userKey).to.not.be.null;

        // Confluence
        const postCustomAPIRes2 = await basAdminClient.postCustomAPI({
            kind: 'confluence',
            api: 'installed',
            data: atlassianConnectorData,
        });
        const postCustomAPIResResult2 = JSON.parse(postCustomAPIRes2.text);
        expect(postCustomAPIResResult2).to.deep.eq(atlassianConnectorData);

        const getUserKeyRes3 = await basAdminClient.getUserKeyFromToken({
            platformToken: getAtlassianPlatformToken(),
            kind: 'confluence',
        });
        expect(getUserKeyRes3.body).to.have.property('userKey');
        expect(getUserKeyRes3.body.userKey).to.not.be.null;

        // Expired token is ignored for atlassian connectors
        const getUserKeyRes4 = await basAdminClient.getUserKeyFromToken({
            platformToken: getAtlassianPlatformToken({ exp: Date.now() / 1000 - 3600 }),
            kind: 'confluence',
        });
        expect(getUserKeyRes4.body).to.have.property('userKey');
        expect(getUserKeyRes4.body.userKey).to.be.null;
    });

    test('API requires authentication', async function () {
        const nonAdminClient = new BASApiClient(app, {
            kind,
            userInfo,
            adminUser: 'badUser',
            adminSecret: 'basSecret',
        });

        const getBlockListRes = await nonAdminClient.getBlockList();
        expect(getBlockListRes.body).to.have.property('error');

        const addToBlockListRes = await nonAdminClient.addToBlockList({});
        expect(addToBlockListRes.body).to.have.property('error');

        const removeFromBlockListRes = await nonAdminClient.removeFromBlockList({});
        expect(removeFromBlockListRes.body).to.have.property('error');
    });

    test('Block and unblock CREATE by userID', async function () {
        // Try create, expect success
        const createRes = await basUserClient.createMultipart(path.join(__dirname, '../connectors/templates/2.0/webdemo.bmpr'));
        expect(createRes.body).to.have.property('platformArchiveID', `${projectId}`);
        let deleteOfflineRes = await basAdminClient.deleteOffline({
            platformArchiveID: `${projectId}`,
            platformSiteID: `${siteID}`,
            platformKind: kind,
        });
        expect(deleteOfflineRes.body).to.have.property('message', 'success');

        // Get the userKey from the platformToken
        const getUserKeyRes = await basAdminClient.getUserKeyFromToken({
            platformToken: getCloudPlatformToken({
                siteId: siteID,
                projectId,
                userId: cloudUserId,
                role: adminRole,
            }),
            kind,
        });
        expect(getUserKeyRes.body).to.have.property('userKey', `cloudUserId-${cloudUserId}`);
        const cloudUser = getUserKeyRes.body.userKey;

        // Block the user
        let addToBlockListRes = await basAdminClient.addToBlockList({
            items: [
                {
                    type: 'userKey',
                    userKey: cloudUser,
                },
            ],
        });
        expect(addToBlockListRes.body).to.not.have.property('error');

        // Try create, expect failure
        const createRes2 = await basUserClient.createMultipart(path.join(__dirname, '../connectors/templates/2.0/webdemo.bmpr'));
        expect(createRes2.body).to.have.property('error');

        // Unblock the user
        let removeFromBlockListRes = await basAdminClient.removeFromBlockList({
            items: [
                {
                    type: 'userKey',
                    userKey: cloudUser,
                },
            ],
        });
        expect(removeFromBlockListRes.body).to.not.have.property('error');

        // Try create, expect success
        const createRes3 = await basUserClient.createMultipart(path.join(__dirname, '../connectors/templates/2.0/webdemo.bmpr'));
        expect(createRes3.body).to.have.property('platformArchiveID', `${projectId}`);
    });

    test('Block and unblock OPEN by UserAgent and XBalsamiqBuildNumber', async function () {
        // Create a test archive
        const createRes = await basAdminClient.createMultipart(path.join(__dirname, '../connectors/templates/2.0/webdemo.bmpr'));
        expect(createRes.body).to.have.property('platformArchiveID', `${projectId}`);

        // Try open, expect success
        let openRes1 = await basUserClient.open();
        expect(openRes1.body).to.have.property('token');

        // Block the client
        let addToBlockListRes = await basAdminClient.addToBlockList({
            items: [
                {
                    type: 'productBuild',
                    productName: 'Balsamiq Wireframes Windows',
                    buildNumber: '4.0.47',
                },
            ],
        });
        expect(addToBlockListRes.body).to.have.property('addedCount', 1);

        // Try open, expect failure
        let openRes2 = await basUserClient.open();
        expect(openRes2.body).to.have.property('error');

        // Switch client version
        basUserClient.headers = {
            'user-agent': 'Balsamiq Wireframes Windows/4.0.48 (OS/Microsoft Windows NT 10.0.18362.0 .NET/Core 3.1.3 (x64))',
        };

        // Try open, expect success
        openRes1 = await basUserClient.open();
        expect(openRes1.body).to.have.property('token');

        // Block any version of the Desktop client
        addToBlockListRes = await basAdminClient.addToBlockList({
            items: [
                {
                    type: 'productBuild',
                    productName: 'Balsamiq Wireframes Windows',
                    buildNumber: '*',
                },
            ],
        });
        expect(addToBlockListRes.body).to.have.property('addedCount', 1);

        // Try open, expect failure
        openRes2 = await basUserClient.open();
        expect(openRes2.body).to.have.property('error');
        expect(openRes2.body.error).to.match(/Balsamiq for Desktop/gm); // Check the custom error message about Desktop Client

        // Switch client
        basUserClient.headers = { 'x-balsamiq-build-num': 'cloud-2235' };

        // expect success
        let openRes4 = await basUserClient.open();
        expect(openRes4.body).to.have.property('token');

        // Block the client
        let addToBlockListRes2 = await basAdminClient.addToBlockList({
            items: [
                {
                    type: 'productBuild',
                    productName: 'Balsamiq Cloud',
                    buildNumber: '2235',
                },
            ],
        });
        expect(addToBlockListRes2.body).to.have.property('addedCount', 1);

        // Try open, expect failure
        let openRes3 = await basUserClient.open();
        expect(openRes3.body).to.have.property('error');
    });

    test('Block and unblock CREATE by UserAgent and XBalsamiqBuildNumber', async function () {
        // Block the clients
        let addToBlockListRes = await basAdminClient.addToBlockList({
            items: [
                {
                    type: 'productBuild',
                    productName: 'Balsamiq Wireframes Windows',
                    buildNumber: '4.0.47',
                },
                {
                    type: 'productBuild',
                    productName: 'Balsamiq Cloud',
                    buildNumber: '2235',
                },
            ],
        });
        expect(addToBlockListRes.body).to.have.property('addedCount', 2);

        // Try create, expect failure
        const createRes = await basUserClient.createMultipart(path.join(__dirname, '../connectors/templates/2.0/webdemo.bmpr'));
        expect(createRes.body).to.have.property('error');

        // Switch client
        basUserClient.headers = { 'x-balsamiq-build-num': 'cloud-2235' };

        // Try create, expect failure
        const createRes2 = await basUserClient.createMultipart(path.join(__dirname, '../connectors/templates/2.0/webdemo.bmpr'));
        expect(createRes2.body).to.have.property('error');

        let removeFromBlockListRes = await basAdminClient.removeFromBlockList({
            items: [
                {
                    type: 'productBuild',
                    productName: 'Balsamiq Wireframes Windows',
                    buildNumber: '4.0.47',
                },
                {
                    type: 'productBuild',
                    productName: 'Balsamiq Cloud',
                    buildNumber: '2235',
                },
            ],
        });
        expect(removeFromBlockListRes.body).to.have.property('removedCount', 2);

        // Try create, expect success
        const createRes3 = await basUserClient.createMultipart(path.join(__dirname, '../connectors/templates/2.0/webdemo.bmpr'));
        expect(createRes3.body).to.have.property('platformArchiveID', `${projectId}`);
    });

    test('Block and unblock OPEN by archive and user', async function () {
        // Create a test archive
        const createRes = await basAdminClient.createMultipart(path.join(__dirname, '../connectors/templates/2.0/webdemo.bmpr'));
        expect(createRes.body).to.have.property('platformArchiveID', `${projectId}`);

        // Try open, expect success
        let openRes1 = await basUserClient.open();
        expect(openRes1.body).to.have.property('token');

        // Block the project
        let addToBlockListRes = await basAdminClient.addToBlockList({
            items: [
                {
                    type: 'platformArchive',
                    kind,
                    platformSiteID: siteID,
                    platformArchiveID: projectId,
                },
            ],
        });
        expect(addToBlockListRes.body).to.not.have.property('error');

        // Get the userKey from the platformToken
        const getUserKeyRes = await basAdminClient.getUserKeyFromToken({
            platformToken: getCloudPlatformToken({
                siteId: siteID,
                projectId,
                userId: cloudUserId,
                role: adminRole,
            }),
            kind,
        });
        expect(getUserKeyRes.body).to.have.property('userKey', `cloudUserId-${cloudUserId}`);
        const cloudUserKey = getUserKeyRes.body.userKey;

        // Block the user
        let addToBlockListRes2 = await basAdminClient.addToBlockList({
            items: [
                {
                    type: 'userKey',
                    userKey: cloudUserKey,
                },
            ],
        });
        expect(addToBlockListRes2.body).to.not.have.property('error');

        // Check entries in the blocklist
        let getBlockListRes = await basAdminClient.getBlockList();
        expect(getBlockListRes.body).to.have.property('items');
        expect(getBlockListRes.body.items.length).to.eq(2);

        // Try open, expect failure
        let openRes2 = await basUserClient.open();
        expect(openRes2.body).to.have.property('error');

        // Remove project block
        let removeFromBlockListRes = await basAdminClient.removeFromBlockList({
            items: [
                {
                    type: 'platformArchive',
                    kind,
                    platformSiteID: siteID,
                    platformArchiveID: projectId,
                },
            ],
        });
        expect(removeFromBlockListRes.body).to.not.have.property('error');

        // Try open, expect failure
        let openRes3 = await basUserClient.open();
        expect(openRes3.body).to.have.property('error');

        // Remove user block
        let removeFromBlockListRes2 = await basAdminClient.removeFromBlockList({
            items: [
                {
                    type: 'userKey',
                    userKey: cloudUserKey,
                },
            ],
        });
        expect(removeFromBlockListRes2.body).to.not.have.property('error');

        // Try open, expect success
        let openRes4 = await basUserClient.open();
        expect(openRes4.body).to.have.property('token');
    });
});
