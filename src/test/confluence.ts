/** @prettier */
import { BASApiClient } from './utils/apiclient.ts';
import type { Application } from 'express';
import jwt from 'atlassian-jwt';
import { expect } from 'chai';
import { ConfluenceConnector } from '../connectors/confluence.js';
import assert from 'assert';

suite('Confluence', function () {
    const kind = `confluence`;
    const userInfo = {
        userId: '557058:18e32678-32c8-4d4b-b838-55f0ee6f9597',
        name: `User`,
        displayName: `user`,
        avatarURL: `https://placehold.it/60x60`,
    };
    const getPlatformToken = () => {
        const connector = testContext.getConnector(kind);
        assert(connector instanceof ConfluenceConnector, 'Expected ConfluenceConnector');
        throw new Error('getPlatformToken not implemented for confluence connector');
        return '';
    };
    const archiveID = 'confluence_test';
    const siteID = 'somesite8f896fa1-641f-3e55-9a60-5ff48c4696e5_10028';

    let app: Application;
    let bas: BASApiClient;

    setup(async function () {
        app = testContext.app;
        bas = new BASApiClient(app, {
            kind,
            userInfo,
            platformArchiveID: archiveID,
            siteID,
            getPlatformToken,
            adminUser: testContext.basAdminUser,
            adminSecret: testContext.basAdminSecret,
        });
    });

    suite('Install connector', function () {
        const installationData = {
            key: 'com.balsamiq.mockups.confluence.staging',
            clientKey: 'cda88a7e-8310-30ce-a914-9dd1244322ff',
            oauthClientId:
                'eyJob3N0S2V5IjoiY2NhODhhN2UtODMxMC0zMGNlLWE5MTQtOWRkMTI0NDMyMmZmIiwiYWRkb25LZXkiOiJjb20uYmFsc2FtaXEubW9ja3Vwcy5jb25mbHVlbmNlLnN0YWdpbmcifQ==',
            publicKey:
                'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAorAIfPTE8mqeR1cMEkHlUsKPGpxoJMTq0+GGeZSVBC1glxbX1Jg/dsA6Rw6tFa1P/vyDp6NKuLomjmxjSeqpNadRYjY96KTa7NJTgPmS0bwmUp9c+ThwmVokbSWAYmEFPhwjbEHRzjUPubu3ihhuvj/IXsK+dl2/pJehZEAHZ9CunutbFVgp+buonc05CE325R6Ez1OG+Dvk0nMY4zCV5qP4Io/85r4q/hTYJBdjJ9R4jZcfDH7G5m6+itGuFZ1+5MR8AqD8XfVOqGieTOugZdsJ9TkTg5lWtXuI9LUhk9bt2KdnsMM4uL+460R1UEGi2GJ8D4nPe3cIygy7WOo3RwIDAQAB',
            sharedSecret: 'ATCO2lFgVfxDeTnTRd2ZXipjIKzkJDgBnsucC1i311_WHCdL934x7juzxO6nHxDCenEO_m_0B3gOKEi25vAd1NgcNA3F1A4827',
            serverVersion: '6452',
            pluginsVersion: '1000.0.0.cb8eab2fafe3',
            baseUrl: 'https://some-customer-server.atlassian.net/wiki',
            productType: 'confluence',
            description: 'Atlassian Confluence at https://some-customer-server.atlassian.net/wiki',
            eventType: 'installed',
            displayUrl: 'https://some-customer-server.atlassian.net/wiki',
        };

        setup(async function () {
            const res = await bas.postCustomAPI({
                kind: 'confluence',
                api: 'installed',
                data: installationData,
            });
            expect(res.status).to.equal(200);
        });

        suite('endpoint API', function () {
            const basApi = '/confluence/endpoint/';
            const resourceName = 'test.txt';

            // This creates a JWT token for Confluence API calls.
            // See https://bitbucket.org/atlassian/atlassian-jwt-js/src/master/
            const req: jwt.Request = jwt.fromMethodAndUrl('GET', basApi + resourceName);
            const now = Math.round(Date.now() / 1000); // Unix time in seconds
            const tokenData = {
                iss: installationData.clientKey,
                iat: now,
                exp: now + 180,
                qsh: jwt.createQueryStringHash(req), // [Query String Hash](https://developer.atlassian.com/cloud/jira/platform/understanding-jwt/#a-name-qsh-a-creating-a-query-string-hash)
            };
            const token = jwt.encodeSymmetric(tokenData, installationData.sharedSecret, jwt.SymmetricAlgorithm.HS512);

            test('JWT missing', async function () {
                const res = await bas.getCustomAPI({
                    kind: 'confluence',
                    api: `endpoint/${resourceName}`,
                    params: {},
                });
                expect(res.status).to.equal(401);
            });

            test('JWT bad', async function () {
                const res = await bas.getCustomAPI({
                    kind: 'confluence',
                    api: `endpoint/${resourceName}`,
                    params: {
                        jwt: 'BAD_TOKEN',
                    },
                });
                expect(res.status).to.equal(401);
            });

            test('resource does not match', async function () {
                const res = await bas.getCustomAPI({
                    kind: 'confluence',
                    api: `endpoint/BAD_RESOURCE`, // This should match the url used in jwt.fromMethodAndUrl
                    params: {
                        jwt: token,
                    },
                });
                expect(res.status).to.equal(401);
            });

            test('secret does not match', async function () {
                const res = await bas.getCustomAPI({
                    kind: 'confluence',
                    api: `endpoint/${resourceName}`,
                    params: {
                        jwt: jwt.encodeSymmetric(tokenData, 'BAD_SECRET', jwt.SymmetricAlgorithm.HS512),
                    },
                });
                expect(res.status).to.equal(401);
            });

            test('everything ok', async function () {
                const res = await bas.getCustomAPI({
                    kind: 'confluence',
                    api: `endpoint/${resourceName}`,
                    params: {
                        jwt: token,
                    },
                });
                expect(res.text).to.equal(testContext.staticServerMock.txtResourceContent);
            });
        });

        suite('custom APIs', function () {
            const basApi = '/confluence/'; // This is the base URL for custom APIs
            const resourceName = 'test.txt';

            // This creates a JWT token for Confluence API calls.
            // See https://bitbucket.org/atlassian/atlassian-jwt-js/src/master/
            const req: jwt.Request = jwt.fromMethodAndUrl('GET', basApi + resourceName);
            const now = Math.round(Date.now() / 1000); // Unix time in seconds
            const tokenData = {
                iss: installationData.clientKey,
                iat: now,
                exp: now + 180,
                qsh: jwt.createQueryStringHash(req), // [Query String Hash](https://developer.atlassian.com/cloud/jira/platform/understanding-jwt/#a-name-qsh-a-creating-a-query-string-hash)
            };
            const token = jwt.encodeSymmetric(tokenData, installationData.sharedSecret, jwt.SymmetricAlgorithm.HS512);

            test('render', async function () {
                const res = await bas.getCustomAPI({
                    kind: 'confluence',
                    api: 'render',
                    params: {
                        permalinkID: '1234567890',
                    },
                    token,
                });
                expect(res.status).to.equal(200);
            });
        });
    });
});
