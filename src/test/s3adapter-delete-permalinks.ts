/** @prettier */
import { expect } from 'chai';
import type { S3PermalinksImageStorageAdapter } from '../s3adapter.ts';

suite('S3 Adapter Delete Permalinks', () => {
    let permalinkImageStorageAdapter!: S3PermalinksImageStorageAdapter;

    setup(async function () {
        ({ permalinkImageStorageAdapter } = testContext);
    });

    test('should handle permalinkIDs smaller than 500', async function () {
        // Generate an array with 500 elements
        const permalinkIDs = Array.from({ length: 500 }, (_, index) => index).map((id) => id.toString());

        // Call the function
        const result = await permalinkImageStorageAdapter.deletePermalinkImages({
            platformKind: 'your-platform-kind',
            permalinkIDs,
        });

        // Assertions
        expect(result).to.be.an('array');
        expect(result).to.have.lengthOf(1);

        // Ensure s3.deleteObjects was called one time
        expect(testContext.s3Mock.methodCalls['deleteObjects'].length).to.equal(1);
    });

    test('should handle permalinkIDs greater than 500', async function () {
        // Generate an array with more than 500 elements
        const permalinkIDs = Array.from({ length: 1500 }, (_, index) => index).map((id) => id.toString());

        // Call the function
        const result = await permalinkImageStorageAdapter.deletePermalinkImages({
            platformKind: 'your-platform-kind',
            permalinkIDs,
        });

        // Assertions
        expect(result).to.be.an('array');
        expect(result).to.have.lengthOf(3); // Three chunks of 500 each

        // Ensure s3.deleteObjects was called three times (once for each chunk)
        expect(testContext.s3Mock.methodCalls['deleteObjects'].length).to.equal(3);
    });

    test('should handle thumbnails with permalinkIDs smaller than 500', async function () {
        // Generate an array with three elements
        const permalinkIDs = Array.from({ length: 3 }, (_, index) => index).map((id) => id.toString());

        // Call the function
        const result = await permalinkImageStorageAdapter.deletePermalinkThumbnailImages({
            platformKind: 'your-platform-kind',
            permalinkIDs,
        });

        // Assertions
        expect(result).to.be.an('array');
        expect(result).to.have.lengthOf(1);

        // Ensure s3.deleteObjects was called one time
        expect(testContext.s3Mock.methodCalls['deleteObjects'].length).to.equal(1);
    });

    test('should handle thumbnails with permalinkIDs greater than 500', async function () {
        // Generate an array with 1000 elements
        const permalinkIDs = Array.from({ length: 1000 }, (_, index) => index).map((id) => id.toString());

        // Call the function
        const result = await permalinkImageStorageAdapter.deletePermalinkThumbnailImages({
            platformKind: 'your-platform-kind',
            permalinkIDs,
        });

        // Assertions
        expect(result).to.be.an('array');
        expect(result).to.have.lengthOf(2); // Two chunks of 500 each

        // Ensure s3.deleteObjects was called twice (once for each chunk)
        expect(testContext.s3Mock.methodCalls['deleteObjects'].length).to.equal(2);
    });

    test('should delete objects from the correct region and bucket', async function () {
        // --- deletePermalinkImages -------------------------------------------

        testContext.s3Mock.methodCalls = {};
        await permalinkImageStorageAdapter.deletePermalinkImages({
            platformKind: 'your-platform-kind',
            permalinkIDs: ['some-permalink-id'],
            // dataResidency: undefined,
        });
        expect(testContext.s3Mock.methodCalls['deleteObjects'].length).to.equal(1);
        expect(testContext.s3Mock.methodCalls['deleteObjects'][0].arguments.region).to.equal('us-region');
        expect(testContext.s3Mock.methodCalls['deleteObjects'][0].arguments.params.Bucket).to.equal('us-permalink-bucket');

        testContext.s3Mock.methodCalls = {};
        await permalinkImageStorageAdapter.deletePermalinkImages({
            platformKind: 'your-platform-kind',
            permalinkIDs: ['some-permalink-id'],
            dataResidency: 'us',
        });
        expect(testContext.s3Mock.methodCalls['deleteObjects'].length).to.equal(1);
        expect(testContext.s3Mock.methodCalls['deleteObjects'][0].arguments.region).to.equal('us-region');
        expect(testContext.s3Mock.methodCalls['deleteObjects'][0].arguments.params.Bucket).to.equal('us-permalink-bucket');

        testContext.s3Mock.methodCalls = {};
        await permalinkImageStorageAdapter.deletePermalinkImages({
            platformKind: 'your-platform-kind',
            permalinkIDs: ['some-permalink-id'],
            dataResidency: 'eu',
        });
        expect(testContext.s3Mock.methodCalls['deleteObjects'].length).to.equal(1);
        expect(testContext.s3Mock.methodCalls['deleteObjects'][0].arguments.region).to.equal('eu-region');
        expect(testContext.s3Mock.methodCalls['deleteObjects'][0].arguments.params.Bucket).to.equal('eu-permalink-bucket');

        testContext.s3Mock.methodCalls = {};
        try {
            await permalinkImageStorageAdapter.deletePermalinkImages({
                platformKind: 'your-platform-kind',
                permalinkIDs: ['some-permalink-id'],
                //@ts-ignore // this is a test to check the error handling for bad dataResidency
                dataResidency: 'unexisting',
            });
            expect.fail('should have thrown an error');
        } catch (err) {
            expect(err).to.be.an('error');
        }

        // --- deletePermalinkThumbnailImages -------------------------------------------

        testContext.s3Mock.methodCalls = {};
        await permalinkImageStorageAdapter.deletePermalinkThumbnailImages({
            platformKind: 'your-platform-kind',
            permalinkIDs: ['some-permalink-id'],
            // dataResidency: undefined,
        });
        expect(testContext.s3Mock.methodCalls['deleteObjects'].length).to.equal(1);
        expect(testContext.s3Mock.methodCalls['deleteObjects'][0].arguments.region).to.equal('us-region');
        expect(testContext.s3Mock.methodCalls['deleteObjects'][0].arguments.params.Bucket).to.equal('us-permalink-bucket');

        testContext.s3Mock.methodCalls = {};
        await permalinkImageStorageAdapter.deletePermalinkThumbnailImages({
            platformKind: 'your-platform-kind',
            permalinkIDs: ['some-permalink-id'],
            dataResidency: 'us',
        });
        expect(testContext.s3Mock.methodCalls['deleteObjects'].length).to.equal(1);
        expect(testContext.s3Mock.methodCalls['deleteObjects'][0].arguments.region).to.equal('us-region');
        expect(testContext.s3Mock.methodCalls['deleteObjects'][0].arguments.params.Bucket).to.equal('us-permalink-bucket');

        testContext.s3Mock.methodCalls = {};
        await permalinkImageStorageAdapter.deletePermalinkThumbnailImages({
            platformKind: 'your-platform-kind',
            permalinkIDs: ['some-permalink-id'],
            dataResidency: 'eu',
        });
        expect(testContext.s3Mock.methodCalls['deleteObjects'].length).to.equal(1);
        expect(testContext.s3Mock.methodCalls['deleteObjects'][0].arguments.region).to.equal('eu-region');
        expect(testContext.s3Mock.methodCalls['deleteObjects'][0].arguments.params.Bucket).to.equal('eu-permalink-bucket');

        testContext.s3Mock.methodCalls = {};
        try {
            await permalinkImageStorageAdapter.deletePermalinkThumbnailImages({
                platformKind: 'your-platform-kind',
                permalinkIDs: ['some-permalink-id'],
                //@ts-ignore // this is a test to check the error handling for bad dataResidency
                dataResidency: 'unexisting',
            });
            expect.fail('should have thrown an error');
        } catch (err) {
            expect(err).to.be.an('error');
        }
    });
});
