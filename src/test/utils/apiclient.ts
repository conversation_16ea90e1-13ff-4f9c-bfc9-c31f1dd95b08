/** @prettier */
import request from 'supertest';
import type { Application } from 'express';
import assert from 'node:assert';

interface UserInfo {
    name: string;
    displayName: string;
    avatarURL: string;
    userId: string;
}

// Utility class used for testing BAS API
class BASApiClient {
    app: Application;

    kind?: string;
    userInfo?: UserInfo;
    displayName?: string;
    platformArchiveID?: string | number | null;
    userName?: string;
    adminUser?: string;
    adminSecret?: string;
    getPlatformToken?: (params: unknown) => string;

    platformSiteID?: string | number | null;
    platformTokenExtra: unknown;
    archiveAttributes: unknown;
    platformInfo: unknown;
    headers: Record<string, string | string[]>;

    constructor(
        app: Application,
        params: {
            kind?: string;
            userInfo?: UserInfo;
            displayName?: string;
            platformArchiveID?: string | number;
            getPlatformToken?: (params: unknown) => string;
            adminUser?: string;
            adminSecret?: string;
            siteID?: string | number | null;
            platformTokenExtra?: unknown;
            archiveAttributes?: unknown;
            platformInfo?: unknown;
            headers?: Record<string, string | string[]>;
            userName?: string;
        } = {}
    ) {
        this.app = app;
        this.kind = params.kind;
        this.userInfo = params.userInfo;
        this.displayName = params.displayName;
        this.platformArchiveID = params.platformArchiveID;
        this.getPlatformToken = params.getPlatformToken;
        this.adminUser = params.adminUser;
        this.adminSecret = params.adminSecret;
        this.userName = params.userName;

        this.platformSiteID = params.siteID ?? null;
        this.platformTokenExtra = params.platformTokenExtra ?? {};
        this.archiveAttributes = params.archiveAttributes ?? {};
        this.platformInfo = params.platformInfo ?? {};
        this.headers = params.headers ?? {};
    }

    encodeQueryParams(params: Record<string, string>) {
        const queryParams = new URLSearchParams(params);
        return queryParams.toString();
    }

    async health({
        stat,
        db,
        webdemo,
        reset,
        serverID,
        basicAuth,
    }: { stat?: boolean; db?: boolean; webdemo?: boolean; reset?: boolean | string; serverID?: string; basicAuth?: boolean } = {}) {
        let params = '';
        if (stat) {
            params = '?stat=true';
        } else if (db) {
            params = '?db=true';
        } else if (webdemo) {
            params = '?webdemo=true';
        } else if (reset) {
            params = `?reset=${reset}&serverID=${serverID}`;
        }

        let req = request(this.app)
            .get('/health' + params)
            .set(this.headers);

        if (basicAuth) {
            assert(this.adminUser && this.adminSecret, 'Missing adminUser or adminSecret');
            req = req.auth(this.adminUser, this.adminSecret);
        }

        return await req;
    }

    async metrics() {
        return await request(this.app).get('/metrics');
    }

    async heartbeat({ userID, channelID }: { userID: string; channelID: string }) {
        return await request(this.app)
            .get(`/heartbeat?channelID=${channelID}&userID=${userID}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers);
    }

    async setPlatformArchiveName({ token, platformArchiveName }: { token: string; platformArchiveName: string }) {
        return await request(this.app)
            .post(`/setPlatformArchiveName?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                platformArchiveName,
            });
    }

    async setArchiveAttributes({ token, attributes }: { token: string; attributes: object }) {
        return await request(this.app)
            .post(`/setArchiveAttributes?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                attributes: JSON.stringify(attributes),
            });
    }

    async postCustomAPI({ kind, api, data, basicAuth }: { kind: string; api: string; data?: object | string; basicAuth?: string }) {
        let req = request(this.app).post(`/${kind}/${api}`);
        if (basicAuth === 'admin') {
            assert(this.adminUser && this.adminSecret, 'Missing adminUser or adminSecret');
            req = req.auth(this.adminUser, this.adminSecret);
        }
        req = req.set('Accept', 'application/json');
        if (data) {
            req = req.set('Content-Type', 'application/x-www-form-urlencoded');
            req = req.send(data);
        }
        return await req;
    }

    async getCustomAPI({ kind, api, params, token }: { kind: string; api: string; params?: Record<string, string>; token?: string }) {
        let req = request(this.app).get(`/${kind}/${api}`);
        if (params) {
            const queryString = this.encodeQueryParams(params);
            req = req.query(queryString);
        }
        if (token) {
            req = req.auth(token, { type: 'bearer' });
        }
        req = req.set('Accept', 'application/json');
        return await req;
    }

    async create() {
        assert(this.getPlatformToken, 'Missing getPlatformToken function');
        return await request(this.app)
            .post(`/create`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                platformToken: this.getPlatformToken(this.platformTokenExtra),
                platformSiteID: this.platformSiteID,
                platformArchiveID: this.platformArchiveID,
                platformArchiveName: null,
                platformInfo: null,
                kind: this.kind,
            });
    }

    async createMultipart(bmprPath: string) {
        assert(this.getPlatformToken, 'Missing getPlatformToken function');
        return await request(this.app)
            .post(`/create`)
            .set(this.headers)
            .field('platformToken', this.getPlatformToken(this.platformTokenExtra))
            .field('platformSiteID', this.platformSiteID ?? '')
            .field('platformArchiveID', this.platformArchiveID ?? '')
            .field('kind', this.kind ?? '')
            .field('platformInfo', JSON.stringify(this.platformInfo))
            .field('archiveAttributes', JSON.stringify(this.archiveAttributes))
            .attach('bmpr', bmprPath);
    }

    async open({ skipThumbnailImage } = { skipThumbnailImage: false }) {
        assert(this.getPlatformToken, 'Missing getPlatformToken function');
        return await request(this.app)
            .post(`/open`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                platformToken: this.getPlatformToken(this.platformTokenExtra),
                platformSiteID: this.platformSiteID,
                platformArchiveID: this.platformArchiveID,
                platformArchiveName: null,
                platformInfo: null,
                kind: this.kind,

                skipThumbnailImage: skipThumbnailImage,
                permissions: 4,
                userInfo: this.userInfo ? JSON.stringify(this.userInfo) : undefined,
                userName: this.userName ? this.userName : undefined,
                branchID: 'All',
            });
    }

    async getBranchAttributes({ token, branchID }: { token: string; branchID: string }) {
        return await request(this.app).get(`/getBranchAttributes?token=${token}&branchID=${branchID}`).set(this.headers);
    }

    async setBranchAttributes({ token, branchID, attributes }: { token: string; branchID: string; attributes: object }) {
        return await request(this.app)
            .post(`/setBranchAttributes?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                branchID: branchID,
                attributes: JSON.stringify(attributes),
            });
    }

    async createResource({
        token,
        branchID,
        resourceID,
        data,
        attributes,
    }: {
        token: string;
        branchID: string;
        resourceID: string;
        data: string;
        attributes: object;
    }) {
        return await request(this.app)
            .post(`/createResource?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                branchID,
                resourceID,
                attributes: JSON.stringify(attributes),
                data,
            });
    }

    async getResourceData({ token, branchID, resourceID }: { token: string; branchID: string; resourceID: string }) {
        return await request(this.app)
            .get(`/getResourceData?token=${token}&branchID=${branchID}&resourceID=${resourceID}`)
            .set(this.headers);
    }

    async getResourceAttributes({ token, branchID, resourceID }: { token: string; branchID: string; resourceID: string }) {
        return await request(this.app)
            .get(`/getResourceAttributes?token=${token}&branchID=${branchID}&resourceID=${resourceID}`)
            .set(this.headers);
    }

    async setResourceData({ token, branchID, resourceID, data }: { token: string; branchID: string; resourceID: string; data: string }) {
        return await request(this.app)
            .post(`/setResourceData?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                branchID,
                resourceID,
                data,
            });
    }

    async setResourceAttributes({
        token,
        branchID,
        resourceID,
        attributes,
    }: {
        token: string;
        branchID: string;
        resourceID: string;
        attributes: object;
    }) {
        return await request(this.app)
            .post(`/setResourceAttributes?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                branchID,
                resourceID,
                attributes: JSON.stringify(attributes),
            });
    }

    async deleteResources({ token, branchID, resourceIDs }: { token: string; branchID: string; resourceIDs: string[] }) {
        return await request(this.app)
            .post(`/deleteResources?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                branchID,
                resourceIDs: JSON.stringify(resourceIDs),
            });
    }

    async createBranch({ token, branchID, attributes }: { token: string; branchID: string; attributes: object }) {
        return await request(this.app)
            .post(`/createBranch?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                branchID: branchID,
                attributes: JSON.stringify(attributes),
            });
    }

    async deleteBranches({ token, branchIDs }: { token: string; branchIDs: string[] }) {
        return await request(this.app)
            .post(`/deleteBranches?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                branchIDs: JSON.stringify(branchIDs),
            });
    }

    async setResourceBranchID({
        token,
        resourceID,
        oldBranchID,
        newBranchID,
    }: {
        token: string;
        resourceID: string;
        oldBranchID: string;
        newBranchID: string;
    }) {
        return await request(this.app)
            .post(`/setResourceBranchID?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                resourceID,
                oldBranchID,
                newBranchID,
            });
    }

    async getResourcesData({ resources, archiveRevision, token }: { resources: { ID: string }[]; archiveRevision: number; token: string }) {
        return await request(this.app)
            .post(`/getResourcesData?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                branchID: 'All',
                archiveRevision: archiveRevision,
                resourceIDs: JSON.stringify(resources.map((r) => r.ID)),
            });
    }

    async getUsersList({ token }: { token: string }) {
        return await request(this.app)
            .get(`/getUsersList?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers);
    }

    async download({ token }: { token: string }) {
        return await request(this.app)
            .get(`/download?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers);
    }

    async fetch({ token, url }: { token: string; url: string }) {
        return await request(this.app)
            .get(`/fetch?${this.encodeQueryParams({ token, url })}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers);
    }

    async logUserEvent({ token, userEvent }: { token?: string; userEvent: object }) {
        return await request(this.app)
            .post(`/logUserEvent${token ? `?token=${token}` : ''}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                userEvent: JSON.stringify(userEvent),
            });
    }

    async getHeuristicArchiveSize({ token }: { token: string }) {
        return await request(this.app)
            .get(`/getHeuristicArchiveSize?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers);
    }

    async getProjectMembers({ token, data }: { token: string; data: object }) {
        return await request(this.app)
            .post(`/getProjectMembers?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                data: JSON.stringify(data),
            });
    }

    async close({ token }: { token: string }) {
        return await request(this.app)
            .post(`/close?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers);
    }

    async delete({ token }: { token: string }) {
        assert(this.getPlatformToken, 'Missing getPlatformToken function');
        let req = request(this.app)
            .post(`/delete?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers);

        if (this.adminSecret && this.adminUser) {
            req = req.auth(this.adminUser, this.adminSecret);
        }

        return await req.send({
            platformToken: this.getPlatformToken(this.platformTokenExtra),
            platformSiteID: this.platformSiteID,
            platformArchiveID: this.platformArchiveID,
            kind: this.kind,
        });
    }

    async deleteOffline({
        platformSiteID,
        platformArchiveID,
        platformKind,
    }: {
        platformSiteID?: string;
        platformArchiveID?: string;
        platformKind: string;
    }) {
        assert(this.adminSecret && this.adminUser, 'Missing adminSecret or adminUser');
        return await request(this.app)
            .post(`/deleteOffline`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .auth(this.adminUser, this.adminSecret)
            .send({ platformSiteID, platformArchiveID, platformKind });
    }

    async projectExistsOnPlatform({
        platformSiteID,
        platformArchiveID,
        kind,
    }: {
        platformSiteID?: string;
        platformArchiveID?: string;
        kind: string;
    }) {
        assert(this.adminSecret && this.adminUser, 'Missing adminSecret or adminUser');
        return await request(this.app)
            .post(`/projectExistsOnPlatform`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .auth(this.adminUser, this.adminSecret)
            .send({ platformSiteID, platformArchiveID, kind });
    }

    async flushOffline({ platformSiteID, platformArchiveID }: { platformSiteID?: string; platformArchiveID?: string }) {
        assert(this.adminSecret && this.adminUser, 'Missing adminSecret or adminUser');
        return await request(this.app)
            .post(`/flushOffline`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .auth(this.adminUser, this.adminSecret)
            .send({ platformSiteID, platformArchiveID });
    }

    async unloadArchive({
        platformSiteID,
        platformArchiveID,
        kind,
    }: {
        platformSiteID?: string;
        platformArchiveID?: string;
        kind: string;
    }) {
        assert(this.adminSecret && this.adminUser, 'Missing adminSecret or adminUser');
        assert(this.getPlatformToken, 'Missing getPlatformToken function');
        return await request(this.app)
            .post(`/unloadArchive`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .auth(this.adminUser, this.adminSecret)
            .send({
                platformSiteID,
                platformArchiveID,
                platformToken: this.getPlatformToken(this.platformTokenExtra),
                kind,
            });
    }

    async unloadArchiveOffline({ platformArchiveID, platformSiteID }: { platformArchiveID?: string; platformSiteID?: string }) {
        assert(this.adminSecret && this.adminUser, 'Missing adminSecret or adminUser');
        return await request(this.app)
            .post(`/unloadArchiveOffline`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .auth(this.adminUser, this.adminSecret)
            .send({
                platformSiteID,
                platformArchiveID,
            });
    }

    async unloadArchiveIfNotSync({
        platformSiteID,
        platformArchiveID,
        kind,
        modifiedTimestamp,
    }: {
        platformSiteID?: string;
        platformArchiveID?: string;
        kind: string;
        modifiedTimestamp: number;
    }) {
        assert(this.adminSecret && this.adminUser, 'Missing adminSecret or adminUser');
        assert(this.getPlatformToken, 'Missing getPlatformToken function');
        return await request(this.app)
            .post(`/unloadArchiveIfNotSync`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .auth(this.adminUser, this.adminSecret)
            .send({
                platformSiteID,
                platformArchiveID,
                platformToken: this.getPlatformToken(this.platformTokenExtra),
                kind,
                modifiedTimestamp,
            });
    }

    async flush({ token, force }: { token: string; force: boolean }) {
        return await request(this.app)
            .get(`/flush?token=${token}&force=${force}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers);
    }

    async broadcastMessage({ token, message }: { token: string; message: object }) {
        return await request(this.app)
            .post(`/broadcastMessage?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                message: JSON.stringify(message),
            });
    }

    async createMyUser({ token, updatedUserInfo }: { token: string; updatedUserInfo: object }) {
        return await request(this.app)
            .post(`/createMyUser?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                updatedUserInfo: JSON.stringify(updatedUserInfo),
            });
    }

    async updateMyUser({ token, updatedUserInfo }: { token: string; updatedUserInfo: object }) {
        return await request(this.app)
            .post(`/updateMyUser?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                updatedUserInfo: JSON.stringify(updatedUserInfo),
            });
    }

    async getArchiveUsersList({ token }: { token: string }) {
        return await request(this.app)
            .get(`/getArchiveUsersList?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers);
    }

    async getArchiveRevision({ token }: { token: string }) {
        return await request(this.app)
            .get(`/getArchiveRevision?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers);
    }

    async getUserInfo({ token, internalID }: { token: string; internalID: string }) {
        return await request(this.app)
            .get(`/getUserInfo?token=${token}&internalID=${internalID}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers);
    }

    async updateUserInfo({ token, userInfo }: { token: string; userInfo: object }) {
        return await request(this.app)
            .post(`/updateUserInfo?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send(userInfo);
    }

    async uploadTempFile({ token, filename, mimetype, data }: { token: string; filename: string; mimetype: string; data: string }) {
        return await request(this.app)
            .post(`/uploadTempFile?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                filename,
                mimetype,
                data,
            });
    }

    async getTOC({ token, branchID }: { token: string; branchID: string }) {
        return await request(this.app).get(`/getTOC?token=${token}&branchID=${branchID}`).set(this.headers);
    }

    async downloadTempFile({ token, key }: { token: string; key: string }) {
        return await request(this.app).get(`/downloadTempFile?token=${token}&key=${key}`).set(this.headers);
    }

    async createComment({
        token,
        commentID,
        resourceID,
        branchID,
        commentParentID,
        data,
    }: {
        token: string;
        commentID: string;
        resourceID: string;
        branchID: string;
        commentParentID: string | null;
        data: object;
    }) {
        return await request(this.app)
            .post(`/createComment?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                token,
                commentID,
                resourceID,
                branchID,
                commentParentID,
                data: JSON.stringify(data),
            });
    }

    async getCommentsData({ token, commentIDs }: { token: string; commentIDs: string[] }) {
        return await request(this.app)
            .post(`/getCommentsData?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                token,
                commentIDs: JSON.stringify(commentIDs),
            });
    }

    async setCommentData({ token, commentID, data }: { token: string; commentID: string; data: object }) {
        return await request(this.app)
            .post(`/setCommentData?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                token,
                commentID: commentID,
                data: JSON.stringify(data),
            });
    }

    async deleteComments({ token, commentIDs }: { token: string; commentIDs: string[] }) {
        return await request(this.app)
            .post(`/deleteComments?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                commentIDs: JSON.stringify(commentIDs),
            });
    }

    // TODO: calling this API raise an error in BAR. Check if deprecated.
    async updateCommentAttributes({ token, commentID, attributes }: { token: string; commentID: string; attributes: object }) {
        return await request(this.app)
            .post(`/updateCommentAttributes?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                commentID: commentID,
                attributes: JSON.stringify(attributes),
            });
    }

    async updateCommentsAttributes({ token, commentIDs, attributes }: { token: string; commentIDs: string[]; attributes: object }) {
        return await request(this.app)
            .post(`/updateCommentsAttributes?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                commentIDs: JSON.stringify(commentIDs),
                attributes: JSON.stringify(attributes),
            });
    }

    async getThumbnail({ token, thumbnailID }: { token: string; thumbnailID: string }) {
        return await request(this.app)
            .get(`/getThumbnail?token=${token}&thumbnailID=${thumbnailID}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers);
    }

    async createThumbnail({ token, thumbnailID, attributes }: { token: string; thumbnailID: string; attributes: object }) {
        return await request(this.app)
            .post(`/createThumbnail?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                thumbnailID: thumbnailID,
                attributes: JSON.stringify(attributes),
            });
    }

    async setThumbnail({
        token,
        thumbnailID,
        attributes,
        archiveRevision,
    }: {
        token: string;
        thumbnailID: string;
        attributes: object;
        archiveRevision: number;
    }) {
        return await request(this.app)
            .post(`/setThumbnail?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                thumbnailID: thumbnailID,
                attributes: JSON.stringify(attributes),
                archiveRevision,
            });
    }

    async deleteThumbnails({ token, thumbnailIDs }: { token: string; thumbnailIDs: string[] }) {
        return await request(this.app)
            .post(`/deleteThumbnails?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                thumbnailIDs: JSON.stringify(thumbnailIDs),
            });
    }

    async refreshSession({ token, platformToken = undefined }: { token: string; platformToken?: string }) {
        return await request(this.app)
            .post(`/refreshSession?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                platformToken,
            });
    }

    async getUsersInfoAndSettings({ userId }: { userId: string | number }) {
        return await request(this.app)
            .post(`/getUsersInfoAndSettings`)
            .set(this.headers)
            .send({
                data: [
                    {
                        projectId: this.platformArchiveID,
                        userId: userId,
                    },
                ],
                platformKind: this.kind,
            });
    }

    async setPermalink({
        token,
        resourceID,
        branchID,
        check,
        permalinkInfo,
        permalinkKind,
    }: {
        token: string;
        resourceID?: string | null;
        branchID?: string | null;
        check?: boolean;
        permalinkInfo?: object;
        permalinkKind?: string;
    }) {
        return await request(this.app)
            .post(`/setPermalinkData?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                resourceID,
                branchID,
                check,
                permalinkKind,
                permalinkInfo: permalinkInfo ? JSON.stringify(permalinkInfo) : undefined,
            });
    }

    async createOrUpdateImageLink({
        token,
        resourceID,
        branchID,
        permalinkInfo,
        imagePath,
    }: {
        token: string;
        resourceID: string;
        branchID: string;
        permalinkInfo?: object;
        imagePath?: string;
    }) {
        let res = request(this.app)
            .post(`/createOrUpdateImageLink?token=${token}&resourceID=${resourceID}&branchID=${branchID}`)
            .set(this.headers)
            .field('resourceID', resourceID)
            .field('branchID', branchID);
        if (permalinkInfo) {
            res.field('permalinkInfo', JSON.stringify(permalinkInfo));
        }
        if (imagePath) {
            res.attach('image', imagePath);
        }
        return await res;
    }

    async createSnapshot({
        platformArchiveID,
        platformSiteID,
        platformKind,
        resourceID,
        branchID,
        platformInfo,
    }: {
        platformArchiveID?: string;
        platformSiteID?: string;
        platformKind: string;
        resourceID: string;
        branchID: string;
        platformInfo: object;
    }) {
        assert(this.adminSecret && this.adminUser, 'Missing adminSecret or adminUser');
        let res = request(this.app)
            .post(`/createSnapshot`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .auth(this.adminUser, this.adminSecret)
            .send({
                resourceID,
                branchID,
                platformSiteID,
                platformArchiveID,
                platformKind,
                platformInfo,
            });

        return await res;
    }

    async setPermalinkAsBASAdmin({
        resourceID,
        branchID,
        platformSiteID,
        platformArchiveID,
        platformKind,
        permalinkInfo,
        platformInfo,
    }: {
        resourceID: string;
        branchID: string;
        platformSiteID?: string;
        platformArchiveID?: string;
        platformKind: string;
        permalinkInfo?: object;
        platformInfo?: object;
    }) {
        assert(this.adminSecret && this.adminUser, 'Missing adminSecret or adminUser');
        return await request(this.app)
            .post(`/setPermalinkData`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .auth(this.adminUser, this.adminSecret)
            .send({
                resourceID,
                branchID,
                platformSiteID,
                platformArchiveID,
                platformKind,
                permalinkInfo: permalinkInfo ? JSON.stringify(permalinkInfo) : undefined,
                platformInfo: platformInfo ? JSON.stringify(platformInfo) : undefined,
            });
    }

    async getPermalink({ id, query, platformToken }: { id: string; query?: string; platformToken?: string }) {
        return await request(this.app)
            .get(`/permalink?ID=${id}` + (query ? `&query=${query}` : ``) + (platformToken ? `&platformToken=${platformToken}` : ``))
            .set(this.headers);
    }

    async getSnapshot({ id }: { id: string }) {
        return await request(this.app).get(`/slack/${id}`).set(this.headers);
    }

    async deletePermalinkData({
        token,
        resourceID,
        branchID,
        permalinkID,
    }: {
        token: string;
        resourceID?: string;
        branchID?: string;
        permalinkID?: string;
    }) {
        // Either use (branchID, resourceID) or permalinkID
        return await request(this.app)
            .post(`/deletePermalinkData?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({
                resourceID,
                branchID,
                permalinkID,
            });
    }

    async getPermalinksData({ token }: { token: string }) {
        return await request(this.app)
            .get(`/getPermalinksData?token=${token}`)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers)
            .send({ token });
    }

    async getPermalinkData({
        token,
        resourceID,
        branchID,
        permalinkID,
    }: {
        token: string;
        resourceID?: string;
        branchID?: string;
        permalinkID?: string;
    }) {
        let url = `/getPermalinkData?token=${token}`;
        if (resourceID) {
            url += `&resourceID=${resourceID}`;
        }
        if (branchID) {
            url += `&branchID=${branchID}`;
        }
        if (permalinkID) {
            url += `&permalinkID=${permalinkID}`;
        }
        return await request(this.app)
            .get(url)
            .set('Content-Type', 'application/x-www-form-urlencoded')
            .set('Accept', 'application/json')
            .set(this.headers);
    }

    async restore({
        platformArchiveName,
        bmprPath,
        bmprTimestamp,
        basSessionId = 'basSessionId',
    }: {
        platformArchiveName: string;
        bmprPath: string;
        bmprTimestamp: string;
        basSessionId?: string;
    }) {
        assert(this.getPlatformToken, 'Missing getPlatformToken function');
        return await request(this.app)
            .post(`/restore`)
            .set(this.headers)
            .field('platformToken', this.getPlatformToken(this.platformTokenExtra))
            .field('platformSiteID', this.platformSiteID ?? '')
            .field('platformArchiveID', this.platformArchiveID ?? '')
            .field('platformArchiveName', platformArchiveName)
            .field('kind', this.kind ?? '')
            .field('basSessionId', basSessionId)
            .field('bmprTimestamp', bmprTimestamp)
            .attach('bmpr', bmprPath);
    }

    getBlockList() {
        assert(this.adminSecret && this.adminUser, 'Missing adminSecret or adminUser');
        return request(this.app).get('/getBlockList').set(this.headers).auth(this.adminUser, this.adminSecret);
    }

    addToBlockList({ items }: { items?: object[] }) {
        assert(this.adminSecret && this.adminUser, 'Missing adminSecret or adminUser');
        return request(this.app).post('/addToBlockList').set(this.headers).auth(this.adminUser, this.adminSecret).send({ items });
    }

    removeFromBlockList({ items }: { items?: object[] }) {
        assert(this.adminSecret && this.adminUser, 'Missing adminSecret or adminUser');
        return request(this.app).post('/removeFromBlockList').set(this.headers).auth(this.adminUser, this.adminSecret).send({ items });
    }

    getUserKeyFromToken({ platformToken, kind }: { platformToken: string; kind: string }) {
        assert(this.adminSecret && this.adminUser, 'Missing adminSecret or adminUser');
        return request(this.app)
            .get('/getUserKeyFromToken')
            .set(this.headers)
            .query({ platformToken, kind })
            .auth(this.adminUser, this.adminSecret);
    }

    getResourceDataWithCache({
        token,
        branchID,
        resourceID,
        basArchiveID,
    }: {
        token: string;
        branchID: string;
        resourceID: string;
        basArchiveID: string;
    }) {
        return request(this.app)
            .get(`/getResourceData`)
            .query({ branchID, resourceID, basArchiveID })
            .set('Authorization', `Bearer ${token}`)
            .set(this.headers);
    }

    async getUserAuthToken({ token }: { token: string }) {
        return await request(this.app).post(`/getUserAuthToken?token=${token}`).set(this.headers);
    }

    getRTCAuthToken({ token }: { token: string }) {
        return request(this.app).get(`/getRTCAuthToken`).set('Authorization', `Bearer ${token}`).set(this.headers);
    }
}

export { BASApiClient };
