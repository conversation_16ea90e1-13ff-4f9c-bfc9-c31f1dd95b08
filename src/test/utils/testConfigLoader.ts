/** @prettier */
import { staticString } from '@balsamiq/serverconfig/lib/environment-accessors.js';
import { parseRtcConfigSecrets } from '@balsamiq/serverconfig/lib/rtc-config.js';
import { HardcodedBalsamiqSecrets, multipleSecretLists } from '@balsamiq/serverconfig/lib/secrets.js';
import type { Config, ConfigLoaderResult } from '../../configLoader.ts';

function loadTestConfig({
    basAdminUser,
    basAdminSecret,
    cloudServerBaseUrl,
}: {
    basAdminUser: string;
    basAdminSecret: string;
    cloudServerBaseUrl: string;
}): ConfigLoaderResult {
    const config: Config = {
        port: 4001,
        clusterSize: 1,
        mySQLConfig: {
            credentialsSecret: {
                getSecret() {
                    return {
                        host: '127.0.0.1',
                        username: 'root',
                        password: '',
                        port: 3306,
                    };
                },
            },
            region: 'local',
            basDBName: 'BAS_TEST',
            permalinksDBName: 'PERMALINKS_TEST',
        },
        archiveIDPrefix: 'BAS_TEST_',
        appName: 'bas-test',
        baseUrl: 'https://bas.ngrok.io',
        shareUrls: {
            us: 'https://us.testshare.com',
            eu: 'https://eu.testshare.com',
        },
        defaultDataResidencyName: 'us',
        cloudBaseUrl: 'http://localhost:9000',
        archivesPath: '/tmp/archives',
        https: false,
        cloudProjectsMaxAgeInDays: 30,
        unloadUnusedArchivesForConnectors: ['jira', 'confluence', 'cloud', 'wd'],
        connectors: ['cloud', 'confluence', 'jira', 'wd'],
        confluenceNamespace: 'com.balsamiq.mockups.confluence.staging',
        jiraNamespace: 'com.balsamiq.mockups.jira.staging',
        getRtcConfig() {
            return {
                secrets: new HardcodedBalsamiqSecrets(
                    (text) => ({
                        pollingIntervalInMin: 1,
                        data: parseRtcConfigSecrets(text),
                    }),
                    {
                        jwt_secrets: {
                            test: ['secret'],
                        },
                        server_secrets: ['send secret'],
                        heartbeat_interval_in_sec: 10,
                    }
                ),
                websocketsUri: staticString('wefwef'),
                jwtCallbackInfo: {
                    basBaseURL: 'http://127.0.0.1:4000/', // Include trailing slash
                    secret: {
                        type: 'credentials-in-clear', // Only for development!
                        username: basAdminUser,
                        password: basAdminSecret,
                    },
                },
            };
        },

        getServerApiSecrets() {
            return new HardcodedBalsamiqSecrets(multipleSecretLists(), {
                pollingIntervalInMin: 0,
                data: {
                    [basAdminUser]: [basAdminSecret],
                },
            });
        },

        metricNamespace: 'balsamiq/bas-local',
        metricDisabled: true, // for local test
        buildNumber: '999',
        proxyConfig: [
            // This array is populated in testContext using the URL of the Mock Server.
        ],
        redisURL: process.env['REDIS_HOST'] || 'localhost',
        redisPort: 6379,
        redisDB: 1,
        kmsService: {
            region: 'global',
            key_arn: 'alias/test',
            environment: 'test',
        },
        i2wService: {
            url: 'https://api.balsamiq.com/i2w-staging/',
        },
        reduceLogging: false,
        loggerOutputForElasticSearch: false,
        environmentName: 'test',
        metricRegion: 'some-region',
        w2iService: {
            region: 'some-region',
            key_arn: 'some-arn',
        },
        permalinkS3Storage: {
            us: {
                bucketRegion: 'us-region',
                bucketName: 'us-permalink-bucket',
                baseDir: 'us-test-permalinks',
            },
            eu: {
                bucketRegion: 'eu-region',
                bucketName: 'eu-permalink-bucket',
                baseDir: 'eu-test-permalinks',
            },
        },
        cloudConfiguration: {
            cloudServerBaseUrl: cloudServerBaseUrl,
            jwtSecret: 'foobar',
            cloudBasicAuthCredentials: {
                username: 'bas',
                password: 'pass',
            },
        },
    };
    return { config, environment: 'test' };
}

export { loadTestConfig };
