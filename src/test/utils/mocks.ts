/** @prettier */
import { createHash } from 'crypto';
import { Readable, Writable } from 'stream';
import type {
    IS3Adapter,
    PutObjectCommandInput,
    CompleteMultipartUploadCommandOutput,
    GetObjectCommandInput,
    GetObjectCommandOutput,
    HeadObjectCommandInput,
    HeadObjectCommandOutput,
    DeleteObjectsCommandInput,
    DeleteObjectsCommandOutput,
    StreamingBlobPayloadInputTypes,
    StreamingBlobPayloadOutputTypes,
} from '../../s3adapter.ts';
import express from 'express';
import type { Request, Response } from 'express';
import http, { type IncomingHttpHeaders } from 'http';
import type { ILambda } from '../../wireframe2image_adapter.ts';
import { ReadStream } from 'fs';
import { Uint8ArrayBlobAdapter } from '@smithy/util-stream'; // This is a dependency from aws-sdk v3
import path from 'path';
import { expect } from 'chai';
import type { Logger } from '@balsamiq/logging';
import type {
    GetMetricStatisticsCommandInput,
    GetMetricStatisticsCommandOutput,
    PutMetricDataCommandInput,
    PutMetricDataCommandOutput,
} from '@aws-sdk/client-cloudwatch';
import assert from 'assert';
import type { CloudWatchInterface } from '../../metrics.ts';

export const mockThumbnailBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==';

export class S3Mock implements IS3Adapter {
    public buckets: Record<string, Record<string, Buffer>> = {};
    public methodCalls: Record<string, { arguments: any }[]> = {};

    async upload(
        region: string,
        params: PutObjectCommandInput,
        cb: (
            err: { message: string; code: string; region: string; time: Date; extendedRequestId: string } | null,
            data? // data
            : CompleteMultipartUploadCommandOutput
        ) => void
    ): Promise<void> {
        this._addMethodCall('upload', { region, params });

        const { Bucket, Key, Body } = params;
        const bucket = this.buckets[Bucket!] || {};

        const data = await this._readStreamToBuffer(Body as ReadStream);

        bucket[Key!] = data;
        this.buckets[Bucket!] = bucket;
        cb(
            null, // err
            {
                // data
                ETag: `"${createHash('sha256').update(data).digest('base64')}"`,
                VersionId: 'CG612hodqujkf8FaaNfp8U..FIhLROcp',
            } as CompleteMultipartUploadCommandOutput
        );
    }

    headObject(
        region: string,
        params: HeadObjectCommandInput,
        cb: (
            err: { message: string; code: string; region: string; time: Date; extendedRequestId: string } | null,
            data?: HeadObjectCommandOutput
        ) => void
    ): void {
        this._addMethodCall('headObject', { region, params });

        cb(
            null, // err
            {
                // data
            } as HeadObjectCommandOutput
        );
    }

    getObject(region: string, params: GetObjectCommandInput): Promise<GetObjectCommandOutput> {
        this._addMethodCall('getObject', { region, params });

        const { Bucket, Key } = params;
        const bucket = this.buckets[Bucket!] || {};

        let data = bucket[Key!];
        if (!data && Key!.endsWith('_thumbnail.png')) {
            data = Buffer.from(mockThumbnailBase64, 'base64');
        }
        const readable = new Readable();
        readable._read = () => {}; // _read is required but you can noop it
        if (data) {
            readable.push(data);
            readable.push(null);
        } else {
            let err = new Error('The specified key does not exist') as Error & { code: string; statusCode: number };
            err.code = 'NoSuchKey';
            err.statusCode = 404;
            readable.emit('error', err);
        }

        (readable as StreamingBlobPayloadOutputTypes).transformToByteArray = () => {
            return Promise.resolve(data);
        };

        return Promise.resolve({ Body: readable } as GetObjectCommandOutput);
    }

    deleteObjects(
        region: string,
        params: DeleteObjectsCommandInput,
        cb: (
            err: { message: string; code: string; region: string; time: Date; extendedRequestId: string } | null,
            data?: DeleteObjectsCommandOutput
        ) => void
    ): void {
        this._addMethodCall('deleteObjects', { region, params });

        const {
            Bucket,
            Delete: { Objects },
        } = params as DeleteObjectsCommandInput & { Delete: NonNullable<DeleteObjectsCommandInput['Delete']> };
        const bucket = this.buckets[Bucket!];
        if (bucket) {
            for (let { Key } of Objects!) {
                delete bucket[Key!];
            }
        }
        cb(null, {} as DeleteObjectsCommandOutput);
    }

    async _readStreamToBuffer(stream: ReadStream): Promise<Buffer> {
        return await new Promise((resolve, reject) => {
            let bufs: Buffer[] = [];
            stream.on('error', reject);
            stream.on('end', function () {
                resolve(Buffer.concat(bufs));
            });
            stream.on('data', function (d) {
                bufs.push(typeof d === 'string' ? Buffer.from(d) : d);
            });
        });
    }

    private _addMethodCall(method: string, args: any) {
        if (!(method in this.methodCalls)) {
            this.methodCalls[method] = [];
        }

        this.methodCalls[method].push({ arguments: args });
    }
}

export class LambdaMock implements ILambda {
    private _error: any;
    private _payload: string;

    constructor({ error, payload }: { error?: any; payload: string }) {
        this._error = error;
        this._payload = payload;
    }

    set error(value: any) {
        this._error = value;
    }

    set payload(value: string) {
        this._payload = value;
    }

    invoke(_args: never, cb: (err: any, data?: any) => void): void {
        cb(this._error, {
            Payload: Uint8ArrayBlobAdapter.fromString(this._payload),
        });
    }
}

export class StaticServerMock {
    public app: express.Express;
    public httpServer: http.Server;
    public hasSnapshot: boolean;
    public responseHeaders: Record<string, string>;
    public responseStatusCode: number;
    public requestHeaders: IncomingHttpHeaders;
    public srcDirName: string;

    public nextChunkData: string;
    public currentChunk: number;
    public totalChunks: number;

    public serverURL: string | null;
    public txtResourceContent = 'test txt file body';

    constructor(srcDirName: string) {
        const app = express();
        const httpServer = http.createServer(app);

        app.get('/test_image.png', this.testImage.bind(this));
        app.get('/chunks', this.chunks.bind(this));
        app.get('/redirect-to-chunks', (req, res) => res.redirect('/chunks'));
        app.get('/bw-atlassian/share/404_image_permalink.html', this.imagePermalinks404.bind(this));
        app.get('/bw-atlassian/confluence/test.txt', this.txtResource.bind(this));
        app.use(this.notFound);

        this.app = app;
        this.httpServer = httpServer;
        this.serverURL = null;

        this.hasSnapshot = false;
        this.srcDirName = srcDirName;

        // Tests can change the behaviour of this mock by setting this params
        this.responseHeaders = {};
        this.responseStatusCode = 200;
        this.requestHeaders = {};

        this.nextChunkData = 'somedata';
        this.currentChunk = -1;
        this.totalChunks = 0;
    }

    start() {
        return new Promise<void>((resolve, reject) =>
            this.httpServer.listen(0, '127.0.0.1', () => {
                try {
                    const addr = this.httpServer.address() as { port: number; address: string };
                    const address = addr.address;
                    const port = addr.port;
                    this.serverURL = `http://${address}:${port}`;
                } catch (err) {
                    reject(err);
                    return;
                }

                resolve();
            })
        );
    }

    stop() {
        return new Promise((resolve) => this.httpServer.close(resolve));
    }

    notFound = (_req: Request, res: Response) => {
        res.status(404).send('404 Not Found');
    };

    testImage(req: Request, res: Response) {
        const imagePath = path.join(this.srcDirName, 'test/utils/testimage.png');
        res.sendFile(imagePath);
    }

    txtResource(req: Request, res: Response) {
        res.status(200).send(this.txtResourceContent).end();
    }

    imagePermalinks404(req: Request, res: Response) {
        res.status(200).send('404 page body').end();
    }

    async chunks(req: Request, res: Response) {
        this.requestHeaders = req.headers;
        this.currentChunk = -1;
        this.totalChunks = 0;

        const count = Number.parseInt(`${req.query.c}`, 10) || 1;
        const delay = Number.parseInt(`${req.query.delay}`, 10) || 1;
        const destroy = Number.parseInt(`${req.query.destroy}`, 10) || -1;
        const wait = Number.parseInt(`${req.query.wait}`, 10) || 0;

        if (wait > 0) {
            await new Promise((resolve) => setTimeout(resolve, wait));
        }

        this.totalChunks = count;

        async function* generate(this: StaticServerMock) {
            for (let i = 0; i < count; i++) {
                await new Promise((resolve) => setTimeout(resolve, delay));
                if (i === destroy) {
                    throw new Error('Destroy Connection');
                }
                yield this.nextChunkData;
                this.currentChunk = i;
            }
        }

        const readable = Readable.from(generate.bind(this)());

        res.status(this.responseStatusCode);

        for (let [k, v] of Object.entries(this.responseHeaders)) {
            res.append(k, v);
        }

        readable.pipe(res, { end: false });
        readable.on('error', () => {
            // This handles the Destroy exception by truncating the underlying connection.
            res.connection?.destroy();
        });
        readable.on('end', () => res.end());
    }
}

export class CloudServerMock {
    public httpServer: http.Server;
    public hasSnapshot: boolean;
    public apiCalls: { api: string }[];
    public dataResidencyResponse: { dataResidency: string } | null | undefined;
    public cloudBasicAuthCredentials: { username: string; password: string };
    public serverURL: string | null;
    public srcDirName: string;

    constructor({
        cloudBasicAuthCredentials,
        srcDirName,
    }: {
        cloudBasicAuthCredentials: { username: string; password: string };
        srcDirName: string;
    }) {
        this.cloudBasicAuthCredentials = cloudBasicAuthCredentials;

        const app = express();
        const httpServer = http.createServer(app);

        app.get(/\/s(\w*)\/p(\w*)\/bmpr_data/, this.bmprData.bind(this));
        app.post(/\/s(\w*)\/p(\w*)\/set_bas_archive_attributes/, this.setBasArchiveAttributes.bind(this));
        app.get(/\/s(\w*)\/p(\w*)\/has_snapshot/, this.checkHasSnapshot.bind(this));
        app.post(/\/s(\w*)\/p(\w*)\/snapshot/, this.snapshot.bind(this));
        app.post(/\/s(\w*)\/p(\w*)\/notify_user_event/, this.notifyUserEvent.bind(this));
        app.post(/\/s(\w*)\/p(\w*)\/project_members/, this.getProjectMembers.bind(this));
        app.post(/\/s(\w*)\/p(\w*)\/create_token/, this.create_token.bind(this));
        app.get(/\/s(\w*)\/p(\w*)\/data_residency/, this.getDataResidency.bind(this));

        this.httpServer = httpServer;
        this.serverURL = null;
        this.hasSnapshot = false;
        this.apiCalls = [];
        this.dataResidencyResponse = undefined;
        this.srcDirName = srcDirName;
    }

    expectApiCalls(expectedCalls: { api: string }[] = []) {
        expect(this.apiCalls).to.deep.equal(expectedCalls);
        this.apiCalls = [];
    }

    start() {
        return new Promise<void>((resolve) =>
            this.httpServer.listen(0, '127.0.0.1', () => {
                const addr = this.httpServer.address();
                if (addr === null) {
                    throw new Error('Server address is null');
                }
                if (typeof addr === 'string') {
                    throw new Error('Server address is a string');
                }
                const address = addr.address;
                const port = addr.port;
                this.serverURL = `http://${address}:${port}`;
                resolve();
            })
        );
    }

    stop() {
        return new Promise((resolve) => this.httpServer.close(resolve));
    }

    parseRequestParams(req: Request) {
        const { 0: siteIDBase36, 1: archiveIDBase36 } = req.params;
        return {
            siteID: Number.parseInt(siteIDBase36, 36),
            archiveID: Number.parseInt(archiveIDBase36, 36),
        };
    }

    setBasArchiveAttributes(req: Request, res: Response) {
        res.status(200).send('Ok.');
    }

    checkHasSnapshot(req: Request, res: Response) {
        // const params = this.parseRequestParams(req);
        res.send({ hasSnapshot: this.hasSnapshot });
        res.end();
    }

    snapshot(req: Request, res: Response) {
        // CAVEAT: you must consume all the data from the request before replying (EPIPE error otherwise)
        req.pipe(
            new Writable({
                write(_chunk, _encoding, callback) {
                    callback();
                },
            })
        ).on('close', () => {
            this.apiCalls.push({ api: 'snapshot' });
            res.send({ snapshotId: 1 });
            res.end();
        });
    }

    create_token(req: Request, res: Response) {
        res.send({ token: '1' });
        res.end();
        this.apiCalls.push({ api: 'create_token' });
    }

    bmprData(req: Request, res: Response) {
        res.sendFile(path.join(this.srcDirName, 'connectors/templates/2.0/webdemo.bmpr'));
    }

    setHasSnapshot(hasSnapshot: boolean) {
        this.hasSnapshot = hasSnapshot;
    }

    notifyUserEvent(req: Request, res: Response) {
        res.status(200).send('Ok.');
    }

    getProjectMembers(req: Request, res: Response) {
        res.send({
            users: [
                {
                    avatarUrl: 'https://placehold.it/120x120',
                    email: '<EMAIL>',
                    fullName: 'Alice Anderson',
                    id: 1000,
                    userName: '<EMAIL>',
                },
            ],
        });
    }

    setDataResidency(dataResidency: string) {
        this.dataResidencyResponse = { dataResidency };
    }

    setDataResidencyNotFound() {
        this.dataResidencyResponse = null;
    }

    getDataResidency(_req: Request, res: Response) {
        // if (this.dataResidencyResponse === undefined) {
        //     throw new Error(
        //         'Cloud mock has received a "get data residency" request, but no data residency has been set prior in tests. Call "setDataResidency" on Cloud server mock to set the data residency before each "get data residency" request.'
        //     );
        // }

        if (this.dataResidencyResponse === null) {
            res.status(404).json({});
        } else {
            res.json(this.dataResidencyResponse);
        }

        this.dataResidencyResponse = undefined;
    }
}

export class BackendRTCClientMock {
    public published: { channelID: string; payload: object }[] = [];
    public herenowUuids: string[] = [];
    sendMessage(channelID: string, payload: object, _logger: Logger) {
        this.published.push({
            channelID,
            payload,
        });
    }
    async buildRTCTokenInfo(channelName: string) {
        return {
            expirationInSec: 1000,
            channel: channelName,
            token: 'some_fake_token',
        };
    }
    async herenow() {
        return this.herenowUuids;
    }
    setReturnHerenowUuids(uuids: string[]) {
        // Used for setting up tests
        this.herenowUuids = uuids;
    }

    async getFrontendConfig(_logger: Logger) {
        return {
            heartbeatIntervalInSec: 10,
            webSocketsURI: '',
        };
    }

    async getWebsocketsURI(_logger: Logger) {
        return '';
    }
}

export class CloudWatchMock implements CloudWatchInterface {
    public metrics: Map<string, unknown[]>;

    constructor() {
        this.metrics = new Map();
    }
    getMetricStatistics(
        _params: GetMetricStatisticsCommandInput,
        callback: (err: any, data: GetMetricStatisticsCommandOutput) => void
    ): void {
        const data: GetMetricStatisticsCommandOutput = {
            $metadata: {
                httpStatusCode: 200,
                requestId: 'requestId',
                attempts: 1,
                totalRetryDelay: 0,
            },
            Datapoints: [
                {
                    Average: 1,
                },
            ],
        };

        callback(null, data);
    }

    putMetricData(params: PutMetricDataCommandInput, callback: (err: any, data: PutMetricDataCommandOutput) => void): void {
        assert(params.Namespace, 'Namespace is required');
        const datapoints = this.metrics.get(params.Namespace) || [];
        datapoints.push(params.MetricData);
        this.metrics.set(params.Namespace, datapoints);
        const data: PutMetricDataCommandOutput = {
            $metadata: {
                httpStatusCode: 200,
                requestId: 'requestId',
                attempts: 1,
                totalRetryDelay: 0,
            },
        };
        callback(null, data);
    }
}
