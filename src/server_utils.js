import { Metrics } from './metrics.ts';
import { SessionManager } from './session-manager.ts';
import { acquireApplicationLock, MySQLDriverPool } from './mysql-driver.ts';
import fs from 'fs';
import diskspace from 'diskspace';
import path from 'path';
import { BuildNumberTracker } from './track_build_number.ts';
import busboy from 'busboy';
import { RtcAdapter } from './rtc-adapter.ts';
import jwtModule from 'atlassian-jwt';
import { checkBasicAuthOnRequest } from './basicAuth.ts';
import httpreq from 'httpreq';
import { callWithLegacyCallback } from './calling-style.ts';
import Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js';
import getFolderSize from 'get-folder-size';
import { object2sqliteBuffer, sqliteBuffer2object } from './utils.ts';


/** @typedef {ReturnType<makeServerUtils>} ServerUtils */

/**
 * Creates server utilities.
 * 
 * @param {Object} params - The parameters for creating server utilities.
 * @param {SessionManager} params.sessionManager - The session manager instance.
 * @param {MySQLDriverPool} params.mySqlDriverInstance - The MySQL driver instance.
 * @param {JSDocTypes.Logger} params.logger - The logger instance.
 * @param {Function} params.getConnector - The function to get the connector.
 * @param {JSDocTypes.Config} params.config - The configuration object.
 * @param {Metrics} params.metrics - The metrics instance.
 * @param {BuildNumberTracker} params.buildNumberTracker - The build number tracker instance.
 * @param {RtcAdapter} params.rtcAdapter - The RTC adapter instance.
 * @param {JSDocTypes.Clock} params.clock - The clock instance.
 */
function makeServerUtils({
    sessionManager,
    mySqlDriverInstance,
    logger,
    getConnector,
    config,
    metrics,
    buildNumberTracker,
    rtcAdapter,
    clock
}) {

    function getKindFromArchiveID(archiveID) {
        let tmp = archiveID.substring(config.archiveIDPrefix.length).split("_");
        return tmp[0];
    }

    function broadcastRTCMessage(archiveID, archiveRevision, internalUserID, username, objToBroadcast, callback) {
       if (!archiveID) {
            // Safety check
            callback({ error: 'Null or undefined archiveID' });
        } else {
            let action = objToBroadcast && objToBroadcast.operation || "unknown";
            let logObj = {module: action, username: username, archiveID: archiveID};
            // logger.info("Broadcast message " + internalUserID + " " + archiveRevision, logObj);
            objToBroadcast.archiveRevision = archiveRevision;
            objToBroadcast.author = internalUserID;
            objToBroadcast.username = username;
            objToBroadcast.timestamp = new Date().getTime();

            let finalise = function(status) {
                let message;
                if (status.error) {
                    // handle error
                    message = JSON.stringify(status);
                    logger.error("Unable to broadcast RTC message: " + JSON.stringify(objToBroadcast) + " " + status, null, logObj);
                    callback && callback({error: "Unable to broadcast RTC message: " + message, busy: true});
                } else {
                    callback && callback({});
                }
            };

            rtcAdapter.sendMessage(archiveID, objToBroadcast);
            finalise({});
        }
    }

    let diskUsageTask = function(logger) {
        let percUsed, used;
        let oneMega = 1024*1024;
        // var oneGiga = oneMega*1024;
        let fiveMinutes = 5 * 60 * 1000;
        let absPath;
        logger = logger.getLogger({action: "disk usage", module: "gar"});

        // var convert2Giga = function(n) {
        //    return Math.round(n/oneGiga * 100) / 100;
        // };

        let convert2Mega = function(n) {
            return Math.round(n/oneMega * 100) / 100;
        };

        let cleanUpOlderFiles = function() {
            fs.readdir(config.archivesPath, function(err, files) {
                if (err) {
                    logger.error("[DF] archivesDiskUsed ERROR listing old files " + err, err);
                } else if (files) {
                    files.forEach(function(file) {
                        absPath = path.join(config.archivesPath, file);
                        (function(absPath) {
                            fs.stat(absPath, function(err, stat) {
                                let endTime, now;
                                if (err) {
                                    logger.error("[DF] archivesDiskUsed ERROR fs.stat " + file + " " + err);
                                } else {
                                    now = new Date().getTime();
                                    endTime = new Date(stat.ctime).getTime() + fiveMinutes;
                                    if (now > endTime) {
                                        fs.unlink(absPath, function(err) {
                                            if (err) {
                                                logger.error("[DF] archivesDiskUsed ERROR deleting old file " + absPath + " " + err);
                                            } else {
                                                logger.info("[DF] archivesDiskUsed deleted old file " + absPath + " size: " + convert2Mega(stat.size) + " MB ", {fileSize: stat.size});
                                            }
                                        });
                                    } else {
                                        logger.info("[DF] archivesDiskUsed skipping file: " + absPath + " size: " + convert2Mega(stat.size) + " MB " + (endTime - now)/1000 + " secs", {fileSize: stat.size});
                                    }
                                }
                            });
                        })(absPath);
                    });
                }
            });
        };

        let checkParentDir = function() {
            let parentDir = path.resolve(config.archivesPath, '..');

            walk_directory(parentDir, function (err, files) {
                if (err) {
                    logger.error("[DF] checkParentDir ERROR listing files in parent dir " + err);
                } else {
                    for (let { filePath, size } of files) {
                        logger.info("[DF] checkParentDir, listing file " + filePath + " size: " + convert2Mega(size) + " MB ");
                    }
                }
            });
        };

        logger.info("[DF] analysing memory usage " + (config.archivesPath || "config.archivesPath not defined"));

        diskspace.check('/etc/hosts', function (err, result)
        {
            if (err) {
                logger.error("[DF] ERROR " + err);
            } else {
                const { total, used } = result;
                percUsed = Math.round(used / total * 100);
                //logger.info("[DF] " + convert2Giga(total) + " GB " + convert2Giga(used) + " GB");
                //logger.info("[DF] disk used: " + percUsed + "%");
                metrics.addValue('diskUsed', percUsed, 'Percent');
            }
        });

        if (config.archivesPath) {
            if (!fs.existsSync(config.archivesPath)) {
                fs.mkdirSync(config.archivesPath);
            }
            logger.info("[DF] going to check shared memory usage " + config.archivesPath);
            diskspace.check(config.archivesPath, function(err, result)
            {
                if (err) {
                    logger.error("[DF] ERROR " + err);
                } else {
                    const { total, used, free } = result;
                    percUsed = Math.round(used / total * 100);
                    const tmpObj = {
                        totalMemory: total,
                        freeMemory: free,
                        percUsed: percUsed,
                    };
                    if (used || used === 0) {
                        logger.info("[DF] archivesDiskUsed total " + convert2Mega(total) + " MB used " + convert2Mega(used) + " MB", tmpObj);
                    } else {
                        logger.warn("[DF] unable to estimate shared memory usage", tmpObj);
                    }
                    //logger.info("[DF] disk used: " + percUsed + "%");
                    metrics.addValue('archivesDiskUsed', percUsed, 'Percent');
                }
            });

            getFolderSize.loose(config.archivesPath).then(function(size) {
                let sizeInMega = convert2Mega(size);
                if (!Number.isNaN(sizeInMega)) {
                    logger.info("[DF] archivesDiskUsed getFolderSize " + sizeInMega + ' MB', {folderSize: size});
                    metrics.addValue('archivesDiskSizeInMega', sizeInMega, 'Bytes');
                }
            });

            cleanUpOlderFiles();
            checkParentDir();
        }
    };

    function parseMultipartFormData(logger, req, res, callback) {
        let bb = busboy({headers: req.headers});
        let errorOccurred = false;
        let params = {};
        let fileDataChunks = [];

        let startParsing = Date.now();
        bb.on('file', function (fieldname, file, info) {
            const { encoding } = info;
            file.on('data', function (data) {
                fileDataChunks.push(Buffer.from(data, encoding));
            });
            file.on('error', function (err) {
                if (!errorOccurred) { // Do not terminate the request more than once
                    logger.error('Error reading file from request body: ' + err.message, err, {action: 'create upload'});
                    errorOccurred = true;
                    res.status(400).send('Error processing the request');
                }
            });
        });
        bb.on('field', function (fieldname, val) {
            params[fieldname] = val;
        });
        bb.on('close', function() {
            if (!errorOccurred) {
                metrics.addValue('upload-parseTime', Date.now() - startParsing, 'Milliseconds');
                req.startProcessing = Date.now();
                callback(params, fileDataChunks.length > 0 ? Buffer.concat(fileDataChunks) : null);
            }
        });
        bb.on('error', function (err) {
            if (!errorOccurred) { // Do not terminate the request more than once
                logger.error('Error parsing the request body: ' + err.message, err, {action: 'create upload'});
                errorOccurred = true;
                res.status(400).send('Error processing the request');
            }
        });
        req.pipe(bb).on('error', function (err) {
            if (!errorOccurred) { // Do not terminate the request more than once
                logger.error('Error on piping data: ' + err.message, err, {action: 'create upload'});
                errorOccurred = true;
                res.status(400).send('Error processing the request');
            }
        });
    }

    function flushToConnectorHoldingTheTruth(logger, sessionData, archiveID, newPlatformArchiveName, kind, options, fromClose, callback) {
        options = options || {};
        let dbConnector = sessionData.dbConnector;
        logger = logger.getLogger({action: sessionData.action, sessionToken: sessionData.token, kind: kind, archiveID: archiveID});
        let unloadBar = options.unloadBar;

        // TODO: to be tested. WRITE lock should reduce race condition not decreasing performance
        let timer = metrics.trackTimeInterval('flushToConnectorHoldingTheTruth openBarLocked');
        sessionManager.openBarLocked(sessionData, archiveID, "WRITE", function (obj) {
            timer.stop();
            if (obj.error) {
                logger.error("flushing, failed to open the archive: " + obj.error);
                callback(obj);
            } else {
                // need a READ LOCK
                let bar = obj.bar;

                bar.getArchiveRevision(function(obj) {
                    let archiveRevision;
                    if (obj.error) {
                        logger.error("getArchiveRevision failed: " + obj.error);
                        sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                            callback(obj);
                        });
                        return;
                    }
                    archiveRevision = obj.archiveRevision;
                    dbConnector.getPlatformData(archiveID, function(obj) {
                        let platformInfo;
                        if (obj.error) {
                            logger.error("dbConnector.getPlatformData failed: " + obj.error);
                            sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                                callback(obj);
                            });
                            return;
                        }

                        // check if the archive is existent, if it doesn't exist getPlatformData returns {}
                        if (!obj.PLATFORM_ARCHIVE_ID) {
                            logger.warn("archive has been already deleted in the meantime");
                            sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                                callback({error: "archive is not loaded on BAS"});
                            });
                            return;
                        }
                        try {
                            platformInfo = obj.PLATFORM_INFO ? JSON.parse(obj.PLATFORM_INFO) : {};
                        } catch(err) {
                            logger.error("parsing failed: " + err);
                            sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                                callback(obj);
                            });
                            return;
                        }

                        function finalizeFlush() {
                            if (!options.force && platformInfo && platformInfo.archiveRevisionOnPlatform !== undefined && archiveRevision <= platformInfo.archiveRevisionOnPlatform) {
                                logger.info("flushToConnectorHoldingTheTruth archive is already updated");
                                sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                                    callback({wasAlreadyUpdated: true, platformArchiveID: obj.PLATFORM_ARCHIVE_ID});
                                });
                                return;
                            }

                            logger.info("flushToConnectorHoldingTheTruth going to flush the updated archive");

                            // archive need to be flushed on platform
                            // make the dump of all the branches
                            timer = metrics.trackTimeInterval('flushToConnectorHoldingTheTruth dump');
                            bar.dump(null, function (obj) {
                                timer.stop();
                                let dump;
                                if (obj.error) {
                                    logger.error("flushing, failed to create the dump");
                                    sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                                        callback(obj);
                                    });
                                } else {
                                    dump = obj.dump;
                                    dump.forceFlush = options.force;
                                    timer = metrics.trackTimeInterval('flushToConnectorHoldingTheTruth unlockConnection');
                                    sessionManager.unlockConnection(sessionData, function (/*objUnlock*/) {
                                        timer.stop();
                                        timer = metrics.trackTimeInterval('flushToConnectorHoldingTheTruth object2sqliteBuffer');
                                        object2sqliteBuffer(config, dump, function (obj) {
                                            timer.stop();
                                            if (obj.error) {
                                                logger.error("flushing, failed to create the buffer from db " + obj.error);
                                                callback(obj);
                                            } else {
                                                let internalUserID, username;
                                                let objToBroadcast, buffer = obj.buffer;

                                                if (sessionData.user) {
                                                    internalUserID = sessionData.user.INTERNAL_ID;
                                                    username = sessionData.user.USERNAME;
                                                }

                                                // begin transaction
                                                timer = metrics.trackTimeInterval('flushToConnectorHoldingTheTruth lock');
                                                dbConnector.lock("WRITE", function (obj) {
                                                    timer.stop();
                                                    if (obj.error) {
                                                        callback(obj);
                                                    } else {
                                                        objToBroadcast = {
                                                            operation: 'archiveIsGoingToBeFlushedOnPlatform',
                                                            ArchiveRevision: dump.Info.ArchiveRevision
                                                        };
                                                        internalUserID && broadcastRTCMessage(archiveID, dump.Info.ArchiveRevision, internalUserID, username, objToBroadcast, function (obj) {
                                                            if (obj.error) {
                                                                logger.error("flushing, failed to broadcast RTC archiveIsGoingToBeFlushedOnPlatform message for archive " + archiveID + " " + obj.error);
                                                            }
                                                        });
                                                        timer = metrics.trackTimeInterval('flushToConnectorHoldingTheTruth save');
                                                        getConnector(kind).save(logger, dbConnector, sessionData.user, archiveID, newPlatformArchiveName, dump.Info.ArchiveRevision, buffer, dump, { fromClose: fromClose, fromRestore: false, force: options.force }, function (obj) {
                                                            timer.stop();
                                                            if (obj.error) {
                                                                logger.warn("flushing, failed to save the database " + obj.error + " " + archiveID);
                                                                dbConnector.unlock(function (objLock) {
                                                                    if (objLock.error) {
                                                                        logger.error("flushing, failed to unlock the database " + objLock.error + " " + archiveID + " " + sessionData.user.USERNAME);
                                                                    }
                                                                    callback(obj);
                                                                });
                                                            } else {
                                                                // if save succeed, we set the WARNING_FLAG to 0 (i.e. revision saved on the platform is the last one, so we can unload the BAR on gardening)
                                                                timer = metrics.trackTimeInterval('flushToConnectorHoldingTheTruth updateArchiveWarningFlag');
                                                                logger.info("Going to updateArchiveWarningFlag " + obj.platformArchiveID);
                                                                dbConnector.updateArchiveWarningFlag(archiveID, 0, function (resWarningFlag) {
                                                                    timer.stop();
                                                                    // in some platform (e.g. JIRA), saving the archive produce a new platform archive ID
                                                                    let platformArchiveID = obj.platformArchiveID;
                                                                    let prevPlatformArchiveID = obj.prevPlatformArchiveID;

                                                                    if (resWarningFlag.error) {
                                                                        logger.error("flushing, failed to set WARNING_FLAG for archive " + archiveID);
                                                                    }

                                                                    // if the flush was successful, broadcast the good news
                                                                    if (obj.wasAlreadyUpdated) {
                                                                        logger.info("flushing, archive was already updated for archive " + archiveID);
                                                                    } else {
                                                                        objToBroadcast = {
                                                                            operation: 'archiveHasBeenFlushedOnPlatform',
                                                                            ArchiveRevision: dump.Info.ArchiveRevision,
                                                                            platformArchiveID: platformArchiveID
                                                                        };
                                                                        internalUserID && broadcastRTCMessage(archiveID, dump.Info.ArchiveRevision, internalUserID, username, objToBroadcast, function (obj) {
                                                                            if (obj.error) {
                                                                                logger.error("flushing, failed to broadcast RTC message for archive " + archiveID + " " + obj.error);
                                                                            }
                                                                        });
                                                                    }

                                                                    logger.info("Going to unlock " + platformArchiveID);
                                                                    dbConnector.unlock(function (objLock) {
                                                                        logger.info("Unlocked " + platformArchiveID);
                                                                        if (objLock.error) {
                                                                            logger.error("flushing, failed to unlock the database " + objLock.error);
                                                                            callback(obj);
                                                                        } else {
                                                                            let unloadFunction = function () {
                                                                                logger.info("Almost done " + platformArchiveID);
                                                                                if (unloadBar) {
                                                                                    logger.info("flushing, unload archive " + archiveID);
                                                                                    timer = metrics.trackTimeInterval('flushToConnectorHoldingTheTruth destroy');
                                                                                    bar.destroy(archiveID, function (obj) {
                                                                                        timer.stop();
                                                                                        if (obj.error) {
                                                                                            logger.warn("flushing, unable to destroy archive " + archiveID + " " + obj.error);
                                                                                            callback(obj);
                                                                                        } else {
                                                                                            logger.info("flushing, unloaded platform info for archive " + archiveID);
                                                                                            timer = metrics.trackTimeInterval('flushToConnectorHoldingTheTruth deleteArchivePlatformData');
                                                                                            dbConnector.deleteArchivePlatformData(archiveID, function (obj) {
                                                                                                timer.stop();
                                                                                                callback(obj);
                                                                                            });
                                                                                        }
                                                                                    });
                                                                                } else {
                                                                                    callback(obj);
                                                                                }
                                                                            };

                                                                            // in JIRA attachment cannot be updated
                                                                            if (prevPlatformArchiveID && platformArchiveID !== prevPlatformArchiveID) {
                                                                                objToBroadcast = {
                                                                                    operation: 'platformArchiveIDChanged',
                                                                                    ArchiveRevision: dump.Info.ArchiveRevision,
                                                                                    platformArchiveID: platformArchiveID
                                                                                };
                                                                                internalUserID && broadcastRTCMessage(archiveID, dump.Info.ArchiveRevision, internalUserID, username, objToBroadcast, function (obj) {
                                                                                    if (obj.error) {
                                                                                        logger.error("flushing, failed to broadcast RTC message for archive " + archiveID + " " + obj.error);
                                                                                    }
                                                                                    unloadFunction();
                                                                                });
                                                                            } else {
                                                                                // we do not need to broadcast any message
                                                                                unloadFunction();
                                                                            }
                                                                        }
                                                                    });
                                                                });
                                                            }
                                                        });
                                                    }
                                                });
                                            }
                                        });
                                    });
                                }
                            });
                        }

                        if (bar.purgeCommentsAndUsers && sessionData.user && fromClose) {
                            let internalUserID, username;
                            let objToBroadcast;

                            if (sessionData.user) {
                                internalUserID = sessionData.user.INTERNAL_ID;
                                username = sessionData.user.USERNAME;
                            }
                            bar.purgeCommentsAndUsers(function (res) {
                                if (res.error) {
                                    logger.error("error purging comments and users: " + res.error);
                                } else {
                                    if (res.purgedComments.length + res.purgedUsers.length > 0) {
                                        logger.info("purged comments: " + res.purgedComments.length + ", purged users: " + res.purgedUsers.length);
                                        objToBroadcast = {
                                            operation: 'commentsAndUsersPurged',
                                            ArchiveRevision: res.archiveRevision,
                                            purgedComments: res.purgedComments,
                                            purgedUsers: res.purgedUsers
                                        };
                                        internalUserID && broadcastRTCMessage(archiveID, res.ArchiveRevision, internalUserID, username, objToBroadcast, function (obj) {
                                            if (obj.error) {
                                                logger.error("flushing, failed to broadcast RTC message for archive " + archiveID + " " + obj.error);
                                            }
                                        });
                                    }
                                }
                                finalizeFlush();
                            });
                        } else {
                            finalizeFlush();
                        }
                    });
                });
            }
        });
    }


    function tryToSyncArchiveOnPlatformOffline(logger, sessionData, archiveID, kind, platformInfo, options, callback) {
        options = options || {};
        if (!getConnector(kind)) {
            callback && callback({error: "offline sync to platform failed: " + kind + " connector not found"});
            return;
        }
        // try to sync the archive on platform
        getConnector(kind).getAuthTokenFromPlatform(logger, platformInfo, function(obj) {
            if (obj.error) {
                callback && callback({error: "offline sync to platform failed: " + kind + " error: " + obj.error, url: obj.url});
            } else {
                // some platform will return the user object needed to impersonate the action
                // otherwise we bubble up the platform token
                sessionData.user = obj.user || {
                    PLATFORM_TOKEN: obj.platformToken
                };

                // flushToConnectorHoldingTheTruth(sessionData, archiveID, newPlatformArchiveName, kind, options, fromClose, callback)
                flushToConnectorHoldingTheTruth(logger, sessionData, archiveID, null, kind, {force: options.force}, false, function (obj) {
                    callback && callback(obj);
                });
            }
        });
    }


    let getLoadedProjectStats = function(logger, dbConnector, warningFlag) {
        warningFlag = warningFlag || 0;
        logger = logger.getLogger({action: "getLoadedProjectStats", module: "gar"});
        // logger.info("Start getLoadedProjectStats");
        dbConnector.getLoadedArchiveForKind(warningFlag, function (obj) {
            let keys, kind, i, metricName, tot = 0;
            if (obj.error) {
                logger.info("ERROR : getLoadedProjectStats " + obj.error);
            } else {
                keys = Object.keys(obj);
                for (i=0; i<keys.length; i++) {
                    kind = keys[i];
                    metricName = 'projects-loaded-' + kind + (warningFlag ? "-not-synced" : "");
                    logger.info("Actual" + (warningFlag ? " NOT SYNCED " : " ") + "loaded projects for kind " + kind + " " + obj[kind]);
                    metrics.addValue(metricName, obj[kind], 'Count');
                    tot += obj[kind];
                }
                metricName = 'projects-loaded' + (warningFlag ? "-not-synced" : "");
                logger.info("Actual TOT" + (warningFlag ? " NOT SYNCED " : " ") + "loaded projects " + tot);
                metrics.addValue(metricName, tot, 'Count');
            }
        })
    };

    function unloadUnusedArchiveJob(logger, sessionData, options, callback) {
        options = options || {};
        let MAX_UNLOADING_BULK = config.maxNumberOfProjectsToUnload || 5000;
        let DELAYED_UNLOAD_TIMEOUT = 1000; // 1 second
        let dbConnector = sessionData.dbConnector;
        let i, k;
        let connectorsList = options.connectors ? options.connectors : config.unloadUnusedArchivesForConnectors;
        let archiveIDsList = [], contentData = {};
        logger = logger.getLogger({action: "unloadUnusedArchiveJob", module: "gar"});

        getLoadedProjectStats(logger, dbConnector);
        getLoadedProjectStats(logger, dbConnector, 1);

        // push metrics
        metrics.putMetricData();

        logger.info("looking for unused archives in connectors: " + connectorsList.join(', '));
        for (k=0; k<connectorsList.length; k++) {
            (function(kind) {
                let now = (new Date()).getTime(), warningFlag = 0;
                let max_timestamp = now - getConnector(kind).maxAge();

                contentData[kind] = {status: "onprogress"};
                dbConnector.selectPlatformDataToUnload(kind, max_timestamp, warningFlag, function (obj) {
                    if (obj.error) {
                        logger.error("unloading old archive data: " + obj.error);
                        contentData[kind] = {status: "error", error: obj.error};
                    } else {
                        logger.info("found " + obj.length + " potential archives to unload of kind " + kind);
                        for (i = 0; i < obj.length; i++) {
                            archiveIDsList.push(obj[i].BAS_ARCHIVE_ID);
                        }
                        contentData[kind] = {status: "success"};
                    }
                });
            })(connectorsList[k]);
        }

        watchdog(contentData, function() {
            let totalNumber = archiveIDsList.length;
            let actualNumber;
            metrics.addValue('projects-to-unload', totalNumber, 'Count');
            if (options.onlyDoArchiveID) {
                archiveIDsList = archiveIDsList.filter(function (archiveID) { return archiveID === options.onlyDoArchiveID; });
            }
            archiveIDsList = archiveIDsList.slice(0, options.maxBulkSize || MAX_UNLOADING_BULK);
            if (options.debugPrint) {
                options.debugPrint('' + archiveIDsList.length + ' archives to process');
                for (let i=0; i<archiveIDsList.length; i+=1) {
                    options.debugPrint(archiveIDsList[i]);
                }
            }
            if (options.skipProcessing) {
                callback({});
                return;
            }
            actualNumber = archiveIDsList.length;
            logger.info("Unloading " + actualNumber + " archives out of " + totalNumber);
            unloadMultipleArchives(logger, sessionData, archiveIDsList, options, DELAYED_UNLOAD_TIMEOUT, function(obj) {
                if (actualNumber) {
                    logger.info("[DONE] Unloaded " + (actualNumber - archiveIDsList.length) + " archives out of " + totalNumber);
                } else {
                    logger.info("[DONE] Nothing to unload, enjoying free time");
                }
                callback(obj);
            });
        });
    }


    // TODO: duplicate
    function watchdog(contentData, callback)
    {
        let i, key, keys = Object.keys(contentData), completed = 0, error = null;
        for (i=0; i<keys.length; i++)
        {
            key = keys[i];
            if (contentData[key].error) {
                error = contentData[key].error;
                break;
            } else if (contentData[key].status === "onprogress") {
                break;
            } else {
                // success
                completed++;
            }
        }

        if (completed === keys.length)
        {
            callback({status: "completed"});
        } else if (error) {
            callback({error: error});
        } else {
            // retry later
            setTimeout(function() {
                watchdog(contentData, callback);
            }, 200);
        }
    }


    function unloadMultipleArchives(logger, sessionData, archiveIDsList, options, delayedTimeout, callback) {
        let archiveID;
        logger = logger.getLogger({action: "unloadMultipleArchives", module: "gar"});

        if (archiveIDsList.length === 0) {
            logger.info("unloadMultipleArchives finished");
            callback({});
        } else {
            logger.info("unloadMultipleArchives still " + archiveIDsList.length + " archives to unload");
            archiveID = archiveIDsList.pop();
            unloadSingleArchive(logger, sessionData, archiveID, options, function (obj) {
                if (obj && obj.wasAlreadyUpdated) {
                    logger.info("unloadMultipleArchives archive was already updated, skip the timeout");
                    unloadMultipleArchives(logger, sessionData, archiveIDsList, options, delayedTimeout, callback);
                } else {
                    setTimeout(function() {
                        unloadMultipleArchives(logger, sessionData, archiveIDsList, options, delayedTimeout, callback);
                    }, delayedTimeout);
                }
            });
        }
    }


    function unloadSingleArchive(logger, sessionData, archiveID, options, callback) {
        options = options || {};

        let dbConnector = sessionData.dbConnector, archiveRevisionOnPlatform, now;
        logger = logger.getLogger({action: "unloadSingleArchive", module: "gar"});
        logger.info("Unloading single archive " + archiveID);

        let finalise = function(obj) {
            // release session
            sessionManager.unlockConnection(sessionData, function (objUnlock) {
                if (objUnlock.error) {
                    logger.error("unlocking connection error " + objUnlock.error);
                }

                if (obj.error) {
                    logger.warn("unloading archive error " + archiveID + " " + obj.error);
                } else {
                    let delta = (new Date()).getTime() - now;
                    logger.info("successfully unloaded archive in " + delta  + " msec: " + archiveID);
                }

                if (objUnlock.error) {
                    callback(objUnlock);
                } else {
                    callback(obj);
                }
            });
        };

        let setWarningFlagAndFinalise = function(obj, message) {
            if (options.skipSettingWarningFlag) {
                finalise(obj);
                return;
            }
            logger.error('Setting warning flag for archive ' + archiveID + '. Reason: ' + message + '. Error: ' + obj.error);
            dbConnector.updateArchiveWarningFlag(archiveID, 1, function(updateObj) {
                if (updateObj.error) {
                    logger.error("not able to set the warning flag on not updated archive: " + archiveID);
                    finalise(updateObj);
                } else {
                    finalise(obj);
                }
            });
        };

        let tryToForceSavingAndFinalise = function(kind, platformInfo) {
            tryToSyncArchiveOnPlatformOffline(logger, sessionData, archiveID, kind, platformInfo, { force: true }, function(obj) {
                if (obj.error) {
                    setWarningFlagAndFinalise(obj, "not able to unload archive to platform. archiveRevisionOnPlatform: " + archiveRevisionOnPlatform);
                } else {
                    logger.info("archive synced offline: " + archiveID);
                    finalise(obj);
                }
            });
        };

        now = (new Date()).getTime();
        // lock the BAR and the PLATFORM_INFO database
        sessionManager.openBarLockedExt(sessionData, archiveID, "WRITE", true, function (obj) {
            let platformSiteID, platformArchiveID, kind, platformInfo;

            if (obj.error) {
                setWarningFlagAndFinalise(obj, 'Error in openBarLockedExt');
                return;
            }

            let bar = obj.bar;
            bar.getArchiveRevision(function (obj) {
                let revision;
                if (obj.error) {
                    setWarningFlagAndFinalise(obj, 'Error in bar.getArchiveRevision');
                    return;
                }
                revision = obj.archiveRevision;
                dbConnector.getPlatformData(archiveID, function(obj) {
                    if (obj.error) {
                        setWarningFlagAndFinalise(obj, 'Error in dbConnector.getPlatformData');
                        return;
                    }

                    try {
                        platformInfo = obj.PLATFORM_INFO ? JSON.parse(obj.PLATFORM_INFO) : {};
                    } catch(e) {
                        logger.warn('unloading single archive: unexpected exception parsing PLATFORM_INFO: ' + obj.PLATFORM_INFO);
                    }

                    kind = obj.PLATFORM_KIND;
                    platformSiteID = obj.PLATFORM_SITE_ID;
                    platformArchiveID = obj.PLATFORM_ARCHIVE_ID;
                    logger.updateParams({
                        kind: kind,
                        platformSiteID: platformSiteID,
                        platformArchiveID: platformArchiveID,
                        archiveID: archiveID,
                    })
                    archiveRevisionOnPlatform = platformInfo.archiveRevisionOnPlatform;

                    if (revision !== archiveRevisionOnPlatform) {
                        logger.info('unloading single archive: tryToSyncArchiveOnPlatformOffline, archiveRevisionOnPlatform ' + archiveRevisionOnPlatform + " revision " + revision);
                        tryToForceSavingAndFinalise(kind, platformInfo);
                        // we can try to delete the archive here in order to not have to wait another 30 days
                    } else {
                        if (!getConnector(kind)) {
                            finalise({}); // kind not supported
                            return;
                        }
                        getConnector(kind).projectHasBeenSavedOnPlatform(logger, dbConnector, platformSiteID, platformArchiveID, function(obj) {
                            if (obj.error) {
                                // project has been deleted from Cloud, we flag the project it will be unloaded by Cloud gardening
                                setWarningFlagAndFinalise(obj, 'Error in projectHasBeenSavedOnPlatform');
                                return;
                            }
                            if (obj.status !== true) {
                                // project has not been saved (i.e. no snapshot in Cloud) , we force the saving
                                logger.info('unloading single archive: archive DOES NOT exist on platform, tryToSyncArchiveOnPlatformOffline');
                                tryToForceSavingAndFinalise(kind, platformInfo);
                            } else {
                                // project has been saved and is synced, we can safely unload it from BAS
                                logger.info('unloading single archive: archive exists on platform');
                                if (options.skipDelete) {
                                    finalise({});
                                    return;
                                }
                                dbConnector.deleteArchivePlatformData(archiveID, function (obj) {
                                    if (obj.error) {
                                        setWarningFlagAndFinalise(obj, 'Error in deleteArchivePlatformData');
                                    } else {
                                        bar.destroyExt(archiveID, true, function (obj) {
                                            logger.info('unloading single archive: archive unloaded from BAS');
                                            // archive has been already synced on platform, setting "wasAlreadyUpdated" property will avoid the not needed timeout
                                            obj.wasAlreadyUpdated = true;
                                            finalise(obj);
                                        });
                                    }
                                });
                            }
                        });
                    }
                });
            });
        });
    }

    // Explanation and use case
    // ------------------------
    // Gardening jobs run at fixed intervals and are supposed to be singletons.
    // If a specific execution is longer than the said interval, the following
    // run will overlap the old one, thus violating the singleton property.
    // In order to avoid this scenario, a simple detection mechanism is used,
    // based on MySQL global named-locks. A new connection is created and held
    // for the whole duration of the job execution. A new starting job will
    // try to acquire the lock in a non-blocking way, and will fail if another
    // task (holding the same named-lock) is still running. Therefore will
    // voluntarily quit in order not to overlap executions.
    // A new connection is needed because global locks are automatically
    // released upon every successfully committed transaction. Therefore
    // regular connections cannot be used, because they initiate and commit
    // several transactions.
    // Explicitly releasing the lock is not necessary, and indeed explicitly
    // avoided, because simply quitting the process will terminate the DB
    // connection, thus implicitly releasing the lock, according to the
    // semantics of GET_LOCK (see MySQL's documentation). In fact, this
    // semantics plays in our favour because even if the process terminates
    // abnormally, the lock is still implicitly released, thus allowing the
    // following scheduled task to correctly execute.
    /**
     * 
     * @param {string} lockName 
     * @param {JSDocTypes.BASLegacyCallback<{success: true, error: undefined}>} cb 
     */
    let acquireApplicationLockOnNewConnection = function (lockName, cb) {
        mySqlDriverInstance.getConnection(function (obj) {
            if (obj.error) {
                cb({ error: 'Cannot acquire DB connection' });
            } else {
                acquireApplicationLock(obj, lockName, false, function (obj) {
                    if (obj.error) {
                        cb({error: `Cannot acquire application lock ${lockName}`});
                    } else {
                        if (!obj.lockAcquired) {
                            cb({error: `Another process is holding the ${lockName} application lock`});
                        } else {
                            cb({success: true});
                        }
                    }
                });
            }
        });
    };

    function createBARFromBuffer(archiveID, buffer, sessionData, platformInfo, kind, platformSiteID, platformArchiveID, platformArchiveName, dbConnector, logger, callback ) {
        sqliteBuffer2object(config, buffer, kind, function (obj) {
            if (obj.error) {
                callback(obj);
                return;
            }
            let dump = obj.dump;
            let bar = sessionManager.getBar(sessionData);
            logger.info("creating the archive " + archiveID);
            // we do not need LOCK, archiveID is unique and PLATFORM_DATA is not yet saved
            bar.createFromDump(archiveID, dump, function (obj) {
                if (obj.error) {
                    callback(obj);
                    return;
                }

                bar.getHeuristicArchiveSize(function (objSize) {
                    let heuristicArchiveSize;
                    if (objSize.error) {
                        logger.error("error calculating archive heuristic size: " + objSize.error);
                    } else {
                        heuristicArchiveSize = objSize.heuristicArchiveSize;
                        logger.info("archive heuristic size: " + objSize.heuristicArchiveSize, {heuristicArchiveSize});
                    }


                    // we save the current revision on load, it will be useful on saving
                    if (!platformInfo) {
                        platformInfo = {};
                    }
                    if (dump.migrated) {
                        // force the archive to be flushed
                        logger.info("archive migrated to 2.0, forcing the flush");
                        platformInfo.archiveRevisionOnPlatform = dump.Info.ArchiveRevision - 1;
                    } else {
                        platformInfo.archiveRevisionOnPlatform = dump.Info.ArchiveRevision;
                    }
                    logger.info("saving platform info for archive " + archiveID, {platformInfo});
                    dbConnector.saveArchivePlatformData(archiveID, kind, platformSiteID, platformArchiveID, platformArchiveName, platformInfo, function (obj) {
                        if (obj.error) {
                            callback(obj);
                            return;
                        }
                        callback({dump: dump, heuristicArchiveSize: heuristicArchiveSize});
                    });
                });
            });
        });
    }

    async function getResourceNameAndIDs({dbConnector, connector, sessionData, platformKind, platformSiteID, platformArchiveID, platformInfo, resourceID, branchID, logger}) {
        async function _getResourceInfoFromConnector({platformKind, platformSiteID, platformArchiveID, platformInfo, resourceID, branchID}) {
            const authInfo = await callWithLegacyCallback(cb => connector.getAuthTokenFromPlatform(logger, platformInfo, cb));
            const platformToken = authInfo.platformToken;

            const archive = await callWithLegacyCallback(cb => connector.loadFromPlatform(logger, dbConnector, platformToken, platformSiteID, platformArchiveID, null, platformInfo, cb));
            const archiveID = archive.id;
            const buffer = archive.buffer;

            platformInfo = archive.platformInfo || platformInfo;  // update the platformInfo if changed by the loadFromPlatform
            let bar;
            try {
                bar = await callWithLegacyCallback(cb => createBARFromBuffer(archiveID, buffer, sessionData, platformInfo, platformKind, platformSiteID, platformArchiveID, null, dbConnector, logger, cb));
            } catch (error) {
                if (error?.resultObj?.busy) {
                    // The archive has just been loaded by another (likely concurrent) request.
                    return await _getResourceInfoFromBAS({archiveID, platformSiteID, platformArchiveID, platformInfo, resourceID, branchID});
                }
                throw error;
            }
            return _getResourceInfo(bar.dump, resourceID, branchID);
        }

        async function _getResourceInfoFromBAS({archiveID, platformSiteID, platformArchiveID, resourceID, branchID}) {
            logger.info(`permalink accessing an already opened basArchiveID:${archiveID} platformArchiveID:${platformArchiveID} platformSiteID: ${platformSiteID}`);

            try {
                const archive = await callWithLegacyCallback(cb => sessionManager.openBarLocked(sessionData, archiveID, "READ", cb));
                const bar = archive.bar;
                const { dump } = await callWithLegacyCallback(cb => bar.getToc(Consts.Branch.AllBranches,cb));
                return _getResourceInfo(dump, resourceID, branchID);
            } finally {
                await callWithLegacyCallback(cb => sessionManager.unlockConnection(sessionData, cb));
            }
        }

        function _getResourceInfo(dump, resourceID, branchID) {
            let ret = {};
            if (dump && dump.Resources && Array.isArray(dump.Resources) && dump.Resources.length > 0) {
                const projectName = dump.Info.ArchiveAttributes.name;

                if (!resourceID) {
                    const resources = dump.Resources.slice().filter((element) => {
                        return (!element.ATTRIBUTES.trashed && element.ATTRIBUTES.kind === "mockup");
                    }).sort(function (a, b) {
                        return a.ATTRIBUTES.order - b.ATTRIBUTES.order;
                    });

                    ret = resources.length > 0 ? {
                        resourceInfo: {
                            resourceID: resources[0].ID,
                            branchID: resources[0].BRANCHID,
                            name: resources[0].ATTRIBUTES.name,
                            projectName
                        }
                    } : {};
                }
                else {
                    if (!branchID) {
                        branchID = Consts.Branch.MasterBranchID;
                    }
                    const resource = dump.Resources.find(
                        (resource) => {
                            if (branchID !== Consts.Branch.MasterBranchID) {
                                return !resource.ATTRIBUTES.trashed && resource.ID.indexOf(resourceID) === 0 && resource.BRANCHID.indexOf(branchID) === 0;
                            }
                            else {
                                return !resource.ATTRIBUTES.trashed && resource.ATTRIBUTES.kind === "mockup" && resource.ID.indexOf(resourceID) === 0 && resource.BRANCHID.indexOf(branchID) === 0;
                            }

                        }
                    );

                    let name = resource ? resource.ATTRIBUTES.name : null;
                    if (resource && !name) {
                        const resourceMaster = dump.Resources.find(
                            (resource) => {
                                return resource.ID.indexOf(resourceID) === 0 && resource.BRANCHID.indexOf(Consts.Branch.MasterBranchID) === 0;
                            }
                        );
                        if (resourceMaster && resourceMaster.ATTRIBUTES.name) {
                            name = resourceMaster.ATTRIBUTES.name;
                            if (branchID !== Consts.Branch.MasterBranchID) {
                                const branch = dump.Branch.find(
                                    (branch) => {
                                        return branch.branchID.indexOf(branchID) === 0;
                                    }
                                );

                                if (branch && branch.ATTRIBUTES.branchName) {
                                    name += " (" + branch.ATTRIBUTES.branchName + ")";
                                }
                            }
                        }
                    }
                    ret = resource ? {
                        resourceInfo: {
                            resourceID: resource.ID,
                            branchID: resource.BRANCHID,
                            name: name,
                            projectName
                        }
                    } : {};
                }
            }

            if (ret && ret.resourceInfo && ret.resourceInfo.resourceID && ret.resourceInfo.branchID && dump.Thumbnails) {
                const thumbnail = dump.Thumbnails.find(
                    (thumbnail) => {
                        return thumbnail.ATTRIBUTES.resourceID.indexOf(ret.resourceInfo.resourceID) === 0 && thumbnail.ATTRIBUTES.branchID.indexOf(ret.resourceInfo.branchID) === 0;
                    }
                );

                if (thumbnail && thumbnail.ATTRIBUTES && thumbnail.ATTRIBUTES.image) {
                    ret.resourceInfo["thumbnail"] = thumbnail.ATTRIBUTES.image;
                }
            }

            return ret;
        }

        let archiveInfo = await callWithLegacyCallback(cb => dbConnector.getBASArchiveIDWithExclusiveRowLock(platformSiteID, platformArchiveID, true, cb));

        if (!archiveInfo.BAS_ARCHIVE_ID) {  // Retrieve the archive id from connector
            return await _getResourceInfoFromConnector({platformKind, platformSiteID, platformArchiveID, platformInfo, resourceID, branchID});
        } else {
            const archiveID = archiveInfo.BAS_ARCHIVE_ID;
            return await _getResourceInfoFromBAS({archiveID, platformSiteID, platformArchiveID, platformInfo, resourceID, branchID});
        }
    }

    function makePermalinkObject(permalinkData, connector) {
        const { permalinkID, resourceID, branchID, dirty, permalinkKind, platformKind, timestamp, permalinkInfo } = permalinkData;
        const { image, edit, comment, view} = connector.getPermalinkExtraFromPermalinkData(permalinkData);
        return {
            resourceID,
            branchID,
            permalinkID,
            permalinkKind,
            platformKind,
            permalinkInfo,
            dirty,
            timestamp,
            image,
            comment,
            edit,
            view,
        };
    }

    async function getPermalinksData({ siteId, projectId, kind, dbConnector, connector}) {
        let platformSiteID = siteId,
            platformArchiveID = projectId,
            platformKind = kind;

        const { permalinksData } = await callWithLegacyCallback(cb => dbConnector.getPermalinksDataFromArchiveID(platformKind, platformSiteID, platformArchiveID, cb));

        return {
            permalinksData: permalinksData.map((permalinkData) => {
                return makePermalinkObject(permalinkData, connector);
            })
        };
    }

    async function createOrUpdateImageUnfurling({ resourceInfo, platformKind, platformSiteID, platformArchiveID, resourceID, branchID, dbConnector, permalinkInfo, platformInfo, connector, logger}) {
        const permalinkID = connector.makePermalinkID({platformKind, platformSiteID, platformArchiveID, resourceID, branchID});
        const [permalinkData, created] = await dbConnector.insertOrUpdatePermalink({
            permalinkID,
            platformKind,
            platformSiteID,
            platformArchiveID,
            resourceID,
            branchID,
            dirty: false,
            permalinkKind: Consts.PermalinkKind.image_unfurling,
            permalinkInfo,
            platformInfo,
            timestamp: clock.now(),
        });

        //lazy image generation
        connector.generatePermalinkImage({logger, permalinkData}).catch(
            (err) => {
                logger.error(`Unexpected error in connector.generatePermalinkImage: ${err.message}`, err);
            });

        let permalinkObject = makePermalinkObject(permalinkData, connector);

        // in case of first creation, forcing the client to generate the image through the query parameter
        if (permalinkObject.image) {
            if (created === 1) {
                permalinkObject.image += '?q=thumbnail-only';
            } else {
                const extensionMatch = permalinkObject.image.match(/(\.\w+)$/);
                if (extensionMatch) {
                    permalinkObject.image = permalinkObject.image.replace(/(\.\w+)$/, '_thumbnail$1');
                }
            }
        }


        return {
            ...permalinkObject,
            projectName: resourceInfo.projectName,
            name: resourceInfo.name,
        };
    }

    return {
        flushToConnectorHoldingTheTruth,
        tryToSyncArchiveOnPlatformOffline,
        unloadUnusedArchiveJob,
        unloadSingleArchive,
        diskUsageTask,
        parseMultipartFormData,
        broadcastRTCMessage,
        acquireApplicationLockOnNewConnection,
        checkServerAPICredentials: checkServerAPICredentialsFactory(config),
        getBASAuthorizationHeaderString: getBASAuthorizationHeaderStringFactory(config),
        isAnonymousUser,
        createBARFromBuffer,
        getResourceNameAndIDs,
        makePermalinkObject,
        getPermalinksData,
        createOrUpdateImageUnfurling,
        getKindFromArchiveID,
    };
}

/**
 *
 * @param {JSDocTypes.Config} config
 * @returns {(req: JSDocTypes.Request) => Promise<boolean>}
 */
const checkServerAPICredentialsFactory = (config) => {
    const serverApiSecrets = config.getServerApiSecrets();
    return async (req) => {
        const secrets = await serverApiSecrets.getSecret(req.bas.logger);
        for (let { domain, secret } of secrets.getAllSecrets()) {
            if (checkBasicAuthOnRequest(req, domain, secret)) {
                return true;
            }
        }
        return false;
    };
};

/**
 *
 * @param {JSDocTypes.Config} config
 * @returns {(logger: JSDocTypes.Logger) => Promise<string>}
 */
const getBASAuthorizationHeaderStringFactory = (config) => {
    const serverApiSecrets = config.getServerApiSecrets();
    return async (logger) => {
        const secrets = await serverApiSecrets.getSecret(logger);
        let secret;
        try {
            secret = secrets.getFirstSecret('bas');
        } catch (e) {
            logger.error('Error getting BAS secret', e);
        }
        return secret ? "Basic " + Buffer.from('bas:' + secret).toString('base64') : '';
    };
};

// Inspiration from: https://stackoverflow.com/a/5827895
var walk_directory = function(dir, done) {
    var results = [];
    fs.readdir(dir, function(err, list) {
        if (err) return done(err);
        var i = 0;
        (function next() {
            var file = list[i++];
            if (!file) return done(null, results);
            file = path.resolve(dir, file);
            fs.stat(file, function(err, stat) {
                if (err) {
                    done(err);
                } else {
                    if (stat && stat.isDirectory()) {
                        walk_directory(file, function(err, res) {
                            if (err) {
                                done(err);
                            } else {
                                results = results.concat(res);
                                next();
                            }
                        });
                    } else {
                        results.push({
                            filePath: path.resolve(dir, file),
                            size: stat.size,
                        });
                        next();
                    }
                }
            });
        })();
    });
};

/**
 * 
 * @param {JSDocTypes.SessionManager} sessionManager 
 * @param {JSDocTypes.Logger} logger 
 * @returns {void}
 */
function startCheckingLockedQueries(sessionManager, logger) {
    logger = logger.getLogger({ subsystem: 'lockedQueries' });
    const intervalHandler = async function () {
        try {
            await sessionManager.withSession(logger, 'check lockedQueries', async ({ dbConnector }) => {
                const rows = await dbConnector.getLockedQueries();
                if (rows.length > 0) {
                    logger.info('Locked queries detected', {lockedQueries: rows});
                }
            });
        } catch (err) {
            logger.error('Unexpected error', err);
        }
    };
    setInterval(intervalHandler, 60 * 1000);
}

let isAtlassianNewLifeCycleActive = function (jwtToken) {
    try {
        let kid = jwtModule.getKeyId(jwtToken);
        return !!kid;
    } catch (e) {
        return false;
    }
}

let isAnonymousUser = function(user) {
    try {
        let userInfo = JSON.parse(user.USERINFO);
        return (userInfo && (userInfo.isAnonymous || userInfo.anonymous));
    } catch(e) {
        return false;
    }
}

let verifyCallFromAtlassian = function (jwtToken, expectedAudience, expectedIssuer, callback) {
    try {
        let kid = jwtModule.getKeyId(jwtToken);
        let alg = jwtModule.getAlgorithm(jwtToken);

        if (kid) {
            httpreq.get(`https://connect-install-keys.atlassian.com/${kid}`, function (err, res) {
                if (err) {
                    callback({error: "https://connect-install-keys.atlassian.com unreachable: " + err});
                } else {
                    if (res.statusCode === 200) {
                        let rsaPublicKey = res.body;
                        let jwtBody;
                        try {
                            jwtBody = jwtModule.decodeAsymmetric(jwtToken, rsaPublicKey, alg, false);
                        } catch (e) {
                            callback({error: "unexpected exception decoding jwtToken: " + e.message});
                            return;
                        }
                        if (jwtBody.aud.includes(expectedAudience) && jwtBody.iss === expectedIssuer) {
                            callback({});
                        } else {
                            callback({error: `unexpected aud or exp ${jwtBody.aud} or aud value ${jwtBody.exp}`});
                        }
                    } else {
                        callback({error: "unexpected status code " + res.statusCode});
                    }
                }
            });
        } else {
            callback({error: "malformed jwt: kid is not present"});
        }
    } catch (e) {
        callback({error: "unexpected exception: " + e});
    }
}

export {
    makeServerUtils,
    checkServerAPICredentialsFactory,
    startCheckingLockedQueries,
    verifyCallFromAtlassian,
    isAtlassianNewLifeCycleActive,
};
