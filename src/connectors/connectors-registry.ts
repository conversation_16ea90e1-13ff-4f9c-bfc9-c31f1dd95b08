/** @prettier */
import type { Logger } from '@balsamiq/logging';
import type { Config } from '../configLoader.ts';
import type { Metrics } from '../metrics.ts';
import type { RedisAdapter } from '../redisAdapter.ts';
import type { S3PermalinksImageStorageAdapter } from '../s3adapter.ts';
import type { ServerUtils } from '../server_utils.js';
import type { SessionManager } from '../session-manager.ts';
import type { Wireframe2image_adapter } from '../wireframe2image_adapter.ts';
import * as cloudConnectorModule from './cloud.js';
import * as confluenceConnectorModule from './confluence.js';
import * as jiraConnectorModule from './jira.js';
import * as webDemoConnectorModule from './webdemo.ts';
import JIRAForgeConnector from './jira-forge.js';

export function initializeConnectors(
    sessionManager: SessionManager,
    logger: Logger,
    metrics: Metrics,
    permalinkImageStorageAdapter: S3PermalinksImageStorageAdapter,
    config: Config,
    serverUtils: ServerUtils,
    w2iAdapter: Wireframe2image_adapter,
    redisAdapter: RedisAdapter
) {
    const jiraConnector = new jiraConnectorModule.JIRAConnector(
        sessionManager,
        logger,
        metrics,
        permalinkImageStorageAdapter,
        config,
        serverUtils,
        w2iAdapter,
        redisAdapter
    );

    const jiraForgeConnector = new JIRAForgeConnector(
        sessionManager,
        logger,
        metrics,
        permalinkImageStorageAdapter,
        config,
        serverUtils,
        w2iAdapter,
        redisAdapter
    );

    const confluenceConnector = new confluenceConnectorModule.ConfluenceConnector(
        sessionManager,
        logger,
        metrics,
        config,
        serverUtils,
        permalinkImageStorageAdapter,
        w2iAdapter,
        redisAdapter
    );

    const webDemoConnector = new webDemoConnectorModule.WebDemoConnector(
        sessionManager,
        logger,
        metrics,
        config,
        permalinkImageStorageAdapter,
        w2iAdapter,
        serverUtils,
        redisAdapter
    );

    const cloudConnector = new cloudConnectorModule.CloudConnector(
        sessionManager,
        logger,
        metrics,
        permalinkImageStorageAdapter,
        config,
        serverUtils,
        w2iAdapter,
        redisAdapter
    );

    const getConnector = function (kind: string) {
        if (kind === 'jira') {
            return jiraConnector;
        } else if (kind === 'jira-forge') {
            return jiraForgeConnector;
        } else if (kind === 'wd') {
            return webDemoConnector;
        } else if (kind === 'confluence') {
            return confluenceConnector;
        } else if (kind === 'cloud') {
            return cloudConnector;
        }
    };

    return {
        connectors: {
            jiraConnector: jiraConnector,
            jiraForgeConnector: jiraForgeConnector,
            webDemoConnector: webDemoConnector,
            confluenceConnector: confluenceConnector,
            cloudConnector: cloudConnector,
        },
        getConnector: getConnector,
    };
}
