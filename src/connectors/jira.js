//A connector that mimics a file repository like S3

import * as jwt from './lib/jwt.js';

import moment from 'moment';
import superagent from 'superagent';
import httpreq from 'httpreq';
import fs from 'fs';
import FormData from 'form-data';
import UrlModule from 'url';
import * as uuid from 'uuid';
import pathUtil from 'path';
import JSZip from 'jszip';
import { parseRedisKey, doAddKeyInRedis, pipeToNewRequest, queryStringToJSON, removePrefix, isImage, decodeJWT } from '../utils.ts';
import con from '../constants.ts';
import shortUUID from 'short-uuid';
import bmprUtilsMod from '@balsamiq/bmpr/lib/BmprUtils.js';
import { verifyCallFromAtlassian, isAtlassianNewLifeCycleActive } from '../server_utils.js';
import * as Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js'

const CONNECTION_TIMEOUT = 5000;
const anonAvatarURL = "https://bas20.balsamiq.com/bw-atlassian/pict/anonymous.png";
const jira_watcher_tag = 'jira_watcher';

import { callSaneFunctionFromLegacy, callWithLegacyCallback, isBASLegacyErrorObject } from '../calling-style.ts';
import { verifyAtlassianJWT, ************************, pipeStreamToResponse } from '../utils.ts';

var JIRAConnector = function (sessionManager, logger, metrics, permalinkImageStorageAdapter, config, serverUtils, w2iAdapter, redisAdapter) {
    this.logger = logger.getLogger({module: 'jira'});
    this.sessionManager = sessionManager;
    this.config = config;
    this.archiveIDPrefix = config.archiveIDPrefix;
    this.permalinkImageStorageAdapter = permalinkImageStorageAdapter;
    this.w2iAdapter = w2iAdapter;
    this.baseUrl = config.baseUrl;
    this.serverUtils = serverUtils;
    this.redisAdapter = redisAdapter;

    this.logger.info("#JIRA# Connector", {action: "startup"});
};

JIRAConnector.prototype.getUniqueUserId = function (user) {
    return "jira_" + user.USERNAME;
};

JIRAConnector.prototype.maxAge = function () {
    // 30 gg
    return 30 * 24 * 60 * 60 * 1000;
};

JIRAConnector.prototype.generateArchiveID = function (prefix = true) {
    return prefix ? this.archiveIDPrefix + generateArchiveID() : generateArchiveID();
};

JIRAConnector.prototype.allowsArchiveCreation = function (dbConnector, platformToken, callback) {
    callback({}); // no error = YES, you're allowed to create
};

JIRAConnector.prototype.getUserKeyFromPlatformToken = function ({ platformToken, dbConnector, logger }) {
    if (!platformToken) {
        return Promise.resolve(null);
    }

    return decodeJWT({
        connectorId: 'jira',
        dbConnector,
        platformToken,
        noVerify:false,
    }).then(result => {
        const { sub, iss } = result.claims;
        return `jira-${sub}-${iss}`;
    }).catch(err => {
        logger.info("Cannot decode JWT token", {fn: "getUserKeyFromPlatformToken", module: "jira", err: err.message, platformToken});
        return null;
    });
};

JIRAConnector.prototype.getRole = function(logger, dbConnector, platformToken, platformSiteID, platformArchiveID, userInfo, callback)
{
    var requesterClaims;
    logger = logger.getLogger({action: "getRole", module: "jira", platformArchiveID: platformArchiveID, platformSiteID: platformSiteID});
    var ret = decodePlatformSiteID(platformSiteID, userInfo);
    var platformInfo;

    if (ret.error) {
        callback(ret);
    } else {
        if (userInfo.isAnonymous) {
            // no platformToken has been provided, we are in a public share link scenario
            // FIX: security report: https://tracker.bugcrowd.com/balsamiq/submissions/3f345adf-c8c2-416e-b612-ad8c0ee32de6
            callSaneFunctionFromLegacy(dbConnector.hasPublicShare({platformKind: "jira", platformSiteID, platformArchiveID}), function(obj) {
                if (obj.hasPublicShare) {
                    if (userInfo.platformInfo) {
                        platformInfo = userInfo.platformInfo;
                        // retrieve the token from servlet
                        this.getAuthTokenFromPlatform(logger, platformInfo, function (obj) {
                            if (obj.error) {
                                logger.error("anonymous user, unable to get platform token for archive ", userInfo);
                                callback && callback({error: "anonymous user, unable to get token info for archive"});
                            } else {
                                platformToken = obj.platformToken;
                                logger.info("anonymous user, we fallback giving VIEWER permissions, platformArchiveID " + platformArchiveID, userInfo);
                                callback && callback({role: con.ROLE_VIEWER, platformToken: platformToken});
                            }
                        }.bind(this));
                    } else {
                        logger.error("anonymous user, missing platform info " + platformArchiveID, userInfo);
                        callback && callback({error: "anonymous user, missing platform info"});
                    }
                } else {
                    logger.error("anonymous user, no public share for archive " + platformSiteID + " " + platformArchiveID);
                    callback && callback({error: "anonymous user, no public share"});
                }
            }.bind(this));
        } else {
            dbConnector.getConnectorData("jira", ret.iss, function (obj) {
                var sharedSecret, secondsValidityLeft, exp;

                if (obj.error) {
                    callback && callback(obj);
                } else {
                    sharedSecret = obj.sharedSecret;
                    try {
                        requesterClaims = jwt.decode(platformToken, sharedSecret, false);
                    } catch (err) {
                        logger.error("invalid platform token: " + err, err, {platformToken});
                        requesterClaims = {};
                    }

                    if (requesterClaims && requesterClaims.iss && requesterClaims.iss === ret.iss) {
                        // Test expiration timestamp from the JWT
                        exp = requesterClaims.exp || 0;
                        secondsValidityLeft = exp - (new Date().getTime() / 1000);
                        if (secondsValidityLeft < 0) {
                            const action = dbConnector.sessionData.action || "unknown action";
                            logger.warn('WARNING JWT expired (' + (-Math.round(secondsValidityLeft)) + ' seconds ago)', {platformToken, userInfo, action});
                            // TODO: as a preliminary step we just log the case where the JWT is expired
                            // callback({error: 'JWT expired'});
                            // return;
                        }

                        getRoleAccordingToUserPermission(logger, dbConnector, obj, platformToken, userInfo.platformInfo, callback);
                    } else {
                        callback({error: "not authorized"});
                    }
                }
            });
        }
    }
};

JIRAConnector.prototype.getBufferFromTemplate = function(callback)
{
    var templateName = "NewProject";
    doGetBufferFromTemplate(templateName, callback);
};

function doGetBufferFromTemplate(templateName, callback) {
    var archiveID = generateArchiveID();
    var templatePath =  "./connectors/templates/" + con.BMPR_CURRENT_SCHEMA_VERSION + "/";

    fs.readFile(templatePath + templateName + ".bmpr", function (err, data) {
        if (err) {
            callback({error:err});
        } else {
            callback({buffer:data, id: archiveID});
        }
    });
}

JIRAConnector.prototype.userConnectedToArchive = function (logger, dbConnector, sessionToken, callback) {
    doAddKeyInRedis(makeRedisKey, this.redisAdapter, logger, sessionToken);
    callback && callback({});
};

JIRAConnector.prototype.create = function (logger, dbConnector, authToken, platformSiteID, platformArchiveName, platformInfo, buffer, callback)
{
    // authToken: JWT
    // platformSiteID: unique id = (iss, issueID)
    // platformArchiveName: attachment name without "bmpr" extension? (not needed)
    // platformInfo: {issueID, iss}
    // callback: function({newly_created_platformArchiveID})
    var issueID = platformInfo ? platformInfo.issueID : null;
    var notAmbiguousPlatformArchiveName = platformArchiveName;
    var filename;

    if (issueID && platformSiteID) {
        dbConnector.getPlatformInfoByPlatformSiteID(platformSiteID, function (obj) {
            var i = 0, inc = 1;
            if (obj.error) {
                callback && callback(obj);
            } else {
                if (obj.rows && obj.rows.length) {
                    // if there is a zombie archive with the same name (e.g. the attachment has been deleted directly)
                    // change the name adding a progressive number
                    while (i < obj.rows.length) {
                        if (obj.rows[i].PLATFORM_ARCHIVE_ID == notAmbiguousPlatformArchiveName) {
                            // zombie archive found
                            notAmbiguousPlatformArchiveName = platformArchiveName + "_" + inc;
                            i = 0;
                            inc++;
                        } else {
                            i++;
                        }
                    }
                }

                filename = notAmbiguousPlatformArchiveName + ".bmpr";

                doSaveToPlatform(logger, dbConnector, authToken, issueID, filename, buffer, function (obj) {
                    if (obj.error) {
                        callback && callback(obj);
                    } else {
                        callback && callback({
                            platformArchiveID: notAmbiguousPlatformArchiveName,
                            platformArchiveName: notAmbiguousPlatformArchiveName
                        });
                    }
                });
            }
        });
    } else {
        callback && callback({error: "missing parameters"});
    }
};

JIRAConnector.prototype.loadFromPlatform = function(logger, dbConnector, authToken, platformSiteID, platformArchiveID, options, platformInfo, callback)
{
    // authToken: JWT
    // platformSiteID: unique id = (iss, issueID)
    // platformArchiveID: attachment name without "bmpr" extention
    // platformArchiveName: attachment name without "bmpr" extention, need to be on sync with platformArchiveID [NOT NECESSARY]
    // platformInfo: {issueID, iss}, will be updated with the attachmentID
    // callback: function({id: barArchiveID, buffer: buffer})

    // JWT signature is verified in the doAuthFetch
    var claims = jwt.decode(authToken, '', true);
    var iss = claims.iss;
    var issueID = getIssueIDfromPlatformSiteID(platformSiteID, iss);
    var attachmentID;
    platformInfo = platformInfo ? platformInfo : {};
    doGetAttachmentIDfromPlatformArchiveID(dbConnector, issueID, platformArchiveID, authToken, this.config, function(obj) {
        if (obj.error) {
            callback && callback(obj);
        } else {
            attachmentID = obj.attachmentID;
            doJiraAttachmentAuthFetch(logger, dbConnector, authToken, attachmentID, true, function(obj) {
                if (obj.error) {
                    callback && callback(obj);
                } else {
                    // return updated platformInfo
                    obj.platformInfo = JSON.parse(JSON.stringify(platformInfo));
                    obj.platformInfo.attachmentID = attachmentID;
                    callback && callback(obj);
                }
            });
        }
    }.bind(this));
};

// options: { fromClose, fromRestore }
JIRAConnector.prototype.save = function(logger, dbConnector, user, archiveID, newPlatformArchiveName, archiveRevision, buffer, dump, options, callback)
{
    logger = logger.getLogger({action: "save", module: "jira"});
    var forceFlush = dump && dump.forceFlush;
    let config = this.config;
    dbConnector.getPlatformData(archiveID, function(platformData) {
        if (platformData.error) {
            callback(platformData);
        } else {
            if (platformData.PLATFORM_INFO) {
                try {
                    var platformInfo = JSON.parse(platformData.PLATFORM_INFO);
                    var issueID = platformInfo.issueID;
                    // In JIRA the platformArchiveName and the platformArchiveID need to be on sync
                    // We do not allow renaming of the attachment, i.e. the newPlatformArchiveName is ignored
                    var platformArchiveName = platformData.PLATFORM_ARCHIVE_ID;
                    var platformSiteID = platformData.PLATFORM_SITE_ID;
                    var platformArchiveID = platformData.PLATFORM_ARCHIVE_ID;
                    var prevAttachmentID = platformInfo.attachmentID;
                    var attachmentID;
                    var filename;
                } catch (e) {
                    logger.info("Unexpected error: malformed parameter: " + JSON.stringify(platformData));
                    callback({error: "Unexpected error: malformed parameter"});
                    return;
                }

                logger.info("saving: previous attachmentID is " + prevAttachmentID + (forceFlush ? " forcing flush " : ""));

                // if newPlatformArchiveName is null, the save is called in the /close API, otherwise we are called in the /fetch API
                if (forceFlush || platformInfo.archiveRevisionOnPlatform < archiveRevision) {
                    filename = platformArchiveName + ".bmpr";
                    doSaveToPlatform(logger, dbConnector, user.PLATFORM_TOKEN, issueID, filename, buffer, function (obj) {
                        if (obj.error) {
                            callback(obj);
                        } else {
                            attachmentID = obj.id;
                            platformInfo.attachmentID = attachmentID;
                            logger.info("saving: new attachmentID is " + attachmentID);
                            platformInfo.archiveRevisionOnPlatform = archiveRevision;
                            // TODO: we need to update only the platformInfo
                            dbConnector.updateArchivePlatformData(platformData.BAS_ARCHIVE_ID, platformData.PLATFORM_KIND, platformData.PLATFORM_SITE_ID, platformData.PLATFORM_ARCHIVE_ID, platformArchiveName, platformInfo, function(obj) {
                                if (obj.error)
                                {
                                    callback(obj);
                                } else
                                {
                                    logger.info("deleting prevAttachmentID: " + prevAttachmentID);
                                    doRemoveExt(logger, dbConnector, user.PLATFORM_TOKEN, prevAttachmentID, config, function (obj) {
                                        if (obj.error) {
                                            callback(obj);
                                        } else {
                                            callback({platformArchiveID: platformData.PLATFORM_ARCHIVE_ID});
                                        }
                                    }.bind(this));
                                }
                            }.bind(this));
                        }
                    }.bind(this));
                } else {
                    logger.info("saving unnecessary: archive revision is not changed");
                    callback({platformArchiveID: platformData.PLATFORM_ARCHIVE_ID, wasAlreadyUpdated: true});
                }
            } else {
                // might be already closed by someone else
                // the recovery is not trivial: the editor should reopen the archive and apply again the unsaved changes
                callback({error: "Unable to save: BAR already closed", busy: true});
            }
        }
    }.bind(this));
};

JIRAConnector.prototype.deleteFromPlatform = function (logger, dbConnector, authToken, platformSiteID, platformArchiveID, callback)
{
    // JWT signature will be verified in the doRemoveExt
    var claims = jwt.decode(authToken, '', true);
    var iss = claims.iss;
    var issueID = getIssueIDfromPlatformSiteID(platformSiteID, iss);
    let config = this.config;

    doGetAttachmentIDfromPlatformArchiveID(dbConnector, issueID, platformArchiveID, authToken, config, function (obj) {
        if (obj.error) {
            callback(obj);
        } else {
            doRemoveExt(logger, dbConnector, authToken, obj.attachmentID, config, callback);
        }
    });
};

let doCheckPermissionAndUpdateUserSession = function (sessionManager, serverUtils, logger, sessionToken, callback) {
    logger = logger.getLogger({
        action: "checkPermissionAndUpdateUserSession",
        module: "jira"
    });

    // logger.info("check permissions for session " + sessionToken);

    let finalise = function (sessionData, obj) {
        // release session
        sessionManager.releaseSession(sessionData, function (/*sessionObj*/) {
            if (obj.error) {
                logger.error(obj.error);
            }

            callback && callback(obj);
        });
    };

    sessionManager.createSession(logger, "checkPermissionAndUpdateUserSession", function (obj) {
        let sessionData, dbConnector;
        if (obj.error) {
            callback && callback({error: obj.error});
        } else {
            sessionData = obj;
            dbConnector = obj.dbConnector;
            dbConnector.getUser(sessionToken, function (obj) {
                if (obj.error) {
                    // session closed
                    finalise(sessionData, obj);
                } else {
                    let archiveID = obj.ARCHIVE_ID;
                    let permission = parseInt(obj.PERMISSIONS);
                    let platformToken = obj.PLATFORM_TOKEN;

                    logger.updateParams({archiveID: obj.ARCHIVE_ID})
                    dbConnector.getPlatformData(archiveID, function (obj) {
                        if (obj.error) {
                            finalise(sessionData, obj);
                        } else {
                            if (!obj.PLATFORM_ARCHIVE_ID) {
                                finalise(sessionData, {success: "nothing to do, archive is not loaded"});
                                return;
                            }

                            let platformInfo;
                            let platformSiteID = obj.PLATFORM_SITE_ID;
                            logger.updateParams({
                                platformSiteID: obj.PLATFORM_SITE_ID,
                                platformArchiveID: obj.PLATFORM_ARCHIVE_ID,
                            })
                            let ret = decodePlatformSiteID(platformSiteID);
                            if (ret.error) {
                                finalise(sessionData, ret);
                                return;
                            }

                            try {
                                platformInfo = JSON.parse(obj.PLATFORM_INFO);
                            } catch (err) {
                                finalise(sessionData, {error: err});
                                return;
                            }

                            dbConnector.getConnectorData("jira", ret.iss, function (obj) {
                                let instanceObj;
                                if (obj.error) {
                                    finalise(sessionData, obj);
                                } else {
                                    instanceObj = obj;
                                    getRoleAccordingToUserPermission(logger, dbConnector, instanceObj, platformToken, platformInfo, function (obj) {
                                        let role;
                                        if (obj.error) {
                                            finalise(sessionData, obj);
                                        } else {
                                            role = obj.role || con.ROLE_NO_ACCESS;
                                            // update the permission only if more restrictive (i.e. we avoid to give more permission to a read/anonymous only session)
                                            if (role < permission) {
                                                callSaneFunctionFromLegacy(dbConnector.updateSessionsPermissionsBySessionTokens({
                                                    tokens: [sessionToken],
                                                    permissions: [role]
                                                }), function (obj) {
                                                    serverUtils.broadcastRTCMessage(archiveID, undefined, undefined, undefined, {
                                                        operation: 'changeSessionRoles',
                                                        sessions: [{token: sessionToken, role}]
                                                    });

                                                    finalise(sessionData, obj);
                                                })
                                            } else {
                                                finalise(sessionData, {success: "nothing to do"});
                                            }
                                        }
                                    }.bind(this));
                                }
                            }.bind(this));
                        }
                    }.bind(this));
                }
            }.bind(this));
        }
    }.bind(this));
};

/**
 *
 * @param {string} sessionToken
 * @param {string} channelId
 * @returns {string}
 */
function makeRedisKey(sessionToken, channelId) {
    return jira_watcher_tag + '::' + sessionToken + "::" + channelId;
}


/**
 *
 * @param {JSDocTypes.SessionManager} sessionManager
 * @param {JSDocTypes.ServerUtils} serverUtils
 * @param {JSDocTypes.Logger} logger
 * @param {JSDocTypes.Metrics} metrics
 * @param {JSDocTypes.Config} config
 * @param {boolean} configureRedisForKeySpaceEvents
 * @param {JSDocTypes.RedisAdapter} redisAdapter
 */
let doRunRedisExpirationKeyListener = async function (sessionManager, serverUtils, logger, metrics, config, configureRedisForKeySpaceEvents, redisAdapter) {
    logger = logger.getLogger({module: "jira", action: "watcher"});

    const subRedisAdapter = redisAdapter.duplicate(logger);
    await subRedisAdapter.init();

    if (configureRedisForKeySpaceEvents) {
        logger.info("Jira: configuring redis server to notify keyspace events");
        try {
            await subRedisAdapter.enableKeyspaceEvents();
        } catch(err) {
            logger.warn("Jira: unable to set redis server to notify keyspace events: " + err);
        }
    }

    logger.info("Jira: starting the redis listener");
    await subRedisAdapter.subscribeToKeyspaceEvents(async (key, channel) => {
        try {
            const parsedKey = parseRedisKey(jira_watcher_tag, key);
            if (parsedKey && parsedKey.type === jira_watcher_tag) {
                try {
                    const result = await callWithLegacyCallback(
                        cb => doCheckPermissionAndUpdateUserSession(sessionManager, serverUtils, logger, parsedKey.sessionToken, cb)
                    );

                    if (!result.skip) {
                        await doAddKeyInRedis(makeRedisKey, redisAdapter, logger, parsedKey.sessionToken);
                    }
                } catch (error) {
                    if (error.message !== "Session closed") {
                        logger.error("Jira: ERROR renewing watcher for " + parsedKey.sessionToken + " " + error.message, error);
                    } else {
                        logger.info("Jira: session closed, no need to renew watcher for " + parsedKey.sessionToken);
                    }
                }
            }
        } catch (err) {
            logger.warn("Jira: catch exception on redis listener, ignore key expired, probably not a valid watcher " + channel + " " + err.message + ' ' + key);
        }
    });

    logger.info("Jira: Redis Listener subscribed to KeyspaceEvents");
};

function getAccessToken(instanceObj, userKey, userAccountId, scopes, callback) {
    var AUTHORIZATION_SERVER_URL = "https://oauth-2-authorization-server.services.atlassian.com",
        EXPIRY_SECONDS = 60,
        GRANT_TYPE = "urn:ietf:params:oauth:grant-type:jwt-bearer",
        SCOPES = scopes; // case-sensitive space-delimited as per the specification
    // (https://tools.ietf.org/html/rfc6749#section-3.3)

    // building JWT assertion
    var now = Math.floor(Date.now() / 1000),
        exp = now + EXPIRY_SECONDS;

    var jwtClaims;

    jwtClaims = {
        iss: "urn:atlassian:connect:clientid:" + instanceObj.oauthClientId,
        tnt: instanceObj.baseUrl,
        aud: AUTHORIZATION_SERVER_URL,
        iat: now,
        exp: exp
    };

    if (userAccountId) {
        jwtClaims.sub = "urn:atlassian:connect:useraccountid:" + userAccountId;
    } else {
        jwtClaims.sub = "urn:atlassian:connect:userkey:" + userKey;
    }

    var assertion = jwt.encode(jwtClaims, instanceObj.sharedSecret);

    var parameters = {
        grant_type: GRANT_TYPE,
        assertion: assertion,
        scope: SCOPES
    };

    superagent
        .post(AUTHORIZATION_SERVER_URL + '/oauth2/token')
        .timeout(CONNECTION_TIMEOUT)
        .set({
            'Accept': 'application/json'
        })
        .type('form')
        .send(parameters)
        .end(function (err, res) {
            if (err) {
                const errorMessage = "ERROR [" + (res?.statusCode || "unknown status code") + "]: Couldn't get access token from response " + err;
                callback({error: errorMessage});
            } else {
                callback({accessToken: res.body.access_token});
            }
    });
}

function getAccessTokenFromJWT(logger, dbConnector, platformToken, scopes, callback) {
    var claims, iss, userKey, userAccountId;
    logger = logger.getLogger({
        action: "getAccessTokenFromJWT",
        module: "jira"
    });
    try {
        // JWT signature will be checked right after
        claims = jwt.decode(platformToken, '', true);
        iss = claims.iss;

        if (claims.sub) {
            // GDPR: userKey deprecated, use urn:atlassian:connect:useraccountid instead
            userAccountId = claims.sub;
        } else if (claims.context && claims.context.user && claims.context.user.userKey) {
            userKey = claims.context.user.userKey;
        }
    } catch (error) {
        // malformed JWT, jwt.decode can raise an exception
        callback({error: error});
        return;
    }

    dbConnector.getConnectorData("jira", iss, function (instanceObj) {
        if (instanceObj.sharedSecret && instanceObj.oauthClientId && (userKey || userAccountId)) {
            try {
                // shared secret could have changed in the meantime
                jwt.decode(platformToken, instanceObj.sharedSecret, true);
            } catch (error) {
                logger.error("JWT signature check failed " + error, error);
                callback({error: "JWT signature check failed"});
                return;
            }

            if (userAccountId) {
                logger.info("userAccountId " + userAccountId, instanceObj);

                getAccessToken(instanceObj, userKey, userAccountId, scopes, function (obj) {
                    if (obj.error) {
                        callback(obj);
                    } else {
                        logger.info("TOKEN access token status " + (userKey || userAccountId));
                        callback({
                            accessToken: obj.accessToken,
                            iss: iss,
                            userKey: userKey,
                            userAccountId: userAccountId,
                            instanceObj: instanceObj
                        });
                    }
                });
            } else {
                // deprecated userKey
                logger.warn("GDPR Unexpected use of claims.context.user.userKey. Claim sub " + claims.sub, instanceObj);
                callback({error: "Unexpected use of claims.context.user.userKey. Claim sub " + claims.sub});
            }
        } else {
            logger.warn("Missing userKey or accountID " + JSON.stringify(claims) + " jwt: " + platformToken);
            callback({
                error: "unable to retrieve the oauth token, missing userKey or userAccountId " + JSON.stringify(claims) + " jwt: " + platformToken,
                migrationGDPR: true
            });
        }
    });
}

function decodePlatformSiteID(platformSiteID, userInfo) {
    const regex = /(.*)_(.*)$/;
    let m;

    if ((m = regex.exec(platformSiteID)) !== null) {
        if (m.length !== 3) {
            return { error: "malformed platformSiteID" };
        } else {
            // if userInfo is provided, we check the consistency of the platformSiteID
            if (userInfo?.platformInfo?.issueID && userInfo?.platformInfo?.issueID !== m[2]) {
                return { error: "inconsistent platformSiteID" };
            }

            // if userInfo is NOT provided, we only check if the platformSiteID is well formed
            return {
                iss: m[1],
                contentID: m[2]
            }
        }
    } else {
        // in Jira platformSiteID cannot be null/undefined/empty string
        return { error: "malformed platformSiteID" };
    }
}

function getRoleAccordingToUserPermission(logger, dbConnector, instanceObj, platformToken, platformInfo, callback) {
    var scopes = "WRITE ACT_AS_USER";
    var queryString = "&permissions=EDIT_ISSUES,CREATE_ATTACHMENTS,DELETE_OWN_ATTACHMENTS,BROWSE_PROJECTS";
    logger = logger.getLogger({action: "getRoleAccordingToUserPermission", module: "jira"});
    var path;

    if (platformInfo && platformInfo.issueID) {
        path = '/rest/api/latest/mypermissions?issueId=' + platformInfo.issueID;
        try {
            // JWT signature has been already tested
            var claims = jwt.decode(platformToken, '', true);
            var iss = claims.iss;
            var userKey, userAccountId;
            if (claims.sub) {
                // GDPR: userKey deprecated, use urn:atlassian:connect:useraccountid instead
                userAccountId = claims.sub;
            } else if (claims.context && claims.context.user && claims.context.user.userKey) {
                userKey = claims.context.user.userKey;
            }

            if (instanceObj.sharedSecret && instanceObj.oauthClientId && (userKey || userAccountId)) {
                if (userAccountId) {
                    try {
                        jwt.decode(platformToken, instanceObj.sharedSecret, false);
                    } catch (error) {
                        callback && callback({error: "JWT signature check failed"});
                        return;
                    }

                    dbConnector.pauseSession(function (obj) {
                        var resumeSession;
                        if (obj.error) {
                            callback && callback(obj);
                        } else {
                            resumeSession = obj.resumeSession;
                            var finalise = function (userPermission) {
                                var role;
                                resumeSession("jira-hasUserEditOrBrowsePermission", function (obj) {
                                    if (obj.error) {
                                        callback && callback(obj);
                                    } else {
                                        if (userPermission.hasEditPermissions) {
                                            role = con.ROLE_ADMIN;
                                        } else if (userPermission.hasBrowsePermissions) {
                                            role = con.ROLE_VIEWER;
                                        } else {
                                            role = con.ROLE_NO_ACCESS;
                                        }
                                        callback && callback({
                                            role: role
                                        });
                                    }
                                });
                            };

                            getAccessToken(instanceObj, userKey, userAccountId, scopes,function (obj) {
                                var url = instanceObj.baseUrl + path + queryString;
                                if (obj.error) {
                                    finalise(obj);
                                } else {
                                    superagent
                                        .get(url)
                                        .timeout(CONNECTION_TIMEOUT)
                                        .set({
                                            "X-Atlassian-Token": "no-check",
                                            "Authorization": "Bearer " + obj.accessToken,
                                            'Accept': 'application/json',
                                        }).end(function (err, res) {
                                            if (err) {
                                                finalise({error: err.message || "unexpected error: " + err.status});
                                            } else if (res.status !== 200) {
                                                finalise({error: "unexpected status code from atlassian server: " + res.status});
                                            } else {
                                                const resp = res.body;
                                                let hasEditPermissions, hasBrowsePermissions;
                                                try {
                                                    hasEditPermissions = resp.permissions.EDIT_ISSUES.havePermission && resp.permissions.CREATE_ATTACHMENTS.havePermission && resp.permissions.DELETE_OWN_ATTACHMENTS.havePermission;
                                                    hasBrowsePermissions = resp.permissions.BROWSE_PROJECTS.havePermission;
                                                } catch (error) {
                                                    // we log the error we fall back to read only
                                                    logger.error("Unexpected error: " + error, error);
                                                }

                                                finalise({
                                                    hasBrowsePermissions: hasBrowsePermissions,
                                                    hasEditPermissions: hasEditPermissions
                                                });
                                            }
                                    });
                                }
                            });
                        }
                    })
                } else {
                    // This should never happen: monitoring JWT deprecated impersonation API
                    logger.error("GDPR Unexpected use of claims.context.user.userKey ", null, {instanceObj});
                    callback({error: "Unexpected use of claims.context.user.userKey. Claim sub " + claims.sub});
                }
            } else {
                // This should never happen: monitoring JWT deprecated impersonation API
                logger.error("GDPR still using JWT authentication " + iss);
                callback({error: "Unexpected error, missing authentication parameters"});
            }
        } catch (error) {
            callback && callback({error: error});
        }
    } else {
        callback && callback({error: "malformed parameter"});
    }
}


function doRemoveExt(logger, dbConnector, jwtToken, attachmentID, config, callback) {
    try {
        logger = logger.getLogger({action: "doRemoveExt", module: "jira"});
        var path = "/rest/api/latest/attachment/" + attachmentID;
        var userKey, userAccountId;
        // JWT signature will be verified right after
        var claims = jwt.decode(jwtToken, '', true);
        var iss = claims.iss;
        if (claims.sub) {
            // GDPR: userKey deprecated, use urn:atlassian:connect:useraccountid instead
            userAccountId = claims.sub;
        } else if (claims.context && claims.context.user && claims.context.user.userKey) {
            userKey = claims.context.user.userKey;
        }

        var finalise = function (err, res){
                var result = {result: res};
                if (err){
                    result.error = "Failed to delete the resource: " + err;
                } else {
                    if (res.statusCode == 204)
                    {
                        // ok
                    } else if (res.statusCode == 404) {
                        // already deleted?
                        result.error = "Unable to delete the attachment: file not found";
                        result.busy = true;
                    }
                    else
                    {
                        result.error = "Forbidden: you don't have permission to remove attachments from this issue";
                    }
                }
                callback && callback(result);
        };

        dbConnector.getConnectorData("jira", iss, function(instanceObj) {
            var scopes = "DELETE ACT_AS_USER";
            if (instanceObj.error || !instanceObj.sharedSecret)
            {
                var ret = instanceObj.error ? instanceObj : {error: "The instance seems to non be authenticated. Please try to reinstall the plugin"};
                callback && callback(ret);
            } else
            {
                if (instanceObj.oauthClientId && (userKey || userAccountId)) {
                    try {
                        jwt.decode(jwtToken, instanceObj.sharedSecret, false);
                    } catch (error) {
                        callback && callback({error: "JWT signature check failed"});
                        return;
                    }

                    if (userAccountId) {
                        getAccessToken(instanceObj, userKey, userAccountId, scopes, function (obj) {
                            var url = instanceObj.baseUrl + path;
                            if (obj.error) {
                                callback(obj);
                            } else {
                                // GDPR: force x-atlassian-force-account-id header
                                httpreq.delete(url, {
                                    url: url,
                                    json: true,
                                    headers: {
                                        "X-Atlassian-Token": "no-check",
                                        "Authorization": "Bearer " + obj.accessToken,
                                        "accept": "application/json",
                                        "x-atlassian-force-account-id": "true"
                                    }
                                }, finalise);
                            }
                        })
                    } else {
                        logger.warn("GDPR Unexpected use of claims.context.user.userKey ", instanceObj);
                        callback({error: "Unexpected use of claims.context.user.userKey. Claim sub " + claims.sub});
                    }
                } else {
                    // TODO: Monitoring JWT deprecated impersonation API
                    var sharedSecret = instanceObj.sharedSecret;

                    var jwtPayload = createJwtPayloadExt({
                        'method': 'DELETE',
                        'path': path,
                        'query': ''
                    }, config, instanceObj);

                    var newJwtToken = jwt.encode(jwtPayload, sharedSecret, 'HS256');
                    var url = instanceObj.baseUrl + path;
                    httpreq.delete(url, {
                        headers: {
                            'Authorization': 'JWT ' + newJwtToken
                        }
                    }, finalise);
                }
            }
        });
    } catch (e)
    {
        callback && callback({error:{message: "Failed to delete the resource: " + e.message}});
    }
}


function doRemove(dbConnector, iss, attachmentID, config, callback) {
    try {
        var path = "/rest/api/latest/attachment/" + attachmentID;

        dbConnector.getConnectorData("jira", iss, function(instanceObj) {
            if (instanceObj.error || !instanceObj.sharedSecret)
            {
                var ret = instanceObj.error ? instanceObj : {error: "The instance seems to non be authenticated. Please try to reinstall the plugin"};
                callback && callback(ret);
            } else
            {
                var sharedSecret = instanceObj.sharedSecret;

                var jwtPayload = createJwtPayloadExt({
                    'method': 'DELETE',
                    'path': path,
                    'query': ''
                }, config, instanceObj);

                var jwtToken = jwt.encode(jwtPayload, sharedSecret, 'HS256');
                var url = instanceObj.baseUrl + path;

                // GDPR: force x-atlassian-force-account-id header
                httpreq.delete(url, {
                    headers:{
                        "x-atlassian-force-account-id": "true",
                        'Authorization': 'JWT ' + jwtToken
                    }
                }, function (err, res){
                    var result = {result: res};
                    if (err){
                        result.error = "Failed to delete the resource: " + err;
                    } else {
                        if (res.statusCode == 204)
                        {
                            // ok
                        } else if (res.statusCode == 404) {
                            // already deleted?
                            result.error = "Unable to delete the attachment: file not found";
                            result.busy = true;
                        }
                        else
                        {
                            result.error = "Forbidden: you don't have permission to remove attachments from this issue";
                        }
                    }
                    callback && callback(result);
                });
            }
        });
    } catch (e)
    {
        callback && callback({error:{message: "Failed to delete the resource: " + e.message}});
    }
}

function doSaveToPlatform(logger, dbConnector, authToken, issueID, filename, buffer, callback)
{
    logger = logger.getLogger({action: "doSaveToPlatform", module: "jira"});
    var path = '/rest/api/latest/issue/' + issueID + '/attachments';

    try {
        // JWT signature will be verified right after
        var claims = jwt.decode(authToken, '', true);
        var iss = claims.iss;
        var userKey, userAccountId;
        if (claims.sub) {
            // GDPR: userKey deprecated, use urn:atlassian:connect:useraccountid instead
            userAccountId = claims.sub;
        } else if (claims.context && claims.context.user && claims.context.user.userKey) {
            userKey = claims.context.user.userKey;
        }

        dbConnector.getConnectorData("jira", iss, function(instanceObj) {
            var scopes = "WRITE ACT_AS_USER";
            if (instanceObj.error || !instanceObj.sharedSecret) {
                var ret = instanceObj.error ? instanceObj : {error: "The instance seems to non be authenticated. Please try to reinstall the plugin"};
                callback && callback(ret);
            } else {
                if (instanceObj.oauthClientId && (userKey || userAccountId)) {
                    try {
                        jwt.decode(authToken, instanceObj.sharedSecret, false);
                    } catch (error) {
                        callback && callback({error: "JWT signature check failed"});
                        return;
                    }

                    if (userAccountId) {
                        getAccessToken(instanceObj, userKey, userAccountId, scopes, function (obj) {
                            var url = instanceObj.baseUrl + path;
                            var headers;
                            if (obj.error) {
                                callback(obj);
                            } else {
                                // GDPR: force x-atlassian-force-account-id header
                                headers = {
                                    "X-Atlassian-Token": "no-check",
                                    "Authorization": "Bearer " + obj.accessToken,
                                    "accept": "application/json",
                                    "x-atlassian-force-account-id": "true"
                                };
                                uploadBinaryExt(url, headers, filename, buffer, function (p_obj) {
                                    if (p_obj.error) {
                                        callback && callback(p_obj);
                                    } else {
                                        callback && callback({id: p_obj.id});
                                    }
                                });
                            }
                        });
                    } else {
                        logger.warn("GDPR Unexpected use of claims.context.user.userKey ", {instanceObj});
                        callback({error: "Unexpected use of claims.context.user.userKey. Claim sub " + claims.sub});
                    }
                } else {
                    logger.error("Unexpected error: missing oauthClientId or userKey " + userKey + "or userAccountId " + userAccountId, {instanceObj});
                    callback({error: "Unexpected error: missing oauthClientId or userKey or userAccountId"});
                }
            }
        });
    } catch (e)
    {
        callback && callback({error:{message: "Failed to save the resource: " + e.message}});
    }
}

function doGetListExt(dbConnector, issueID, authToken, extname, config, callback)
{
    var issuer = "unknown";
    try {
        // JWT signature will be verified right after
        var claims = jwt.decode(authToken, '', true);
        var path = "/rest/api/latest/issue/" + issueID;
        var fields = '*all';
        issuer = claims.iss;

        dbConnector.getConnectorData("jira", claims.iss, function(obj)
        {
            if (obj.error || !obj.sharedSecret)
            {
                var ret = obj.error ? obj : {error: "The instance seems to non be authenticated. Please try to reinstall the plugin"};
                callback && callback(ret);
            } else {
                var sharedSecret = obj.sharedSecret;

                try {
                    jwt.decode(authToken, obj.sharedSecret, false);
                } catch (error) {
                    callback && callback({error: "JWT signature check failed"});
                    return;
                }

                var jwtPayload = createJwtPayloadExt({
                    'method': 'GET',
                    'path': path,
                    'query': {
                        fields: fields
                    }
                }, config, obj);

                var jwtToken = jwt.encode(jwtPayload, sharedSecret, 'HS256');
                var url = obj.baseUrl + path + '?fields=' + fields;

                // GDPR: force x-atlassian-force-account-id header
                httpreq.get(url, {
                    timeout: CONNECTION_TIMEOUT,
                    headers:{
                        "x-atlassian-force-account-id": "true",
                        'Authorization': 'JWT ' + jwtToken
                    }
                }, function (err, res) {
                    var attachmentLength;
                    if (err) {
                        callback && callback({error: "Failed to list attachments for selected issue: error " + err + " " + issuer, issueID: issueID, result: res});
                    } else {
                        if (res.statusCode === 200)
                        {
                            var resp = JSON.parse(res.body),
                                i, l, attachment,
                                list = {}, totalLength = 0;

                            if (extname && extname.length>0)
                            {
                                for (i=0; i<extname.length; i++)
                                {
                                    list[extname[i]] = [];
                                }
                            } else
                            {
                                extname = [".*"];
                                list[".*"] = [];
                            }

                            attachmentLength = resp.fields && resp.fields.attachment ? resp.fields.attachment.length : 0;
                            for (i = 0; i < attachmentLength; i++) {
                                attachment = resp.fields.attachment[i];
                                for (l=0; l<extname.length; l++)
                                {
                                    if (extname[l] == ".*" || pathUtil.extname(attachment.filename) === extname[l]) {
                                        var name = pathUtil.basename(attachment.filename, pathUtil.extname(attachment.filename));
                                        list[extname[l]].push({id: attachment.id, filename: name, created: attachment.created});
                                        totalLength++
                                    }
                                }
                            }
                            callback && callback({id: issueID, data: list, totalLength: totalLength});
                        } else if (res.statusCode === 401)
                        {
                            callback && callback({error: "Failed to list the attachments: Unauthorized (401) " + " " + issuer, issueID: issueID, result: res});
                        } else {
                            callback && callback({error: "Failed to list the attachments for selected issue, status code: " + res.statusCode + " " + issuer, issueID: issueID, result: res});
                        }
                    }
                });
            }
        });
    } catch (e)
    {
        callback && callback({error:{message: "Unexpected exception, failed to list the resources: " + e.message + " " + issuer}});
    }
}

function doGetAttachmentMetadata(dbConnector, attachmentID, authToken, config, callback)
{
    var issuer = "unknown";
    try {
        // JWT signature will be verified right after
        var claims = jwt.decode(authToken, '', true);
        var path = "/rest/api/latest/attachment/" + attachmentID;
        issuer = claims.iss;

        dbConnector.getConnectorData("jira", claims.iss, function(obj)
        {
            if (obj.error || !obj.sharedSecret)
            {
                var ret = obj.error ? obj : {error: "The instance seems to non be authenticated. Please try to reinstall the plugin"};
                callback && callback(ret);
            } else {
                var sharedSecret = obj.sharedSecret;

                try {
                    jwt.decode(authToken, sharedSecret, false);
                } catch (error) {
                    callback && callback({error: "JWT signature check failed"});
                    return;
                }

                var jwtPayload = createJwtPayloadExt({
                    'method': 'GET',
                    'path': path
                }, config, obj);

                var jwtToken = jwt.encode(jwtPayload, sharedSecret, 'HS256');
                var url = obj.baseUrl + path;

                // GDPR: force x-atlassian-force-account-id header
                httpreq.get(url, {
                    timeout: CONNECTION_TIMEOUT,
                    headers:{
                        "x-atlassian-force-account-id": "true",
                        'Authorization': 'JWT ' + jwtToken
                    }
                }, function (err, res) {
                    if (err) {
                        callback && callback({error: "Failed to get attachment metadata: " + err + " " + issuer});
                    } else {
                        if (res.statusCode === 200) {
                            var resp = JSON.parse(res.body);
                            callback && callback(resp);
                        } else if (res.statusCode === 401) {
                            callback && callback({error: "Failed to get attachment metadata: Unauthorized (401) " + " " + issuer, code: res.statusCode});
                        } else {
                            callback && callback({error: "Failed to get attachment metadata: " + res.statusCode + " " + issuer, code: res.statusCode});
                        }
                    }
                });
            }
        });
    } catch (e)
    {
        callback && callback({error:{message: "Unexpected exception, failed to list the resources: " + e.message + " " + issuer}});
    }
}


function doGetAttachmentIDfromPlatformArchiveID(dbConnector, issueID, platformArchiveID, authToken, config, callback) {
    // platformArchiveID: attachment filename without ".bmpr" extention
    doGetListExt(dbConnector, issueID, authToken, [".bmpr"], config, function(obj) {
        // list the attachments, match the name and select the latest one
        var i, list, candidateCreated = null, resp = null;
        if (obj.error) {
            callback(obj);
        } else {
            list = obj.data[".bmpr"];
            for (i=0; i<list.length; i++) {
                if (list[i].filename === platformArchiveID) {
                    var created;
                    created = moment(list[i].created);
                    if (resp) {
                        if (created.isAfter(candidateCreated)) {
                            resp = list[i];
                            candidateCreated = created;
                        }
                    } else {
                        resp = list[i];
                        candidateCreated = moment(resp.created);
                    }
                }
            }
            if (resp) {
                callback({attachmentID: resp.id});
            } else {
                callback({error: "Unknown archive: " + platformArchiveID});
            }
        }
    });
}

//function doGetPermission(username, permission, issueKey, callback) {
//
//    try {
//        var claims = jwt.decode(authToken, '', true);
//        var path = "/rest/api/latest/user/permission/search";
//        var query = "username" + username + "&permissions"+ permission + "&issueKey"+ issueKey;
//
//        dbConnector.getConnectorData("jira", claims.iss, function(obj)
//        {
//            if (obj.error || !obj.sharedSecret)
//            {
//                var ret = obj.error ? obj : {error: "The instance seems to non be authenticated. Please try to reinstall the plugin"};
//                callback && callback(ret);
//            } else {
//                var sharedSecret = obj.sharedSecret;
//
//                var jwtPayload = createJwtPayload({
//                    'method': 'GET',
//                    'path': path,
//                    'query': query
//                }, obj.clientKey);
//
//                var jwtToken = jwt.encode(jwtPayload, sharedSecret, 'HS256');
//                var url = obj.baseUrl + path + "?" + query;
//
//                httpreq.get(url, {
//                    headers: {
//                     'Authorization': 'JWT ' + jwtToken
//                    }
//                }, function (err, res) {
//                    if (err) {
//                        callback && callback({error: "Failed to list the resources: " + err});
//                    } else {
//                        if (res.statusCode === 200)
//                        {
//                            var resp = JSON.parse(res.body);
//                            // TODO
//                            callback && callback(resp);
//                        } else if (res.statusCode === 401)
//                        {
//                            callback && callback({error: "Failed to get permission: Unauthorized (401)"});
//                        } else {
//                            callback && callback({error: "Failed to to get permission: " + res.statusCode});
//                        }
//                    }
//                });
//            }
//        });
//    } catch (e)
//    {
//        callback && callback({error:{message: "Failed to get permission: " + e.message}});
//    }
//}

function doGetLicense(dbConnector, authToken, config, callback) {
    try {
        // JWT signature will be tested right after
        var claims = jwt.decode(authToken, '', true);
        var path, key;

        dbConnector.getConnectorData("jira", claims.iss, function (obj) {
            if (obj.error || !obj.sharedSecret) {
                var ret = obj.error ? obj : {error: "The instance seems to non be authenticated. Please try to reinstall the plugin"};
                callback && callback(ret);
            } else {
                key = obj.key || config.jiraNamespace;
                path = "/rest/atlassian-connect/1/addons/" + key;

                var sharedSecret = obj.sharedSecret;

                try {
                    jwt.decode(authToken, sharedSecret, false);
                } catch (error) {
                    callback && callback({error: "JWT signature check failed"});
                    return;
                }

                var jwtPayload = createJwtPayloadExt({
                    'method': 'GET',
                    'path': path,
                    'query': ''
                }, config, obj);

                var jwtToken = jwt.encode(jwtPayload, sharedSecret, 'HS256');
                var url = obj.baseUrl + path;

                // GDPR: force x-atlassian-force-account-id header
                httpreq.get(url, {
                    timeout: CONNECTION_TIMEOUT,
                    headers:{
                        "x-atlassian-force-account-id": "true",
                        'Authorization': 'JWT ' + jwtToken
                    }
                }, function (err, res) {
                    if (err) {
                        callback && callback({error: "Unable to retrieve license info: " + err});
                    } else {
                        if (res.statusCode === 200) {
                            var resp, tmp;
                            try {
                                tmp = JSON.parse(res.body);
                                if (tmp.license) {
                                    resp = {
                                        valid: tmp.license.active,
                                        evaluation: tmp.license.evaluation
                                    };
                                } else {
                                    // unlicensed, we still allow read only mode
                                    resp = {valid: false, evaluation: false};
                                }
                            } catch (e) {
                                resp = {valid: false, evaluation: false};
                            }
                            callback && callback(resp);
                        } else if (res.statusCode === 401) {
                            callback && callback({error: "Unable to retrieve license info: Unauthorized (401)"});
                        } else if (res.statusCode === 404) {
                            callback && callback({error: "Unable to retrieve license info: Not found (404)"});
                        }
                        else {
                            callback && callback({error: "Unable to retrieve license info: " + res.statusCode});
                        }
                    }
                });
            }
        });
    } catch (e) {
        callback && callback({error: {message: "Unable to retrieve license info: " + e.message}});
    }
}

// TODO integrate with doGetList
function doGetAttachmentList(dbConnector, issueID, authToken, extname, config, callback)
{
    try {
        // JWT signature will be verified right after
        var claims = jwt.decode(authToken, '', true);
        var path = "/rest/api/latest/issue/" + issueID;

        dbConnector.getConnectorData("jira", claims.iss, function(obj)
        {
            if (obj.error || !obj.sharedSecret)
            {
                var ret = obj.error ? obj : {error: "The instance seems to non be authenticated. Please try to reinstall the plugin"};
                callback && callback(ret);
            } else {
                var sharedSecret = obj.sharedSecret;

                try {
                    jwt.decode(authToken, sharedSecret, false);
                } catch (error) {
                    callback && callback({error: "JWT signature check failed"});
                    return;
                }

                var jwtPayload = createJwtPayloadExt({
                    'method': 'GET',
                    'path': path,
                    'query': ''
                }, config, obj);

                var jwtToken = jwt.encode(jwtPayload, sharedSecret, 'HS256');
                var url = obj.baseUrl + path;

                // GDPR: force x-atlassian-force-account-id header
                httpreq.get(url, {
                    timeout: CONNECTION_TIMEOUT,
                    headers:{
                        "x-atlassian-force-account-id": "true",
                        'Authorization': 'JWT ' + jwtToken
                    }
                }, function (err, res) {
                    if (err) {
                        callback && callback({error: "Failed to list the attachments: " + err});
                    } else {
                        if (res.statusCode === 200)
                        {
                            var resp = JSON.parse(res.body),
                                l, attachment, attachments;

                            attachments = [];
                            l = resp.fields.attachment.length;

                            for (var i = 0; i < l; i++) {
                                attachment = resp.fields.attachment[i];
                                if (!extname || pathUtil.extname(attachment.filename) == extname) {
                                    attachments.push(attachment);
                                }
                            }
                            callback && callback({key: resp.key, attachment: attachments});
                        } else if (res.statusCode === 401)
                        {
                            callback && callback({error: "Failed to list the attachments: Unauthorized (401)"});
                        } else {
                            callback && callback({error: "Failed to list the attachments: " + res.statusCode});
                        }
                    }
                });
            }
        });
    } catch (e)
    {
        callback && callback({error:{message: "Failed to list the resources: " + e.message}});
    }
}

// In Jira we avoid to rename the archive/file on the platform: platformArchiveID e platformArchiveName need to be on sync
JIRAConnector.prototype.setPlatformArchiveName = function (platformArchiveID, authToken, platformArchiveName, callback)
{
    //do nothing
    callback({skipPlatformInfoUpdate: true});
};

JIRAConnector.prototype.authorizedForArchive = function(logger, sessionData, platformArchiveID, platformSiteID, platformInfo, platformToken, callback) {
    var requesterClaims, archivedClaims, ret;
    logger = logger.getLogger({action: "authorizedForArchive", module: "jira", platformArchiveID: platformArchiveID, platformSiteID: platformSiteID});
    var dbConnector = sessionData.dbConnector;

    dbConnector.getConnectorData("jira", platformInfo.iss, function(obj){
        var sharedSecret;

        if (obj.error) {
            callback && callback(obj);
        } else {
            sharedSecret = obj.sharedSecret;
            try {
                requesterClaims = jwt.decode(platformToken, sharedSecret, false);
            } catch (e) {
                logger.info("permalink is embedded in another platform");
                requesterClaims = {};
            }

            try {
                // shared secret could have changed in the meantime
                archivedClaims = jwt.decode(platformInfo.jwt, '', true);
            } catch (e) {
                logger.error("token stored in platform info is not valid " + platformInfo.jwt);
                archivedClaims = {};
            }

            if (requesterClaims && requesterClaims.iss && archivedClaims && archivedClaims.iss
                && requesterClaims.iss === archivedClaims.iss) {
                ret = {success: true};
            } else {
                ret = {error: "permalink is not fully authorized for this platform"};
            }

            callback && callback(ret);
        }
    });
};

JIRAConnector.prototype.storePermalinkImage = async function ({dataStream, mimeType, permalinkID, permalinkInfo}) {
    const format = permalinkInfo?.image?.format ? permalinkInfo.image.format : "png";
    const dataResidency = permalinkInfo?.dataResidency ?? this.config.defaultDataResidencyName;
    return await this.permalinkImageStorageAdapter.uploadPermalinkImage({
        dataStream,
        mimeType,
        permalinkID,
        platformKind: 'jira',
        format,
        dataResidency,
    });
};

JIRAConnector.prototype.deletePermalinkImages = async function ({permalinkIDs, format = "png", dataResidency = this.config.defaultDataResidencyName}) {
    return await this.permalinkImageStorageAdapter.deletePermalinkImages({
        permalinkIDs,
        platformKind: 'jira',
        format,
        dataResidency,
    });
};

JIRAConnector.prototype.deletePermalinkThumbnailImages = async function ({permalinkIDs, format = "png", dataResidency = this.config.defaultDataResidencyName}) {
    return await this.permalinkImageStorageAdapter.deletePermalinkThumbnailImages({
        permalinkIDs,
        platformKind: 'jira',
        format,
        dataResidency,
    });
};

JIRAConnector.prototype.generatePermalinkImage = async function ({logger, permalinkData, attachment, suffix}) {
    const {platformKind, platformSiteID, platformArchiveID, resourceID, branchID, platformInfo, permalinkInfo} = permalinkData;
    let ret = {};
    try {
        if (this.w2iAdapter) {
            await this.w2iAdapter.invoke({
                baseUrl: this.baseUrl,
                branchID: branchID,
                resourceID: resourceID,
                platformArchiveID: platformArchiveID,
                platformSiteID: platformSiteID,
                platformInfo: platformInfo,
                platformToken: platformInfo.jwt,
                platformKind: platformKind,
                permalinkInfo,
                permalinkID: [permalinkData.permalinkID, suffix].join(''),
                authorizationHeaderString: await this.serverUtils.getBASAuthorizationHeaderString(logger),
                thumbnailSize: 392,
                dataResidency: this.config.defaultDataResidencyName,
            });
        }
        else {
            // attachment format is "png" by design
            if (attachment && attachment.file && attachment.mimeType) {
                ret = await this.storePermalinkImage({
                    dataStream: attachment.file,
                    mimeType: attachment.mimeType,
                    permalinkID: permalinkData.permalinkID,
                });
            }
        }
    }
    catch (error) {
        // error action handled by the caller
        throw error;
    }

    return ret;
};

JIRAConnector.prototype.generateSnapshot = async function ({logger, platformKind, platformSiteID, platformArchiveID, resourceID, branchID, platformInfo}) {
    //TODO: not implemented
    throw new Error("Not implemented");
};

JIRAConnector.prototype.getPermalink = function ({dbConnector, permalinkData, releaseSessionIfNotReleased, req, res}, cb) {
    callSaneFunctionFromLegacy(this.asyncGetPermalink({dbConnector, permalinkData, releaseSessionIfNotReleased, req, res}), cb);
};

JIRAConnector.prototype.asyncGetPermalinkThumbnail = async function (permalinkData, logger) {
    const {permalinkID, platformKind, permalinkInfo} = permalinkData;
    const format = permalinkInfo?.image?.format ? permalinkInfo.image.format : "png";
    const dataResidency = permalinkInfo?.dataResidency ?? this.config.defaultDataResidencyName;
    try {
        return await this.permalinkImageStorageAdapter.getPermalinkImageBase64({permalinkID, platformKind, format, dataResidency});
    } catch (err) {
        // permalink image could be not ready yet
        await this.generatePermalinkImage({logger, permalinkData});
        return await this.permalinkImageStorageAdapter.getPermalinkImageBase64({permalinkID, platformKind, format, dataResidency});
    }
}

JIRAConnector.prototype.asyncGetPermalink = async function ({dbConnector, permalinkData, releaseSessionIfNotReleased, req, res}) {
    const {permalinkID, platformKind, permalinkInfo} = permalinkData;
    const format = permalinkInfo?.image?.format ? permalinkInfo.image.format : "png";
    const dataResidency = permalinkInfo?.dataResidency ?? this.config.defaultDataResidencyName;
    const _pipeStream = async () => {
        const headers = await this.permalinkImageStorageAdapter.getPermalinkHeadersWithMetadata({platformKind, permalinkID, format, dataResidency});
        const stream = await this.permalinkImageStorageAdapter.getPermalinkImageStream({platformKind, permalinkID, format, dataResidency});
        await releaseSessionIfNotReleased();
        return await pipeStreamToResponse(stream, res, headers);
    }

    try {
        return await _pipeStream();
    } catch (err) {
        try {
            await this.generatePermalinkImage({logger: req.bas.logger, permalinkData});
            return await _pipeStream();
        }
        catch (error) {
            // error action handled by the caller
            throw error;
        }
    }
    return {};
};

JIRAConnector.prototype.asyncGetPermalinkFromPlatform = async function ({dbConnector, permalinkData, releaseSessionIfNotReleased, req, res})
{
    req.bas.addLoggerInfo({fn: "asyncGetPermalinkFromPlatform", module: "jira"});
    const {
        platformInfo,
        resourceID,
        branchID,
    } = permalinkData;

    if (platformInfo && platformInfo.issueID && resourceID && branchID && platformInfo.jwt) {
        const attachmentResponse = await callWithLegacyCallback(cb => doGetAttachmentList(dbConnector, platformInfo.issueID, platformInfo.jwt, null, this.config, cb));
        const uniqueFileName = `balsamiq_wireframe_${resourceID}_${branchID}.png`;
        const attachments = attachmentResponse.attachment.filter(a => a.filename.endsWith(uniqueFileName));
        if (attachments.length <= 0) {
            const err = new Error(`permalink: no HD image found for resource ID ${resourceID} branch ID ${branchID}`);
            err.resultObj = {info: true};
            throw err;
        }

        const attachment = attachments[0];
        req.query.attachmentID = attachment.id;
        req.query.platformToken = platformInfo.jwt;
        req.bas.logger.info(`permalink: attachmentID ${attachment.id}`);

        // FIX: JWT is broken
        // let {urlNoToken, jwtToken} = await callWithLegacyCallback(cb => doGetAttachmentAuthURL(dbConnector, req.query.platformToken, req.query.attachmentID, cb));
        // let options = {
        //     url: urlNoToken,
        //     headers: {
        //         'Authorization': 'JWT ' + jwtToken
        //     }
        // }
        let {urlNoToken, accessToken} = await callWithLegacyCallback(cb => doGetAttachmentAuthURL(req.bas.logger, dbConnector, req.query.platformToken, req.query.attachmentID, cb));
        let headers = {
            "X-Atlassian-Token": "no-check",
            "Authorization": "Bearer " + accessToken,
            "accept": "application/json",
            "x-atlassian-force-account-id": "true"
        };
        let options = {
            url: urlNoToken,
            headers: headers,
            incomingResHandler: (incomingRes) => {
                // remove the content-disposition in order to not force the download of the image
                delete incomingRes.headers['content-disposition'];
                releaseSessionIfNotReleased();
            }
        }

        await releaseSessionIfNotReleased();

        await callWithLegacyCallback(cb => {
            pipeToNewRequest(req, res, options, req.bas.logger, cb);
        });

        return {};
    } else {
        // [PUBLIC_SHARE] TODO: in case of public share resourceID and branchID are not specified and we serve the default png
        throw new Error('permalink: missing parameter');
    }
};

JIRAConnector.prototype.makePermalinkID = function () {
    return shortUUID.generate();
};

JIRAConnector.prototype.getSnapshotUrl = function (baseDir, permalinkID) {
    //do nothing
    return '';
};

JIRAConnector.prototype.getPermalinkExtraFromPermalinkData = function (permalinkData) {
    const {permalinkInfo, permalinkID, permalinkKind} = permalinkData;
    const kindForPath = "jira";
    const format = permalinkInfo?.image?.format ? permalinkInfo.image.format : "png";
    const image = bmprUtilsMod.composeImagePermalinkUrl(this.config.shareUrls[this.config.defaultDataResidencyName], permalinkID, kindForPath, format);
    return {
        image: (permalinkKind === Consts.PermalinkKind.image || permalinkKind === Consts.PermalinkKind.image_unfurling) ? image : undefined,
        edit: permalinkInfo.edit ? permalinkInfo.edit : undefined,
        comment: permalinkInfo.comment ? permalinkInfo.comment : undefined,
        view: permalinkInfo.view ? permalinkInfo.view : undefined,
    };
};

/**
 * @param {JSDocTypes.BASRequest} req
 * @param {JSDocTypes.Response} res
 * @returns {void}
 */
JIRAConnector.prototype.endPoint = function (req, res) {
    // serve iframe endpoints and validate the JWT
    const endpoint = req.params.endpoint;
    const logger = req.bas.logger.getLogger({action: 'jira-endpoint', module: "jira", endpoint: endpoint});

    const repo = this.config.proxyConfig.find(cfg => cfg.prefix === '/bw-atlassian/');
    if (!repo) {
        logger.error("Proxy config not found for prefix /bw-atlassian/");
        res.status(500).json({error: "Proxy not found"});
        return;
    }

    /** @type URL */
    let url;
    try {
        const path = pathUtil.join(repo.path, "jira/", ************************(endpoint))
        url = new URL(path, repo.host);
    } catch (error) {
        logger.error("Error constructing URL", error, {repo, endpoint});
        res.status(400).json({error: 'Invalid URL' + error});
        return;
    }

    verifyAtlassianJWT(logger, req, this.sessionManager, function (obj) {
        if (isBASLegacyErrorObject(obj)) {
            logger.error(obj.error, null, {action: "JWT validation for Atlassian Jira endpoint"});
            res.status(401).json({code: 401, error: "Invalid or missing JWT token"});
            return;
        }
        function handleError(/** @type {Error} */ e) {
            logger.error("Unexpected error serving the request: " + e.message, e);
            res.status(500).json({code: 500, error: "Unable to serve the request"});
        }
        try {
            superagent
                .get(url.href)
                .on('response', (incomingResponse) => res.set(incomingResponse.headers))
                .pipe(res)
                .on('error', function(e) { handleError(e) });
        } catch (e) {
            handleError(e);
        }
    });
};

JIRAConnector.prototype.directAPICall = function(method, p_req, p_res)
{
    var instanceInfoObj = p_req.body;
    var kind = "jira";
    var issueId;
    var action;
    var extname;
    var jwtToken;
    var attachmentID;
    var issuerClaim;
    let logger = p_req.bas.logger.getLogger({action: "jira/" + p_req.params.api, module: "jira", method: method});
    let config = this.config;

    issuerClaim = getIssuerClaim(p_req);
    logger.info("direct api call [" + p_req.params.api + "] " + issuerClaim);

    this.sessionManager.createSession(logger, p_req.params.api, function(obj) {
        var sessionData, dbConnector;

        let finalizeError = function (errorMessage, errorForLogs, errorCode) {
            let errorMessageForLog = errorMessage + ": " + errorForLogs;
            logger.error(errorMessageForLog);
            let sessionManager = sessionData.sessionManager;
            sessionManager && sessionManager.releaseSession(sessionData);

            p_res.status(errorCode || 404);
            p_res.setHeader('Content-Type', 'text/json');
            p_res.setHeader('Content-Length', Buffer.byteLength(errorMessage));
            p_res.write(errorMessage);
            p_res.end();
        }

        if (obj.error) {
            returnData(JSON.stringify(obj), p_res, obj);
        } else {
            sessionData = obj;
            dbConnector = obj.dbConnector;
            if (method === "GET" && p_req.params.api === "fetch") {
                doPipe(logger, dbConnector, p_req, p_res, sessionData, config);
            } else if (method === "POST" && p_req.params.api === "issue-updated") {
                // TODO: remove once the plugin has been released
                returnData(JSON.stringify({}), p_res, sessionData);
            } else if (method === "POST" && p_req.params.api === "installed") {
                let finalizeInstallation = function (instanceInfoObj) {

                    var saveAndReturn = function (/*logger*/) {
                        dbConnector.saveConnectorData(kind, instanceInfoObj.clientKey, instanceInfoObj, function (obj) {
                            if (obj.error) {
                                returnData(JSON.stringify(obj), p_res, sessionData);
                            } else {
                                dbConnector.getConnectorData(kind, instanceInfoObj.clientKey, function (obj) {
                                    if (obj.error || !obj.clientKey) {
                                        var ret;
                                        ret = obj.error ? obj : {error: "Installation failed. Unable to save the instance data"};
                                        returnData(JSON.stringify(ret), p_res, sessionData);
                                    } else {
                                        // [GDPR] not necessary to share with third party processor
                                        // logger.info("successfully installed subscription for clientKey " + obj.clientKey);
                                        returnData(JSON.stringify(obj), p_res, sessionData);
                                    }
                                });
                            }
                        });
                    };

                    // check if the clientKey is already installed
                    dbConnector.getConnectorData(kind, instanceInfoObj.clientKey, function (obj) {
                        if (obj.error) {
                            returnData(JSON.stringify(obj), p_res, sessionData);
                        } else {
                            // if we are in the new lifecycle, the check on the token has already passed
                            // if we are in the old lifecycle we test only for the upgrade i.e. the app is already installed and it is signed with a jwt token
                            if (obj.clientKey && newInstallationCycle === false) {
                                // already installed
                                logger.info("old lifecycle, already installed " + obj.clientKey);
                                jwtToken = p_req.query.jwt || (p_req.headers.authorization && p_req.headers.authorization.split(" ")[1]);
                                try {
                                    // verify the signature
                                    jwt.decode(jwtToken, obj.sharedSecret, false);
                                    // request has been signed correctly, we save the new shared secret
                                    saveAndReturn(logger);
                                } catch (e) {
                                    // set the code to unauthorized
                                    p_res.status(401);
                                    returnData(JSON.stringify({error: "Signature failed:" + e.message}), p_res, sessionData);
                                }
                            } else {
                                saveAndReturn(logger);
                            }
                        }
                    });
                }

                let jwtToken, newInstallationCycle;
                try {
                    // Get Authorization header from request: `Authorization: JWT ${jwt_token}`
                    jwtToken = p_req.query.jwt || p_req.headers.authorization.split(" ")[1];
                    newInstallationCycle = isAtlassianNewLifeCycleActive(jwtToken);
                } catch (e) {
                    // no jwtToken associated
                    newInstallationCycle = false;
                }

                logger.updateParams({newInstallationCycle});
                logger.info("installing request " + JSON.stringify(instanceInfoObj));

                if (newInstallationCycle) {
                    doVerifyCallFromAtlassian(jwtToken, instanceInfoObj, config, function (obj) {
                        if (obj.error) {
                            finalizeError("not authorized", obj.error);
                        } else {
                            finalizeInstallation(instanceInfoObj);
                        }
                    })
                } else {
                    finalizeInstallation(instanceInfoObj);
                }
            } else if (method === "POST" && p_req.params.api === "uninstalled") {
                // Get Authorization header from request: `Authorization: JWT ${jwt_token}`
                let jwtToken, newInstallationCycle;
                try {
                    // Get Authorization header from request: `Authorization: JWT ${jwt_token}`
                    jwtToken = p_req.query.jwt || p_req.headers.authorization.split(" ")[1];
                    newInstallationCycle = isAtlassianNewLifeCycleActive(jwtToken);
                } catch (e) {
                    // no jwtToken associated
                    newInstallationCycle = false;
                }

                logger.updateParams({newInstallationCycle});
                logger.info("uninstalling request " + JSON.stringify(instanceInfoObj));

                if (newInstallationCycle) {
                    doVerifyCallFromAtlassian(jwtToken, instanceInfoObj, config, function (obj) {
                        if (obj.error) {
                            finalizeError("not authorized", obj.error);
                        } else {
                            dbConnector.deleteConnectorData(kind, instanceInfoObj.clientKey, function (obj) {
                                if (obj.error) {
                                    returnData(JSON.stringify(obj), p_res, sessionData);
                                } else {
                                    // [GDPR] not necessary to share with third party processor
                                    // logger.info("successfully uninstalled subscription for clientKey " + instanceInfoObj.clientKey);
                                    returnData(JSON.stringify({success: "ok"}), p_res, sessionData);
                                }
                            });
                        }
                    });
                } else {
                    dbConnector.deleteConnectorData(kind, instanceInfoObj.clientKey, function (obj) {
                        if (obj.error) {
                            returnData(JSON.stringify(obj), p_res, sessionData);
                        } else {
                            // [GDPR] not necessary to share with third party processor
                            // logger.info("successfully uninstalled subscription for clientKey " + instanceInfoObj.clientKey);
                            returnData(JSON.stringify({success: "ok"}), p_res, sessionData);
                        }
                    });
                }
            } else if (method === "POST" && p_req.params.api === "enabled") {
                // [GDPR] not necessary to share with third party processor
                // logger.info("successfully enabled for clientKey " + instanceInfoObj.clientKey);
                returnData(JSON.stringify({}), p_res, sessionData);
            } else if (method === "POST" && p_req.params.api === "disabled") {
                // [GDPR] not necessary to share with third party processor
                // logger.info("successfully disabled for clientKey " + instanceInfoObj.clientKey);
                returnData(JSON.stringify({}), p_res, sessionData);
            } else if (method === "GET" && p_req.params.api === "list") {
                issueId = p_req.query.issueID;
                jwtToken = p_req.query.jwt;
                doGetListExt(dbConnector, issueId, jwtToken, [".bmpr"], config, function(obj) {
                    returnData(JSON.stringify(obj), p_res, sessionData);
                });
            } else if (method === "GET" && p_req.params.api === "migrate") {
                issueId = p_req.query.issueID;
                jwtToken = p_req.query.jwt;
                doMigrationSteps(logger, dbConnector, issueId, jwtToken, config, function(obj) {
                    returnData(JSON.stringify(obj), p_res, sessionData);
                });
            } else if (method === "GET" && p_req.params.api === "license") {
                jwtToken = p_req.query.jwt || p_req.headers.authorization.split(" ")[1];
                doGetLicense(dbConnector, jwtToken, config, function(obj) {
                    returnData(JSON.stringify(obj), p_res, sessionData);
                });
            } else if (method === "GET" && p_req.params.api === "deleteAttachment") {
                jwtToken = p_req.query.jwt || p_req.headers.authorization.split(" ")[1];
                attachmentID = p_req.query.attachmentID;
                doRemoveExt(logger, dbConnector, jwtToken, attachmentID, config, function(obj) {
                    returnData(JSON.stringify(obj), p_res, sessionData);
                });
            } else if (method === "GET" && p_req.params.api === "condition") {
                var resp;
                var shouldDisplay = false;

                action = p_req.query.action;
                issueId = p_req.query.issueid;
                jwtToken = p_req.query.jwt || p_req.headers.authorization.split(" ")[1];

                logger.info("condition " + action + " " + issuerClaim);

                if (action == "edit")
                {
                    doGetListExt(dbConnector, issueId, jwtToken, [".bmpr", ".bmml"], config, function(obj) {
                        if (obj.error) {
                            // we do not log in order to not create to much noise in the log
                            // logger.info("unable to get resource list " + JSON.stringify(obj));
                            returnData(JSON.stringify({shouldDisplay: false}), p_res, sessionData);
                        } else {
                            if (obj.data[".bmml"] && obj.data[".bmml"].length || obj.totalLength > 1)
                            {
                                // TODO: what to do in case of duplicated BMPR name
                                // if BMMLs are still attached or we have multiple BMPR, we do not show the menu command
                                returnData(JSON.stringify({shouldDisplay: false}), p_res, sessionData);
                            } else
                            {
                                doGetLicense(dbConnector, jwtToken, config, function(obj) {
                                    // { "valid": true, "evaluation": true, "nearlyExpired": false, ... }
                                    if (obj.error)
                                    {
                                        // the plugin has no trial or subscription valid
                                        // logger.error("unable to get license " + JSON.stringify(obj));
                                        returnData(JSON.stringify({shouldDisplay: false}), p_res, sessionData);
                                    } else
                                    {
                                        if (obj.evaluation)
                                        {
                                            // trial
                                            if (obj.valid)
                                            {
                                                // valid
                                                // edit allowed
                                                // show bar in the editor
                                                // show bar in the home panel
                                                shouldDisplay = true
                                            } else
                                            {
                                                // expired
                                                // valid
                                                // edit NOT allowed
                                            }
                                        } else
                                        {
                                            // subscription
                                            if (obj.valid)
                                            {
                                                // valid
                                                shouldDisplay = true
                                            } else
                                            {
                                                // expired
                                            }
                                        }

                                        resp = {shouldDisplay: shouldDisplay, evaluation: obj.evaluation, valid: obj.valid};
                                        returnData(JSON.stringify(resp), p_res, sessionData);
                                    }
                                }.bind(this));
                            }
                        }
                    }.bind(this));
                } else {
                    if (action == "migrate") {
                        // in case there are bmml attached we should display the migration panel
                        extname = [".bmml"];
                    } else
                    {
                        // as a fallback, we show the panel in case of bmpr attached
                        extname = [".bmpr"];
                    }
                    doGetListExt(dbConnector, issueId, jwtToken, extname, config, function(obj) {
                        if (obj.error)
                        {
                            // we do not log in order to not create to much noise in the log
                            // logger.info("unable to get resource list " + JSON.stringify(obj));
                        } else
                        {
                            shouldDisplay = obj.totalLength > 0;
                        }
                        logger.info("condition should display " + action + " [" + shouldDisplay + "] " + issuerClaim + " " + JSON.stringify(obj));
                        resp = {shouldDisplay: shouldDisplay};
                        returnData(JSON.stringify(resp), p_res, sessionData);
                    }.bind(this));
                }
            } else {
                logger.info("condition ERROR Unexpected action" + action);
                returnData(JSON.stringify({shouldDisplay: false}), p_res, sessionData);
            }
        }
    }.bind(this));
};

let doVerifyCallFromAtlassian = function (jwtToken, instanceInfoObj, config, callback) {
    try {
        let expectedAudience = config.baseUrl;
        let expectedIssuer = instanceInfoObj.clientKey;

        verifyCallFromAtlassian(jwtToken, expectedAudience, expectedIssuer, callback);
    } catch (e) {
        callback({error: "unexpected exception: " + e});
    }
}

// var doGetAppUserInfo = function(instanceObj, accessToken, callback) {
//     var userId, appUserInfo, contentData, logObj = {action: "doGetAppUserInfo"};
//     var ret = {users: []};
//     var url = instanceObj.baseUrl + '/rest/api/3/groupuserpicker?query=Balsamiq';
//     var key = instanceObj.key || config.jiraNamespace;
//     var options = {
//         method: 'GET',
//         url: url,
//         auth: {bearer: accessToken},
//         headers: {
//             'Accept': 'application/json',
//             "x-atlassian-force-account-id": "true"
//         }
//     };
//     request(options, function (error, response, body) {
//         var users, user, i;
//         if (error) {
//             log.error(logObj, error);
//             callback && callback(ret);
//         } else {
//             users = JSON.parse(body);
//             contentData = {};
//
//             for (i = 0; i < users.users.users.length; i++) {
//                 user = users.users.users[i];
//                 userId = user.accountId;
//
//                 if (user.displayName.startsWith("Balsamiq")) {
//                     contentData[userId] = {status: "onprogress"};
//                     getUserDetails(instanceObj.baseUrl, userId, accessToken, contentData, function (obj) {
//                         ret.users.push(obj);
//                     });
//                 }
//             }
//
//             watchdog(contentData, function () {
//                 for (i=0; i<ret.users.length; i++) {
//                     if (ret.users[i].email === key + "@connect.atlassian.com") {
//                         appUserInfo = ret.users[i];
//                         break;
//                     }
//                 }
//
//                 if (appUserInfo) {
//                     callback && callback(appUserInfo);
//                 } else {
//                     callback && callback({error: "not found"});
//                 }
//             });
//         }
//     });
// };

var isUserAlreadyIn = function(usersList, userToCheck) {
    return usersList.users.filter(function(user) {return (user.id === userToCheck.id || user.id === userToCheck.userId)}).length;
};

function getIssuerClaim(req) {
    var jwtToken;
    var claims, issuer;

    try {
        jwtToken = req.query.jwt || (req.headers && req.headers.authorization && req.headers.authorization.split(" ")[1]);
        if (jwtToken) {
            // TODO verify if JWT signature is not needed (used only for log)
            claims = jwt.decode(jwtToken, '', true);
            issuer = claims && claims.iss || "";
        } else {
            issuer = "";
        }
    } catch (e) {
        issuer = ""
    }

    return issuer;
}

// TODO rename returnJson?
function returnData(data, res, sessionData)
{
    var sessionManager = sessionData.sessionManager;
    // TODO: do we ignore the releaseSession error?
    sessionManager && sessionManager.releaseSession(sessionData);

    res.setHeader('Content-Type', 'text/json');
    res.setHeader('Content-Length', Buffer.byteLength(data));
    res.write(data);
    res.end();
}

function createJwtPayloadExt(req, config, instanceObj, userKey, userAccountId) {
    var now = moment().utc();
    //TODO addon.config.jwt().validityInMinutes;
    var jwtTokenValidityInMinutes = 5;
    var clientKey = instanceObj.clientKey;
    var iss = instanceObj.key || config.jiraNamespace;

    var token =  {
        "iss": iss,
        "iat": now.unix(),
        "exp": now.add(jwtTokenValidityInMinutes, 'minutes').unix(),
        "qsh": jwt.createQueryStringHash(req),
        "aud": [ clientKey ]
    };

    if (userKey) {
        token["sub"] = userKey;
    } else if (userAccountId) {
        token["sub"] = userAccountId;
    }
    return token;
}

// function createJwtPayload(req, clientKey, userKey, userAccountId) {
//     var now = moment().utc();
//     //TODO addon.config.jwt().validityInMinutes;
//     var jwtTokenValidityInMinutes = 5;
//
//     var token =  {
//         "iss": config.jiraNamespace,
//         "iat": now.unix(),
//         "exp": now.add(jwtTokenValidityInMinutes, 'minutes').unix(),
//         "qsh": jwt.createQueryStringHash(req),
//         "aud": [ clientKey ]
//     };
//
//     if (userKey) {
//         token["sub"] = userKey;
//     } else if (userAccountId) {
//         token["sub"] = userAccountId;
//     }
//     return token;
// }


function uploadBinaryExt(p_url, headers, name, buffer, callback)
{
    var body = "";
    var parsed = UrlModule.parse(p_url);
    var form = new FormData();

    form.append('notifyUsers', 'false');
    form.append('comment', 'auto-generated by Balsamiq Wireframes. DO NOT REMOVE');
    form.append('file', buffer, { ContentType: 'application/octet-stream', filename: name} );
    form.submit({
        protocol: parsed.protocol,
        host: parsed.hostname,
        port: parsed.port,
        path: parsed.path,
        headers: headers
    }, function(err, res) {
        if (err) {
            callback({error: "Failed to upload the resource: " + err, url: p_url});
        } else {
            if (res.statusCode == 200)
            {
                res.setEncoding('utf8');
                res.on('data', function (chunk) {
                    body += chunk;
                });
                res.on('end', function () {
                    try {
                        var obj = JSON.parse(body);
                        // jira returns obj[0]
                        var id = obj.id || obj[0] && obj[0].id || obj.results && obj.results[0].id;
                        if (id) {
                            callback({id: id});
                        } else {
                            callback({error: "Bad json response", url: p_url});
                        }
                    } catch (er) {
                        callback({error: "Bad json", url: p_url});
                    }
                });
            } else {
                callback({error: "Unable to upload the resource: " + res.statusCode, url: p_url, code: res.statusCode});
            }
        }
    });
}

// TODO: unify Atlassian Utils
// function uploadBinary(p_url, jwt, name, buffer, callback)
// {
//     var body = "";
//     var parsed = UrlModule.parse(p_url);
//     var form = new FormData();
//     form.append('notifyUsers', 'false');
//     form.append('comment', 'auto-generated by Balsamiq Wireframes. DO NOT REMOVE');
//     form.append('file', buffer, { ContentType: 'application/octet-stream', filename: name} );
//     form.submit({
//         protocol: parsed.protocol,
//         host: parsed.hostname,
//         port: parsed.port,
//         path: parsed.path,
//         headers: {'Authorization': 'JWT '+jwt, 'X-Atlassian-Token': 'no-check'}
//     }, function(err, res) {
//         if (err) {
//             callback({error: "Failed to upload the resource: " + err});
//         } else {
//             if (res.statusCode == 200)
//             {
//                 res.setEncoding('utf8');
//                 res.on('data', function (chunk) {
//                     body += chunk;
//                 });
//                 res.on('end', function () {
//                     try {
//                         var obj = JSON.parse(body);
//                         // jira returns obj[0]
//                         var id = obj.id || obj[0] && obj[0].id || obj.results && obj.results[0].id;
//                         if (id) {
//                             callback({id: id});
//                         } else {
//                             callback({error: "Bad json response"});
//                         }
//                     } catch (er) {
//                         callback({error: "Bad json"});
//                     }
//                 });
//             } else {
//                 callback({error: "Unable to upload the resource: " + res.statusCode, url: p_url, code: res.statusCode});
//             }
//         }
//     });
// }

function doGetAuthURLExt(dbConnector, authToken, method, path, queryObject, config, callback)
{
    var url, urlNoToken;
    var claims;
    var queryString;

    if (authToken) {
        // JWT signature verified right after
        try {
            claims = jwt.decode(authToken, '', true);
        } catch (err) {
            callback && callback({error: "JWT first decode failed"});
            return;
        }

        queryString = jwt.canonicalizeQueryString(queryObject);
        dbConnector.getConnectorData("jira", claims.iss, function(instanceObj) {
            if (instanceObj.error || !instanceObj.sharedSecret) {
                var ret = instanceObj.error ? instanceObj : {error: "The instance seems to non be authenticated. Please try to reinstall the plugin"};
                callback && callback(ret);
            } else {
                var sharedSecret = instanceObj.sharedSecret;
                var userKey, userAccountId;

                try {
                    // verify the signature
                    jwt.decode(authToken, sharedSecret, false);

                    if (claims.sub) {
                        // GDPR: userKey deprecated, use urn:atlassian:connect:useraccountid instead
                        userAccountId = claims.sub;
                    } else if (claims.context && claims.context.user && claims.context.user.userKey) {
                        userKey = claims.context.user.userKey;
                    }

                    // TODO: test this for GDPR
                    var jwtPayload = createJwtPayloadExt({
                        'method': method,
                        'path'  : path,
                        'query' : queryObject
                    }, config, instanceObj, userKey, userAccountId);

                    var jwtToken = jwt.encode(jwtPayload, sharedSecret, 'HS256');

                    urlNoToken =  instanceObj.baseUrl + path + (queryString ? '?' + queryString: '');
                    // [deprecated JWT on URL parameter] TODO checked all caller
                    url = instanceObj.baseUrl + path + (queryString ? "?" + queryString + "&": '?') + "jwt=" + jwtToken;

                    callback && callback({url: url, urlNoToken: urlNoToken, jwtToken: jwtToken});
                } catch (e) {
                    callback && callback({error: "Signature failed:" + e.message});
                }
            }
        });
    } else {
        callback && callback({error: "Wrong parameter: platformToken is missing" });
    }
}

function doGetAuthURLOAuth(logger, dbConnector, authToken, scope, path, queryObject, callback)
{
    let urlNoToken;
    let queryString;

    if (authToken) {
        getAccessTokenFromJWT(logger, dbConnector, authToken, scope, function (obj) {
            let instanceObj;
            if (obj.error) {
                callback(obj)
            } else {
                instanceObj = obj.instanceObj;
                if (instanceObj.error) {
                    callback({error: "Cannot get connector data: " + instanceObj.error});
                } else {
                    urlNoToken = instanceObj.baseUrl + path + (queryString ? '?' + queryString : '');
                    callback({urlNoToken: urlNoToken, accessToken: obj.accessToken});
                }
            }
        });
    } else {
        callback && callback({error: "Wrong parameter: platformToken is missing" });
    }
}

function doPipe(logger, dbConnector, req, res, sessionData, config, callback) {
    var platformToken = req.query.platformToken;
    var attachmentID = req.query.attachmentID;
    var path = req.query.path;
    logger = logger.getLogger({action: "fetch", module: "jira"});

    var sendStatusError = function(code, message, logger) {
        logger.error("ERROR: " + message);
        try {
            res.status(code).send(message);
        } catch (error) {
            // probably "cannot set headers after they are sent to the client"
            logger.error("Unexpected error when sending result status code", error);
        }
    };

    // var finalise = function(obj) {
    //     sessionData.sessionManager.releaseSession(sessionData, function(sessionObj) {
    //         if (sessionObj.error) {
    //             // we log error but we try to complete the call
    //             logger.error("sessionManager.releaseSession failed: " + sessionObj.error);
    //         }
    //
    //         if (obj.error) {
    //             sendStatusError(obj.code, obj.error, logger);
    //         } else {
    //             var options;
    //             if (obj.urlNoToken && obj.jwtToken) {
    //                 options = {
    //                     url: obj.urlNoToken,
    //                     headers: {
    //                         'Authorization': 'JWT ' + obj.jwtToken
    //                     }
    //                 };
    //             } else {
    //                 options = {
    //                     url: obj.url
    //                 }
    //             }
    //
    //             logger.info("fetch happening " + options.url);
    //             pipeToNewRequest(req, res, options, logger, (obj) => {
    //                 if (obj.error) {
    //                     var message = obj.error.message ? obj.error.message:  "Unexpected error";
    //                     sendStatusError(404, message, logger);
    //                 }
    //                 callback && callback(obj);
    //             });
    //         }
    //     });
    // };
    let finaliseOAuthToken = function(obj) {
        sessionData.sessionManager.releaseSession(sessionData, function(sessionObj) {
            if (sessionObj.error) {
                // we log error but we try to complete the call
                logger.error("sessionManager.releaseSession failed: " + sessionObj.error);
            }

            if (obj.error) {
                sendStatusError(obj.code, obj.error, logger);
            } else {
                let options;
                let headers = {
                    "X-Atlassian-Token": "no-check",
                    "Authorization": "Bearer " + obj.accessToken,
                    "accept": "application/json",
                    "x-atlassian-force-account-id": "true"
                };

                options = {
                    url: obj.urlNoToken,
                    headers: headers
                };

                logger.info("fetch with OAuth token happening " + options.url);
                pipeToNewRequest(req, res, options, logger, (obj) => {
                    if (obj.error) {
                        let message = obj.error.message ? obj.error.message:  "Unexpected error piping";
                        sendStatusError(404, message, logger);
                    }
                    callback && callback(obj);
                });
            }
        });
    };


    if (attachmentID) {
        doGetAttachmentAuthURL(logger, dbConnector, platformToken, attachmentID, function (obj) {
            if (obj.error) {
                logger.error(obj.error + " " + attachmentID);
                finaliseOAuthToken({error: "Unable to get the authorised URI", code: 404});
            } else {
                // JWT Authentication is not functioning
                // finalise({urlNoToken: obj.urlNoToken, jwtToken: obj.jwtToken});
                finaliseOAuthToken({urlNoToken: obj.urlNoToken, accessToken: obj.accessToken});
            }
        });
    } else if (path) {
        // path = "/jira/secure/useravatar?size=small&ownerId=test&avatarId=10500";
        var str = path.split('?'), queryObject, pathString;
        pathString = str[0];
        if (str.length === 2) {
            queryObject = queryStringToJSON('?' + str[1]);
        }
        doGetAuthURLExt(dbConnector, platformToken, 'GET', pathString, queryObject, config, function(obj) {
            if (obj.error) {
                logger.error(obj.error + " " + path);
                finaliseOAuthToken({error: "Unable to get the authorised URI", code: 404});
            } else {
                // JWT Authentication is not functioning
                // finalise({urlNoToken: obj.urlNoToken, jwtToken: obj.jwtToken});
                finaliseOAuthToken({urlNoToken: obj.urlNoToken, accessToken: obj.accessToken});
            }
        });
    } else {
        logger.updateParams({query: req.query});
        logger.error("Unexpected parameters");
        finaliseOAuthToken({error: "Wrong parameters", code: 500});
    }
}

function doFetchOAuth(url, token, isBinary, callback) {
    let headers = {
        "X-Atlassian-Token": "no-check",
        "Authorization": "Bearer " + token,
        "accept": "application/json",
        "x-atlassian-force-account-id": "true"
    };

    try {
        httpreq.get(url, {
            binary: isBinary,
            headers: headers,
        }, function (err, res) {
            if (err) {
                callback({error: "Failed to retrieve the resource: " + err});
            } else {
                if (res.statusCode === 200) {
                    callback(res.body);
                } else {
                    callback({error: "Unable to retrieve the resource: " + url + " " + res.statusCode});
                }
            }
        });
    } catch (e) {
        callback({error:{message: "Failed to retrieve the resource: " + url + " " + e.message}});
    }
}

// function doFetch(url, jwtToken, isBinary, callback) {
//     try {
//         httpreq.get(url, {
//             binary: isBinary,
//             headers: {
//                 'Authorization': 'JWT ' + jwtToken
//             }
//         }, function (err, res) {
//             if (err) {
//                 callback({error: "Failed to retrieve the resource: " + err});
//             } else {
//                 if (res.statusCode === 200) {
//                     callback(res.body);
//                 } else {
//                     callback({error: "Unable to retrieve the resource: " + url + " " + res.statusCode});
//                 }
//             }
//         });
//     } catch (e) {
//         callback({error:{message: "Failed to retrieve the resource: " + url + " " + e.message}});
//     }
// }

function doJiraAttachmentAuthFetch(logger, dbConnector, authToken, attachmentID, isBinary, callback) {
    doGetAttachmentAuthURL(logger, dbConnector, authToken, attachmentID, function (obj) {
        // FIX: JWT is broken
        // doFetch(url, jwtToken, isBinary, function(obj) {
        doFetchOAuth(obj.urlNoToken, obj.accessToken, isBinary, function (obj) {
            const archiveID = generateArchiveID();
            if (obj.error) {
                callback && callback(obj);
            } else {
                callback && callback({id: archiveID, buffer: obj});
            }
        });
    });
}

function doGetAttachmentAuthURL(logger, dbConnector, authToken, attachmentID, callback)
{
    const path = '/rest/api/latest/attachment/content/' + attachmentID + '/';

    // doGetAuthURLExt(dbConnector, authToken, 'GET', path, null, callback);
    doGetAuthURLOAuth(logger, dbConnector, authToken, "READ ACT_AS_USER", path, null, callback);
}

function generateArchiveID()
{
    return "jira_"+uuid.v1().replace(/-/g, '_');
}

//function getPlatformSiteID(issueID, token) {
//    var claims = jwt.decode(token, '', true);
//    var iss = claims.iss;
//    return iss + "_" + issueID;
//}

function getIssueIDfromPlatformSiteID(platformSiteID, iss) {
    return removePrefix(platformSiteID, iss + "_");
}

function watchdog(contentData, callback)
{
    var i, key,
        keys = Object.keys(contentData),
        completed = 0, error = null;
    for (i=0; i<keys.length; i++)
    {
        key = keys[i];
        if (contentData[key].error) {
            error = contentData[key].error;
            break;
        } else if (contentData[key].status == "onprogress") {
            break;
        } else {
            // success
            completed++;
        }
    }

    if (completed === keys.length)
    {
        callback({status: "completed"});
    } else if (error) {
        callback({error: error});
    } else {
        // retry later
        setTimeout(function() {
            watchdog(contentData, callback);
        }, 200);
    }
}

function escapeRegExp(string) {
    return string.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, "\\$1");
}

function replaceAll(string, find, replace) {
    return string.replace(new RegExp(escapeRegExp(find), 'gi'), replace);
}

function doMigrationSteps(logger, dbConnector, issueID, authToken, config, callback)
{
    // JWT signature verified in the doGetAttachmentList
    var claims = jwt.decode(authToken, '', true);
    var iss = claims.iss;
    var jiraServerURL;
    var convertedZIPAttachmentID;
    logger = logger.getLogger({action: "migrationSteps", module: "jira"});
    // get the list of attachments
    // for each attachment

    dbConnector.getConnectorData("jira", iss, function(instanceObj) {
        if (instanceObj.error) {
            callback(obj);
        } else {
            jiraServerURL = instanceObj.baseUrl; // "https://balsamiqjira.atlassian.net";

            doGetAttachmentList(dbConnector, issueID, authToken, null, config, function(obj) {
                var i,
                    /** @type JSZip */
                    backupAssets,
                    include, history, issueKey,
                    backupZip = new JSZip(),
                    convertedZip = new JSZip(),
                    /** @type JSZip */
                    convertedAssets,
                    contentData = {},
                    imagesMetadata = {},
                    mockupsMetadata = {},
                    mockupsHistory = {};


                if (obj.error)
                {
                    callback(obj);
                } else
                {
                    backupAssets = backupZip.folder('assets');
                    convertedAssets = convertedZip.folder('assets');
                    issueKey = obj.key;
                    for (i=0; i<obj.attachment.length; i++) {
                        (function(attachment) {

                            if (pathUtil.extname(attachment.filename) === ".bmml") {
                                contentData[attachment.id] = {status: "onprogress"};
                                if (mockupsHistory[attachment.filename] && mockupsHistory[attachment.filename].length > 0)
                                {
                                    var h, gotcha = false, attachmentDate = new Date(attachment.created),
                                        history = mockupsHistory[attachment.filename];

                                    for (h = 0; h<history.length; h++)
                                    {
                                        if (attachmentDate > new Date(history[h].created))
                                        {
                                            history[h].splice(h, 0, attachment);
                                            gotcha = true;
                                            break;
                                        }
                                    }

                                    if (gotcha == false)
                                    {
                                        history.push(attachment);
                                    }
                                } else
                                {
                                    mockupsHistory[attachment.filename] = [attachment];
                                }

                                include = true;
                            } else if (isImage(attachment.mimeType))
                            {
                                contentData[attachment.id] = {status: "onprogress"};
                                include = true;
                            } else {
                                include = null;
                            }

                            if (include) {
                                var isBinary = isImage(attachment.mimeType);
                                doJiraAttachmentAuthFetch(logger, dbConnector, authToken, attachment.id, isBinary, function(obj) {
                                    if (obj.error) {
                                        contentData[attachment.id] = {status: "error", error: obj.error};
                                    } else {
                                        contentData[attachment.id] = {status: "success", data: obj.buffer, metadata: attachment};
                                    }
                                    if (isBinary)
                                    {
                                        // image
                                        imagesMetadata[attachment.id] = attachment;
                                    } else
                                    {
                                        // mockup
                                        mockupsMetadata[attachment.id] = attachment;
                                        contentData[attachment.id].converted = obj.buffer;
                                    }
                                });
                            }
                        })(obj.attachment[i]);
                    }

                    watchdog(contentData, function(obj) {
                        var buffer, imageKeys, mockupKeys, i, l, h, strToFind, strToReplace, image, mockup, strConverted, filename;
                        if (obj.error)
                        {
                            callback(obj);
                        } else
                        {
                            imageKeys = Object.keys(imagesMetadata);
                            mockupKeys = Object.keys(mockupsMetadata);

                            // for each images
                            for (i=0; i<imageKeys.length; i++)
                            {
                                image = imagesMetadata[imageKeys[i]];
                                for (l=0; l<mockupKeys.length; l++)
                                {
                                    mockup = mockupsMetadata[mockupKeys[l]];

                                    strToReplace = "<src>" +"./assets/" + escape(encodeURIComponent(image.filename)) + "</src>";

                                    //logger.info("BMML converted: " + contentData[mockup.id].converted);
                                    strToFind = "<src>" + escape(jiraServerURL + "/secure/attachment/" + image.id + "/" + image.id + "_" + encodeURIComponent(image.filename)) + "</src>";
                                    strConverted = replaceAll(contentData[mockup.id].converted, strToFind, strToReplace);
                                    //logger.info("migrate string to find: " + strToFind);

                                    if (strConverted == contentData[mockup.id].converted) {
                                        // WORKAROUND 2.2 BUG: sometime the image.filename is not encoded
                                        strToFind = "<src>" + escape(jiraServerURL + "/secure/attachment/" + image.id + "/" + image.id + "_" + image.filename) + "</src>";
                                        strConverted = replaceAll(contentData[mockup.id].converted, strToFind, strToReplace);
                                        //logger.info("migrate string to find (workaround): " + strToFind);
                                    }

                                    if (strConverted != contentData[mockup.id].converted)
                                    {
                                        // the image was mentioned
                                        contentData[image.id].linked = true;
                                        contentData[mockup.id].converted = strConverted;
                                    }
                                }
                            }

                            // for each images
                            for (i=0; i<imageKeys.length; i++)
                            {
                                image = imagesMetadata[imageKeys[i]];
                                filename = pathUtil.basename(image.filename, pathUtil.extname(image.filename)) + ".bmml";
                                if (mockupsHistory[filename])
                                {
                                    // the image is a PNG export of a mockup
                                    contentData[image.id].exportedPNG = true;
                                }
                            }

                            mockupKeys = Object.keys(mockupsHistory);
                            for (l=0; l<mockupKeys.length; l++)
                            {
                                history = mockupsHistory[mockupKeys[l]];
                                for (h=0; h<history.length; h++)
                                {
                                    mockup = history[h];
                                    if (h == 0)
                                    {
                                        filename = mockup.filename;
                                        convertedZip.file(filename, contentData[mockup.id].converted);
                                    } else
                                    {
                                        filename = mockup.filename + "." + h;
                                    }
                                    backupZip.file(filename, contentData[mockup.id].converted);
                                }
                            }

                            for (i=0; i<imageKeys.length; i++)
                            {
                                image = imagesMetadata[imageKeys[i]];
                                if (contentData[image.id].linked)
                                {
                                    backupAssets.file(image.filename, contentData[image.id].data);
                                    convertedAssets.file(image.filename, contentData[image.id].data);
                                }
                            }

                            backupZip.generateInternalStream({type:"nodebuffer"}).accumulate().then(function (backupBuffer) {
                                doSaveToPlatform(logger, dbConnector, authToken, issueID, issueKey + "-Backup.zip", backupBuffer, function(obj) {
                                    if (obj.error)
                                    {
                                        callback(obj);
                                    } else
                                    {
                                        convertedZip.generateInternalStream({type:"nodebuffer"}).accumulate().then(function (buffer) {
                                            doSaveToPlatform(logger, dbConnector, authToken, issueID, issueKey + "-Converted.zip", buffer, function(obj) {
                                                var attachment, keys, toDelete;
                                                if (obj.error)
                                                {
                                                    callback(obj);
                                                } else
                                                {
                                                    convertedZIPAttachmentID = obj.id;
                                                    keys = Object.keys(contentData);
                                                    for (l=0; l<keys.length; l++) {

                                                        attachment = contentData[keys[l]].metadata;
                                                        toDelete = false;
                                                        if (isImage(attachment.mimeType))
                                                        {
                                                            if (contentData[keys[l]].exportedPNG || contentData[keys[l]].linked)
                                                            {
                                                                toDelete = true;
                                                            }
                                                        } else
                                                        {
                                                            // bmml file
                                                            toDelete = true;
                                                        }

                                                        if (toDelete)
                                                        {
                                                            contentData[keys[l]] = {status: "onprogress"};
                                                            (function(attachment) {
                                                                doRemove(dbConnector, iss, attachment.id, config, function(obj) {
                                                                    if (obj.error) {
                                                                        logger.error("unable to delete the file: " + JSON.stringify(obj.result));
                                                                        contentData[attachment.id] = {status: "error", error: obj.error};
                                                                    } else {
                                                                        logger.info("successfully delete the file: " + JSON.stringify(obj.result));
                                                                        contentData[attachment.id] = {status: "success"};
                                                                    }
                                                                });
                                                            })(attachment);
                                                        }
                                                    }

                                                    watchdog(contentData, function(obj) {
                                                        if (obj.error)
                                                        {
                                                            callback(obj);
                                                        } else {
                                                            // [JWT deprecation: the url is passed to the client side]
                                                            doGetAttachmentAuthURL(logger, dbConnector, authToken, convertedZIPAttachmentID, function(obj) {
                                                                if (obj.error) {
                                                                    callback(obj);
                                                                } else {
                                                                    callback({url: obj.urlNoToken, convertedZIPAttachmentID: convertedZIPAttachmentID});
                                                                }
                                                            });
                                                        }
                                                    });
                                                }
                                            });
                                        });
                                    }
                                });
                            });
                        }
                    })
                }
            });

            //          if path.extension(attachment.filename) == "bmml"
            //              download attachment
            //              mockups[attachment.filename] = i
            //          else if isImage(attachment.filename))
            //              download attachment
            //              if image is thumbnail
            //                  thumbnail[attachment.filename] = i
            //              else
            //                  images[attachment.filename] = i
            //      for each mockups
            //          for each images
            //              if (replaceAll(mockup, image) != -1)
            //                  image.used = true
            //      for each mockups
            //          zip.add(mockup)
            //      assetFolder = zip.create("assets")
            //      // are thumbnail created during import?
            //      thumbnailFolder = zip.create("thumbnail")
            //      for each images
            //          if images.used
            //              assetFolder.add(image)
            //      for each thumbnails
            //          thumbnailFolder.add(thumbnail)
            //      zip.compress
            //      attach zip to issue
            //      delete mockups, bmml, png
            //      launch editor giving the handle
            // else
            //      callback


//    var index = 0;
//    var src = "<src>https%3A//balsamiqjira.atlassian.net/secure/attachment/10415/10415_pippo%25CC%2588.jpg</src>";
//    var url_to_search = "<src>" + escape("https://balsamiqjira.atlassian.net" + "/secure/attachment/" + obj.attachment[index].id + "/" + obj.attachment[index].id + "_" + encodeURIComponent(obj.attachment[index].filename)) + "</src>";
//
//    if (url_to_search == src)
//    {
//        console.log("EUREKA");
//    }

//    var url_to_search = "<src>" + escape("https://balsamiqjira.atlassian.net" + "/secure/attachment/" + obj.attachment[index].id + "/" + obj.attachment[index].id + "_" + encodeURIComponent(obj.attachment[index].filename)) + "</src>";
            // <src>https%3A//balsamiqjira.atlassian.net/secure/attachment/10431/10431_easyone.jpg</src>


//    var url_to_convert =  "./assets/" + escape(encodeURIComponent(obj.attachment[index].filename));
//
//    var bmml_escaped = replaceAll(bmml, url_to_search, url_to_convert);

        }

    });
}

JIRAConnector.prototype.aboutToSetArchiveAttributes = function (logger, sessionData, authToken, attributes, platformData, dbConnector, callback) {
    // do nothing
    callback({});
};

JIRAConnector.prototype.everyoneLeft = function(archiveID, callback) {
    //do nothing
    callback({});
};

JIRAConnector.prototype.getAuthTokenFromPlatform = function(logger, platformInfo, callback) {
    if (platformInfo && platformInfo.jwt) {
        callback && callback({platformToken: platformInfo.jwt});
    } else {
        logger = logger.getLogger({action: "getAuthTokenFromPlatform", module: "jira", platformInfo});
        logger.error("JWT token not available for this entry");
        callback && callback({error: "JWT token not available for this entry"});
    }
};

JIRAConnector.prototype.logUserEvent = function(logger, dbConnector, ip, userInfo, userEvent, platformData, callback) {
    // nothing to do
    callback({});
};

JIRAConnector.prototype.getUsersInfoAndSettings = function(req, userData, callback) {
    // userData {
    //   "data": [
    //     {
    //       "projectId": "NGP-14",
    //       "platformSiteID: "8f896fa1-641f-3e55-9a60-5ff48c4696e5_10028",
    //       "userId": "557058:18e32678-32c8-4d4b-b838-55f0ee6f9597",
    //       "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI1NTcwNTg6MThlMzI2NzgtMzJjOC00ZDRiLWI4MzgtNTVmMGVlNmY5NTk3IiwicXNoIjoiZTdkMGU3ODI3ZTAzYjQ1ZTk2MDkyNzM1NGM3OWM0YzZiYWE4MDYzZjY3ODE2MzEyYjViYzhiZjIwYTVmMjljNSIsImlzcyI6IjhmODk2ZmExLTY0MWYtM2U1NS05YTYwLTVmZjQ4YzQ2OTZlNSIsImNvbnRleHQiOnt9LCJleHAiOjE1NTI1NTkwNDYsImlhdCI6MTU1MjU1ODE0Nn0.wx1uICg_QyVNnUlM-K8sICsMy1ieXqr_t3nylNwTpQc"
    //     }
    //   ],
    //   "platformKind": "jira"
    // }
    let logger = req.bas.logger.getLogger({
        action: "getUsersInfoAndSettings",
        module: "jira"
    });
    var i, contentData, user, claims, iss;
    var ret = {data: []};
    var usersPerProject = {};
    var sessionManager = this.sessionManager;

    this.serverUtils.checkServerAPICredentials(req)
        .then(allowed => {
            if (allowed) {
                // get platform info data if archive is loaded
                if (userData.data && userData.data.length) {
                    logger.info("getting users info and setting from Jira platform ", {userData: userData.data});
                    contentData = {};
                    sessionManager.createSession(logger, "getUsersInfoAndSettings", function (obj) {
                        var sessionData, dbConnector;

                        if (obj.error) {
                            callback && callback({error: "Cannot create db session " + obj.error});
                        } else {
                            sessionData = obj;
                            dbConnector = obj.dbConnector;

                            for (i = 0; i < userData.data.length; i++) {
                                user = userData.data[i];

                                claims = jwt.decode(user.token, '', true);
                                iss = claims.iss;

                                (function (user, iss) {
                                    var projectId, platformSiteID, key;
                                    var accountUserId = user.userId;
                                    contentData[accountUserId] = {status: "onprogress"};

                                    projectId = user.projectId;
                                    platformSiteID = user.platformSiteID;
                                    key = projectId + "_" + platformSiteID;

                                    if (!usersPerProject[key]) {
                                        usersPerProject[key] = {
                                            projectId: projectId,
                                            platformSiteID: platformSiteID,
                                            platformArchiveName: projectId,
                                            users: []
                                        };
                                    }

                                    dbConnector.getConnectorData("jira", iss, function (instanceObj) {
                                        var scopes = "READ";
                                        var baseUrl, accessToken;

                                        if (instanceObj.error || !instanceObj.sharedSecret) {
                                            contentData[accountUserId] = {
                                                status: "error",
                                                error: instanceObj.error ? instanceObj.error : "The instance seems to non be authenticated. Please try to reinstall the plugin"
                                            };
                                        } else {

                                            try {
                                                jwt.decode(user.token, instanceObj.sharedSecret, false);
                                            } catch (error) {
                                                contentData[accountUserId] = {status: "error", error: "JWT signature check failed"};
                                                return;
                                            }

                                            if (instanceObj.oauthClientId && accountUserId) {
                                                baseUrl = instanceObj.baseUrl;
                                                getAccessToken(instanceObj, null, accountUserId, scopes, function (obj) {
                                                    if (obj.error) {
                                                        contentData[accountUserId] = {status: "error", error: obj.error};
                                                    } else {
                                                        accessToken = obj.accessToken;
                                                        getUserDetails(baseUrl, accountUserId, accessToken, contentData, function (obj) {
                                                            if (obj.error) {
                                                                logger.warn("Unknown user " + obj.error);
                                                            } else {
                                                                usersPerProject[key].users.push(obj);
                                                            }
                                                        });
                                                    }
                                                });
                                            } else {
                                                contentData[accountUserId] = {
                                                    status: "error",
                                                    error: "invalid credentials, missing oauthClientId or accountUserId"
                                                };
                                            }
                                        }
                                    });
                                })(user, iss);
                            }

                            watchdog(contentData, function () {
                                var keys, i;
                                sessionManager.releaseSession(sessionData);

                                keys = Object.keys(usersPerProject);

                                for (i=0; i<keys.length; i++) {
                                    ret.data.push(usersPerProject[keys[i]]);
                                }

                                logger.info("returning users info and setting from Jira platform ", ret);
                                callback && callback(ret);
                            });
                        }
                    });
                } else {
                    // nothing to do
                    callback && callback(ret);
                }
            } else {
                callback && callback({error: "invalid credentials"});
            }
        })
        .catch(err => {
            callback && callback({error: err.toString()});
        });
};

var getUserDetails = function (baseUrl, userId, accessToken, contentData, callback) {
    var options, avatarUrl;
    options = {
        method: 'GET',
        url: baseUrl + '/rest/api/3/user?accountId=' + userId,
        auth: {bearer: accessToken},
        headers: {
            'Accept': 'application/json',
            "x-atlassian-force-account-id": "true"
        }
    };

    superagent
        .get(options.url)
        .set('Accept', 'application/json')
        .set('X-Atlassian-Force-Account-Id', 'true')
        .set('Authorization', 'Bearer ' + accessToken)
        .end(function (error, response) {
            if (error) {
                contentData[userId] = {status: "error retrieving user info", error: error};
                callback({error: "error retrieving user info: " + userId});
            } else {
                const user = response.body;
                if (user.accountId) {
                    avatarUrl = user.avatarUrls ? user.avatarUrls["32x32"] : anonAvatarURL;
                    contentData[userId] = {status: "success"};
                    callback({
                        "avatarUrl": avatarUrl,
                        "fullName": user.displayName,
                        "id": user.accountId,
                        "userName": user.accountId,
                        "email": user.emailAddress
                    });
                } else {
                    contentData[userId] = {status: "error", error: "missing account id information"};
                    callback({error: "missing account id information: " + userId + " " + JSON.stringify(user)});
                }
            }
        });
};


JIRAConnector.prototype.getProjectMembers = function(logger, dbConnector, ip, userInfo, userEvent, platformData, callback) {
    var query = userEvent.searchString;
    var mentionedUserIds = userEvent.mentionedUserIds;
    var ret = {users: []};
    logger = logger.getLogger({action: 'getProjectMembers', module: 'jira'});

    // if (query || (mentionedUserIds && mentionedUserIds.length)) {
        try {
            var platformInfo = JSON.parse(platformData.PLATFORM_INFO);
            var authToken = platformInfo.jwt;
            // JWT signature will be verified right after
            var claims = jwt.decode(authToken, '', true);
            var issueId = platformInfo.issueID;
            var iss = claims.iss;
            var options;

            var userKey, userAccountId, accessToken;
            if (claims.sub) {
                // GDPR: userKey deprecated, use urn:atlassian:connect:useraccountid instead
                userAccountId = claims.sub;
            } else if (claims.context && claims.context.user && claims.context.user.userKey) {
                userKey = claims.context.user.userKey;
            }

            dbConnector.getConnectorData("jira", iss, function (instanceObj) {
                var scopes = "READ";
                if (instanceObj.error || !instanceObj.sharedSecret) {
                    ret = instanceObj.error ? instanceObj : {error: "The instance seems to non be authenticated. Please try to reinstall the plugin"};
                    callback && callback(ret);
                } else {
                    if (instanceObj.sharedSecret && instanceObj.oauthClientId && (userKey || userAccountId)) {
                        try {
                            jwt.decode(authToken, instanceObj.sharedSecret, false);
                        } catch (error) {
                            callback && callback({error: "JWT signature check failed"});
                            return;
                        }

                        if (userAccountId) {
                            getAccessToken(instanceObj, userKey, userAccountId, scopes, function (obj) {
                                var url, i, userId, contentData;
                                if (obj.error) {
                                    callback && callback(obj);
                                } else {
                                    accessToken = obj.accessToken;
                                    if (query) {
                                        url = instanceObj.baseUrl + '/rest/api/3/groupuserpicker?query=' + query + "&showAvatar=true&excludeConnectAddons=true";
                                        // GDPR: force x-atlassian-force-account-id header
                                        options = {
                                            method: 'GET',
                                            url: url,
                                            auth: {bearer: accessToken},
                                            headers: {
                                                'Accept': 'application/json',
                                                "x-atlassian-force-account-id": "true"
                                            }
                                        };
                                        logger.info('Requesting query: ' + query);
                                        superagent
                                            .get(options.url)
                                            .set('Accept', 'application/json')
                                            .set('X-Atlassian-Force-Account-Id', 'true')
                                            .set('Authorization', 'Bearer ' + accessToken).end(function (error, response) {
                                                if (error) {
                                                    logger.error(`${error}`, error);
                                                    callback && callback(ret);
                                                } else {
                                                    // logger.info('Response: ' + response.statusCode + ' ' + response.statusMessage);
                                                    let users = response.body;
                                                    if (users.users.total) {
                                                        contentData = {};
                                                        for (let i = 0; i < users.users.users.length; i++) {
                                                            let user = users.users.users[i];
                                                            userId = user.accountId;
                                                            contentData[userId] = {status: "onprogress"};

                                                            if (user.avatarUrl && user.emailAddress) {
                                                                ret.users.push({
                                                                    "avatarUrl": user.avatarUrl,
                                                                    "fullName": user.displayName,
                                                                    "id": userId,
                                                                    "userName": userId,
                                                                    "email": user.emailAddress
                                                                });
                                                                contentData[userId] = {status: "success"};
                                                            } else {
                                                                getUserDetails(instanceObj.baseUrl, userId, accessToken, contentData, function(obj) {
                                                                    if (obj.error) {
                                                                        // user is from another product
                                                                        logger.warn("Unknown user " + obj.error);
                                                                    } else {
                                                                        ret.users.push(obj);
                                                                    }
                                                                });
                                                            }
                                                        }
                                                        watchdog(contentData, function () {
                                                            callback && callback(ret);
                                                        });
                                                    } else {
                                                        callback && callback(ret);
                                                    }
                                                }
                                            }
                                        );
                                    } else {
                                        // we return the user info of the mentioned users + watcher and assignee
                                        if (mentionedUserIds && mentionedUserIds.length) {
                                            // retrieve the user info for the mentioned users
                                            logger.info('Requesting : mentionedUserIds ' + mentionedUserIds);
                                            contentData = {};
                                            for (i = 0; i < mentionedUserIds.length; i++) {
                                                userId = mentionedUserIds[i];
                                                contentData[userId] = {status: "onprogress"};
                                                getUserDetails(instanceObj.baseUrl, userId, accessToken, contentData, function (obj) {
                                                    if (obj.error) {
                                                        // user is from another platform
                                                        logger.warn("Unknown user " + obj.error);
                                                    } else {
                                                        ret.users.push(obj);
                                                    }
                                                });
                                            }

                                            watchdog(contentData, function () {
                                                doAddWatcherAndAssignee(issueId, instanceObj.baseUrl, accessToken, ret, userInfo, logger, callback);
                                            });
                                        } else {
                                            // return the watcher list
                                            doAddWatcherAndAssignee(issueId, instanceObj.baseUrl, accessToken, ret, userInfo, logger, callback);
                                        }
                                    }
                                }
                            });
                        } else {
                            logger.warn("GDPR Unexpected use of claims.context.user.userKey ", instanceObj);
                            callback({error: "Unexpected use of claims.context.user.userKey. Claim sub " + claims.sub});
                        }
                    } else {
                        callback && callback({error: {message: "missing credentials: "}});
                    }
                }
            });
        } catch (e) {
            callback && callback({error: {message: "Failed to retrieve project members: " + e.message}});
        }
};

var doAddWatcherAndAssignee = function(issueId, baseUrl, accessToken, usersList, callerUserInfo, logger, callback) {
    doGetWatcherAssigneeReporter(issueId, baseUrl, accessToken, logger, function(obj) {
        var i;
        var user;
        if (obj.error) {
            callback && callback(usersList);
        } else {
            for (i=0; i<obj.users.length; i++) {
                user = obj.users[i];
                // if the is already inserted or it is the caller do not insert him
                if (isUserAlreadyIn(usersList, user) || user.id === callerUserInfo.name) {
                    // skip
                } else {
                    usersList.users.push(user);
                }
            }
        }
        callback && callback(usersList);
    });
};

var doGetWatcherAssigneeReporter = function(issueId, baseUrl, accessToken, logger, callback) {
    var options, avatarUrl, ret = {users: []};
    logger = logger.getLogger({
        action: "doGetWatcherAssigneeReporter",
        issueId: issueId,
        baseUrl: baseUrl,
        reqId: shortUUID.generate()
    });
    // GDPR: force x-atlassian-force-account-id header
    options = {
        method: 'GET',
        url: baseUrl + '/rest/api/3/issue/' + issueId + '/watchers',
        auth: {bearer: accessToken},
        headers: {
            'Accept': 'application/json',
            "x-atlassian-force-account-id": "true"
        }
    };
    superagent
        .get(options.url)
        .set('Accept', 'application/json')
        .set('X-Atlassian-Force-Account-Id', 'true')
        .set('Authorization', 'Bearer ' + accessToken)
        .end(function (error, response) {
            if (error) {
                logger.error(error.toString(), error);
                callback && callback({error: error.toString()});
            } else {
                const resp = response.body;
                if (resp && resp.watchCount) {
                    for (let i = 0; i < resp.watchers.length; i++) {
                        const watcher = resp.watchers[i];
                        avatarUrl = watcher.avatarUrls ? watcher.avatarUrls["32x32"] : anonAvatarURL;
                        ret.users.push({
                            "fullName": watcher.displayName,
                            "id": watcher.accountId,
                            "avatarUrl": avatarUrl,
                            "userName": watcher.accountId,
                            "email": watcher.emailAddress
                        });
                    }
                }
            }

            // get assignee
            options = {
                method: 'GET',
                url: baseUrl + '/rest/api/3/issue/' + issueId + "?fields=assignee, reporter",
                auth: {bearer: accessToken},
                headers: {
                    'Accept': 'application/json',
                    "x-atlassian-force-account-id": "true"
                }
            };

            superagent
                .get(options.url)
                .set('Accept', 'application/json')
                .set('X-Atlassian-Force-Account-Id', 'true')
                .set('Authorization', 'Bearer ' + accessToken)
                .end(function (error, response) {
                    if (error) {
                        logger.error(error.toString(), error);
                    } else {
                        const resp = response.body;
                        if (resp && resp.fields) {
                            if (resp.fields.assignee) {
                                const assignee = resp.fields.assignee;
                                avatarUrl = assignee.avatarUrls ? assignee.avatarUrls["32x32"] : anonAvatarURL;
                                ret.users.push({
                                    "fullName": assignee.displayName,
                                    "id": assignee.accountId,
                                    "avatarUrl": avatarUrl,
                                    "userName": assignee.accountId,
                                    "email": assignee.emailAddress
                                });
                            }
                            if (resp.fields.reporter) {
                                const assignee = resp.fields.reporter;
                                avatarUrl = assignee.avatarUrls ? assignee.avatarUrls["32x32"] : anonAvatarURL;
                                ret.users.push({
                                    "fullName": assignee.displayName,
                                    "id": assignee.accountId,
                                    "avatarUrl": avatarUrl,
                                    "userName": assignee.accountId,
                                    "email": assignee.emailAddress
                                });
                            }
                        } else {
                            logger.warn("unexpected result looking for reporter", {body: resp});
                        }
                    }
                    callback && callback(ret);
            });
    });
};

JIRAConnector.prototype.projectHasBeenSavedOnPlatform = function(logger, dbConnector, platformSiteID, platformArchiveID, callback) {
    callback && callback({status: true});
};

JIRAConnector.prototype.projectExistsOnPlatform = function(logger, dbConnector, platformSiteID, platformArchiveID, callback) {
    let config = this.config;
    dbConnector.getBASArchiveID(platformSiteID, platformArchiveID, function (obj) {
        var platformInfo, authToken, attachmentID;
        if (obj.error) {
            callback(obj);
        } else {
            if (obj.PLATFORM_INFO) {
                platformInfo = JSON.parse(obj.PLATFORM_INFO);
                authToken = platformInfo.jwt;
                attachmentID = platformInfo.attachmentID;
                doGetAttachmentMetadata(dbConnector, attachmentID, authToken, config, function(obj) {
                    if (obj.error) {
                        obj.platformSiteID = platformSiteID;
                        obj.platformArchiveID = platformArchiveID;
                        callback(obj);
                    } else {
                        callback({
                            status: true,
                            response: obj
                        });
                    }
                });
            } else {
                callback({error: "archive not loaded"});
            }
        }
    });
};

JIRAConnector.prototype.checkUserInfo = function (logger, dbConnector, userInfo, platformToken, callback) {
    var claims;
    var userAccountId;
    var sessionManager = this.sessionManager;
    logger = logger.getLogger({
        module: "jira",
        action: "checkUserInfo"
    });

    if (userInfo.isAnonymous) {
        callback({success: "ok"});
    } else {
        try {
            claims = jwt.decode(platformToken, '', true);
        } catch (e) {
            logger.error("Unable to decode JWT, platform token is " + platformToken);
            claims = {};
        }

        if (claims.sub) {
            userAccountId = claims.sub;
            if (userAccountId !== userInfo.name) {
                callback({error: `userID is not matching: ${userAccountId} !== ${userInfo.name}`});
            } else {
                if (userInfo.avatarUrl || userInfo.displayName || userInfo.email) {
                    sessionManager.createSession(logger, "checkUserInfo", function (obj) {
                        var sessionData, dbConnector;
                        if (obj.error) {
                            callback({error: "Cannot get db session: " + obj.error});
                        } else {
                            sessionData = obj;
                            dbConnector = sessionData.dbConnector;
                            getAccessTokenFromJWT(logger, dbConnector, platformToken, "READ ACT_AS_USER", function (obj) {
                                let instanceObj;
                                if (obj.error) {
                                    callback && callback(obj)
                                } else {
                                    sessionManager.releaseSession(sessionData);
                                    instanceObj = obj.instanceObj;
                                    if (instanceObj.error) {
                                        callback({error: "Cannot get connector data: " + instanceObj.error});
                                    } else {
                                        getUserDetails(instanceObj.baseUrl, userInfo.name, obj.accessToken, {}, function (obj) {
                                            let errorMessage;
                                            // {
                                            //     "avatarUrl": userInfo.avatarUrl,
                                            //     "fullName": userInfo.displayName,
                                            //     "id": userInfo.accountId,
                                            //     "userName": userInfo.name,
                                            //     "email": userInfo.email
                                            // }
                                            if (obj.error) {
                                                callback && callback({error: `error retrieving user details: ${userInfo.name} from ${instanceObj.baseUrl}`});
                                            } else {
                                                if (userInfo.avatarUrl && userInfo.avatarUrl !== obj.avatarUrl) {
                                                    errorMessage = `avatarUrl is not matching: ${userInfo.avatarUrl} !== ${obj.avatarUrl}`;
                                                } else if (userInfo.displayName && userInfo.displayName !== obj.fullName) {
                                                    errorMessage = `displayName is not matching: ${userInfo.displayName} !== ${obj.fullName}`;
                                                } else if (userInfo.email && userInfo.email !== obj.email) {
                                                    errorMessage = `displayName is not matching: ${userInfo.email} !== ${obj.email}`;
                                                }

                                                if (errorMessage) {
                                                    callback && callback({error: "user info check failed: " + errorMessage});
                                                } else {
                                                    callback && callback({success: "ok"});
                                                }
                                            }
                                        });
                                    }
                                }
                            });
                        }
                    });
                } else {
                    // nothing to check
                    callback && callback({success: "ok"});
                }
            }
        } else {
            callback({error: "missing userID information"});
        }
    }
};

export {
    JIRAConnector,
    doRunRedisExpirationKeyListener as JiraRedisExpirationKeyListener,
    makeRedisKey as jiraMakeRedisKey
}
