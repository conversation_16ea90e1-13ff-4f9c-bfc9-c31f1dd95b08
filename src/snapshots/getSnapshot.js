import { pipeStreamToResponse } from '../utils.ts';

// This function is here only for local development because on AWS we have the Cloudfront lambda@edge
async function getSnapshot({
    req,
    res,
    config,
    bucketDir,
    permalinkImageStorageAdapter,
}) {
    // HELPER
    async function statusCodeResponse(code, message) {
        req.bas.logger.info(`Response status: ${code} - ${message}`);
        res.status(code).send(message);
    }

    async function getSnapshot ({UUID, bucketDir, dataResidency, res}) {
        const _pipeStream = async () => {
            const stream = await permalinkImageStorageAdapter.getSnapshotImageStream({bucketDir, UUID, dataResidency});
            return await pipeStreamToResponse(stream, res, [['Content-Type', 'image/png']]);
        }
        try {
            return await _pipeStream();
        } catch (err) {
            // error action handled by the caller
            throw err;
        }
        return {};
    };

    const UUID = req.params.id;

    if (!UUID) {
        return {error: "invalid parameters"};
    }

    req.bas.addLoggerInfo({UUID, bucketDir});
    res.setHeader('Cache-Control', 'no-cache, no-store');

    req.bas.logger.info("Serving snapshot image");

    try {
        await getSnapshot({
            UUID,
            bucketDir,
            res,
            dataResidency: config.defaultDataResidencyName,
        });
        return; //we're done
    } catch (err) {
        req.bas.logger.error(`Unexpected error: ${err.message}`, err);

        if (res.headersSent) {  // Failed while streaming the data. Cannot fix this anymore.
            res.end();
            return;
        }
    }

    return statusCodeResponse(404, "Not found");
}

export {
    getSnapshot
};
