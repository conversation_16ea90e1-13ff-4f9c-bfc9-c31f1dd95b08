import shortUUID from 'short-uuid';
import { base64ToStream } from '../utils.ts';
import { DATA_RESIDENCIES } from '../environment-variables-schemas.ts';

async function createSnapshot({
    req,
    config,
    getSessionData,
    getConnector,
    verifyAdminCredential,
    serverUtils,
    permalinkImageStorageAdapter
}) {
    if (!(await verifyAdminCredential())) {
        return {error: "wrong credentials"};
    }

    const jsonObj = req.body;
    let resourceID = jsonObj.resourceID;
    let branchID = jsonObj.branchID;
    const platformSiteID = jsonObj.platformSiteID;
    const platformArchiveID = jsonObj.platformArchiveID;
    const platformKind = jsonObj.platformKind;
    const userID = jsonObj.platformInfo.userID;
    const bucketDir = jsonObj.platformInfo.bucketDir;
    const format = jsonObj?.platformInfo?.image?.format ? jsonObj.platformInfo.image.format : 'png';
    const quality = jsonObj?.platformInfo?.image?.quality ? jsonObj.platformInfo.image.quality : 1;
    const transparentBackground = jsonObj?.platformInfo?.image?.transparentBackground ? jsonObj.platformInfo.image.transparentBackground : false;
    const dataResidency = jsonObj?.platformInfo?.dataResidency ? jsonObj.platformInfo.dataResidency : config.defaultDataResidencyName;

    req.bas.addLoggerInfo({
        platformArchiveID,
        platformSiteID,
        platformKind,
        platformInfo: jsonObj.platformInfo,
    });

    if (!platformArchiveID || !platformSiteID || !platformKind || !userID || !bucketDir) {
        return {error: "wrong parameters"};
    }

    if (!DATA_RESIDENCIES.includes(dataResidency)) {
        return {error: "Invalid data residency"};
    }

    const connector = getConnector(platformKind);
    if (!connector) {
        return {error: "connector unavailable for platformKind " + platformKind};
    }

    const sessionData = await getSessionData();
    const dbConnector = sessionData.dbConnector;

    req.bas.logger.info(`Getting getResourceNameAndIDs for ${branchID}-${resourceID}` );

    let resourceInfo;
    try {
        ({resourceInfo} = await serverUtils.getResourceNameAndIDs({dbConnector, connector, sessionData, platformKind, platformSiteID, platformArchiveID, platformInfo: jsonObj.platformInfo, resourceID, branchID, logger: req.bas.logger}));
    } catch(err) {
        resourceInfo = null;
    } finally {
        if(!(resourceInfo && resourceInfo.resourceID && resourceInfo.branchID)) {
            return {error: 'No resource found', notFound: true};
        }
        resourceID = resourceInfo.resourceID;
        branchID = resourceInfo.branchID;

        req.bas.addLoggerInfo({
            resourceID,
            branchID,
        });
    }

    const UUID = shortUUID.generate();
    req.bas.logger.info('Creating snapshot...');
    try {
        await connector.generateSnapshot({logger: req.bas.logger, platformKind, platformSiteID, platformArchiveID, resourceID, branchID, platformInfo: {
                userID: userID,
                bucketKey: `snapshots/${bucketDir}/${UUID}.${format}`,
            },
            permalinkInfo: {
                image: {
                    format: format,
                    quality: quality,
                    transparentBackground: transparentBackground
                }
            },
            dataResidency,
        });
    } catch (err) {
        req.bas.logger.error(`Unexpected error in connector.generateSnapshot: ${err.message}`, err);

        //fallback to thumbnail
        if (resourceInfo.thumbnail) {
            // thumbnail format is "png" by design
            req.bas.logger.info("fallback to thumbnail: calling storePermalinkImage");
            const {stream, length} = base64ToStream(resourceInfo.thumbnail);
            await permalinkImageStorageAdapter.uploadSnapshotImage({
                dataStream: stream,
                mimeType: 'image/png',
                UUID: UUID,
                bucketDir: bucketDir,
                format: 'png',
                dataResidency,
                metadata: {
                    platformArchiveID: platformArchiveID,
                    platformSiteID: platformSiteID,
                    platformKind: platformKind,
                    branchID: branchID,
                    resourceID: resourceID,
                    userID: userID,
                }
            });
        }
    }

    req.bas.logger.info("snapshot created");

    return {
        projectName: resourceInfo.projectName,
        name: resourceInfo.name,
        image: connector.getSnapshotUrl(bucketDir, UUID, format, dataResidency)
    };
}



export {
    createSnapshot,
};
