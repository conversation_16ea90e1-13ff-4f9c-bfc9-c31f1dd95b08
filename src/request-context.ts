/** @prettier */
import type { Logger } from '@balsamiq/logging';
import type { Request, Response } from 'express';
import { getUUID } from './utils.ts';
import { RedisAdapter } from './redisAdapter.ts';
import { callWithLegacyCallback } from './calling-style.ts';
import { type SessionData, SessionManager } from './session-manager.ts';
import { type ServerUtils } from './server_utils.js';
import { type MinRoleForAccess } from './roleforaccess.ts';
import type { BlockList } from './blocklist.ts';
import type { GetValidUserForRoleParams } from './sane-helpers.ts';
import type { User } from './db_connector.js';
import type { Metrics } from './metrics.ts';
import type { Clock } from './clock.js';

// export type BASRequest = Request & { bas: BASRequestContext };
export interface BASRequest extends Request {
    bas: BASRequestContext;
}

export type BASRequestContextParams = {
    logger: Logger;
    redisAdapter: RedisAdapter;
    sessionManager: SessionManager;
    serverUtils: ServerUtils;
    minRoleForAccess: MinRoleForAccess;
    getConnector(kind: string): AnyConnector;
    blockList: BlockList;
    sane_getValidUserForRole(params: GetValidUserForRoleParams): Promise<User>;
    returnJSON(data: object, res: Response, sessionData?: SessionData, disableLog?: boolean): void;
    metrics: Metrics;
    clock: Clock;
};

export class BASRequestContext {
    private sessionData: SessionData | null = null;

    req: Request;
    res: Response;

    logger: Logger;
    redisAdapter: RedisAdapter;
    sessionManager: SessionManager;
    serverUtils: ServerUtils;
    minRoleForAccess: MinRoleForAccess;
    getConnector: (kind: string) => AnyConnector;
    blockList: BlockList;
    getValidUserForRole: (params: GetValidUserForRoleParams) => Promise<User>;
    returnJSON: (data: object, res: Response, sessionData?: SessionData, disableLog?: boolean) => void;
    metrics: Metrics;
    clock: Clock;

    constructor(req: Request, res: Response, params: BASRequestContextParams) {
        const { logger } = params;
        this.logger = logger.getLogger({
            reqId: getUUID(),
            sourceIP: req.ip,
            request: {
                method: req.method,
                path: req.path,
                param: req.query,
            },
            userAgent: {
                header: req.headers['user-agent'],
            },
        });
        this.res = res;
        this.req = req;
        this.redisAdapter = params.redisAdapter;
        this.sessionManager = params.sessionManager;
        this.serverUtils = params.serverUtils;
        this.minRoleForAccess = params.minRoleForAccess;
        this.getConnector = params.getConnector;
        this.blockList = params.blockList;

        this.sessionData = null;
        this.getValidUserForRole = params.sane_getValidUserForRole;
        this.returnJSON = params.returnJSON;
        this.metrics = params.metrics;
        this.clock = params.clock;
    }

    addLoggerInfo(params: { [T: string]: unknown }) {
        this.logger = this.logger.getLogger(params);
    }

    sendStatusCodeJsonResponse(statusCode: number, value: unknown) {
        this.res.setHeader('Cache-Control', 'no-cache, no-store');
        this.res.status(statusCode).json(value);
    }

    async acquireSession({ token }: { token?: string } = {}) {
        if (this.sessionData) {
            return this.sessionData;
        }
        const sessionData = await callWithLegacyCallback<SessionData>((cb) =>
            this.sessionManager.createSession(this.logger, this.req.route.path, cb)
        );
        if (token) {
            sessionData.token = token;
        }
        this.sessionData = sessionData;
        return sessionData;
    }

    async releaseSession() {
        if (!this.sessionData) {
            return;
        }
        try {
            const sessionToRelease = this.sessionData;
            await callWithLegacyCallback((cb) => this.sessionManager.releaseSession(sessionToRelease, cb));
            this.sessionData = null;
        } catch (err) {
            this.logger.error(`Release session failed`, err as Error);
            // Do not throw an error here, as it is not critical
        }
    }

    async withSession<T>(action: (session: SessionData) => Promise<T>, params: { token?: string } = {}): Promise<T> {
        const session = await this.acquireSession(params);
        try {
            return await action(session);
        } finally {
            await this.releaseSession();
        }
    }

    async verifyAdminCredential() {
        const isAdmin = await this.serverUtils.checkServerAPICredentials(this.req);

        if (!isAdmin) {
            this.logger.info(`Bad admin credentials`);
        }

        return isAdmin;
    }

    static assertBASRequestContext(req: Request): asserts req is BASRequest {
        if (!this.isBASRequest(req)) {
            throw new Error('BASRequestContext is not set');
        }
    }

    static isBASRequest(req: Request): req is BASRequest {
        return 'bas' in req && req.bas instanceof BASRequestContext;
    }

    static augmentRequestObject(req: Request, res: Response, params: BASRequestContextParams): req is BASRequest {
        if (!this.isBASRequest(req)) {
            (req as BASRequest).bas = new BASRequestContext(req, res, params);
        }
        return true;
    }
}
