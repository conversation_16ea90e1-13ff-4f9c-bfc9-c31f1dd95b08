import con from './constants.ts';
import { getValidUserForRole as sane_getValidUserForRole } from './sane-helpers.js';
import { callWithLegacyCallback } from './calling-style.ts';

async function getUsersList({ req, res, sessionManager, minRoleForAccess, rtcAdapter, metrics, clock, serverUtils: { getKindFromArchiveID } }) {
    const token = req.query.token;
    const option = req.query.option;
    req.bas.addLoggerInfo({ action: "getUsersList", sessionToken: token });
    res.setHeader("Cache-Control", "no-cache, no-store");
    let archiveID;
    let optionsFromEditor = null;

    // -- Sanity checks ----------------------------

    await sessionManager.withSession(req.bas.logger, "restore-sanity-checks", async (sessionData) => {
        const user = await sane_getValidUserForRole({
            token,
            dbConnector: sessionData.dbConnector,
            metrics,
            logger: req.bas.logger,
            role: minRoleForAccess,
            sessionData,
        });
        archiveID = user.ARCHIVE_ID;
        const userInfo = user.USERINFO ? JSON.parse(user.USERINFO) : null;
        if (userInfo && userInfo.basOptions) {
            optionsFromEditor = userInfo.basOptions;
        }
        req.bas.addLoggerInfo({ archiveID });
    });

    // -- Query RTC server ----------------------------

    const kind = getKindFromArchiveID(archiveID);
    let uuids = await rtcAdapter.herenow(archiveID, req.bas.logger);

    if (uuids.length === 0) {
        return { users: [] };
    }

    // -- Build response ----------------------------
    return await sessionManager.withSession(req.bas.logger, "restore-build-response", async (sessionData) => {
        const { users, basSessionsCount, minTimestamp, maxTimestamp } = await callWithLegacyCallback(cb => sessionData.dbConnector.getUsersList(uuids, option, cb));
        let now = clock.now();
        if (users && users.length) {
            metrics.addValue('rtc-users-count-' + kind, users.length, 'Count');
            req.bas.addLoggerInfo({
                sessionsCount: uuids.length,
                usersCount: users.length,
                basSessionsCount: basSessionsCount,
                minTimestamp: minTimestamp,
                maxTimestamp: maxTimestamp,
                minTimestampInSecs: (now - minTimestamp) / 1000,
                maxTimestampInSecs: (now - maxTimestamp) / 1000,
                uuids: uuids,
            })
            // RTC log BW Web editor in background is very noisy, we disable the log for single user session
            if (users.length > 1 && basSessionsCount > 1) {
                req.bas.logger.info("Users Online " + users.length + ", RTC sessions " + uuids.length + ", BAS sessions " + basSessionsCount);
            }
        }
        return { users };
    });
}

export {
    getUsersList,
};
