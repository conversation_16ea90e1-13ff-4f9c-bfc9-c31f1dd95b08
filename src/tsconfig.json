{"compilerOptions": {"incremental": false, "target": "es2020", "module": "nodenext", "allowJs": true, "noEmit": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "erasableSyntaxOnly": true, "verbatimModuleSyntax": true, "rewriteRelativeImportExtensions": true, "allowImportingTsExtensions": true}, "exclude": ["node_modules"], "ts-node": {"esm": true, "transpileOnly": true}}