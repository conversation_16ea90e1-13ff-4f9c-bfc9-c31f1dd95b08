/** @prettier */
import type { Request } from 'express';
import auth from 'basic-auth';
import compare from 'tsscmp';

// Secure Basic-Auth check implementation
// Documentation:
// - https://www.npmjs.com/package/basic-auth (Generic basic auth Authorization header field parser)
// - https://www.npmjs.com/package/tsscmp (Timing safe string compare using double HMAC)

function checkBasicAuthOnRequest(req: Request, name: string, pass: string): boolean {
    const credentials = auth(req);
    if (!credentials) {
        return false;
    }
    return compare(credentials.name, name) && compare(credentials.pass, pass);
}

export { checkBasicAuthOnRequest };
