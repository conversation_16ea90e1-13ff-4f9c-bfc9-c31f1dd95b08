import { z } from 'zod';

// List of data residencies that we support
// N.B.: if you change this, you must also apply a similar change in Cloud code base, because data residencies supported by BAS and Cloud must match
export const DATA_RESIDENCIES = ['us', 'eu'] as const;
export type DataResidencyName = (typeof DATA_RESIDENCIES)[number];

export const RTC_CONFIG_SECRET_SCHEMA = z.object({
    jwt_secrets: z.record(z.string(), z.array(z.string())),
    server_secrets: z.array(z.string()),
    heartbeat_interval_in_sec: z.number(),
});

export const MYSQL_SECRET_SCHEMA = z.object({
    username: z.string(),
    password: z.string(),
    dbname: z.string(),
    host: z.string(),
    port: z.number(),
});

export const BAS_SERVER_API_SECRET_SCHEMA = z.object({
    data: z.record(z.string(), z.array(z.string())),
    pollingIntervalInMin: z.number(),
});

// We do it this way (i.e. dynamic definition of an object schema, and then cast to the propert Zod type) because the cleaner alternative
// would be to use z.record(z.enum(DATA_RESIDENCIES), z.string()), but that way when we parse a value we get a
// Partial<...> result (i.e. all properties are optional), which is not what we want. We want all data residency regions to be required.
export const DATA_RESIDENCY_SHARES_SCHEMA = z.object(
    Object.fromEntries(DATA_RESIDENCIES.map((dr) => [dr, z.string()])) as { [K in DataResidencyName]: z.ZodString }
);
export type DataResidenciesConfig = z.infer<typeof DATA_RESIDENCY_SHARES_SCHEMA>;

const PERMALINK_STORAGE_REGION_SCHEMA = z.object({
    bucketRegion: z.string(),
    bucketName: z.string(),
    baseDir: z.string(),
});
export const PERMALINK_STORAGE_SCHEMA = z.object(
    Object.fromEntries(DATA_RESIDENCIES.map((dr) => [dr, PERMALINK_STORAGE_REGION_SCHEMA])) as {
        [K in DataResidencyName]: typeof PERMALINK_STORAGE_REGION_SCHEMA;
    }
);
export type PermalinkStorage = z.infer<typeof PERMALINK_STORAGE_SCHEMA>;

export const CLOUD_CONFIG_SCHEMA = z.object({
    basicAuthCredentials: z
        .object({
            bas: z.object({ username: z.string(), password: z.string() }),
        })
        .passthrough(),
    basTokenSecretKey: z.string(),
});
export type CloudConfig = z.infer<typeof CLOUD_CONFIG_SCHEMA>;

export const BAS_APP_CONFIG_SCHEMA = z.object({
    redis: z.object({
        url: z.string(),
        port: z.number(),
    }),
    mysql: z.object({
        region: z.string(),
    }),
    reduceLogging: z.boolean(),
    permalinkStorage: PERMALINK_STORAGE_SCHEMA,
    dataResidencyShares: DATA_RESIDENCY_SHARES_SCHEMA,
    defaultDataResidencyName: z.enum(DATA_RESIDENCIES),
    cloud: z.object({
        baseUrl: z.string(),
        projectsMaxAgeInDays: z.number(),
        timeDeltaForSavingLiveProjectsInMinutes: z.number(),
    }),
    rtc: z.object({
        websocketsUri: z.string(),
    }),
    proxyConfig: z.array(z.object({
        prefix: z.string(),
        host: z.string(),
        path: z.string(),
    })),
    confluenceNamespace: z.string(),
    jiraNamespace: z.string(),
    metricsRegion: z.string(),
    clusterSize: z.number(),
    i2w: z.object({
        serviceUrl: z.string(),
    }),
    w2i: z.object({
        region: z.string(),
        lambdaFunctionArn: z.string(),
    }),
    kms: z.object({
        region: z.string(),
        key_alias: z.string(),
        environment: z.string(),
    }),
});
export type BasAppConfig = z.infer<typeof BAS_APP_CONFIG_SCHEMA>;
