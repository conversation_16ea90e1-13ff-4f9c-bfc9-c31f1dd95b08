/** @prettier */
import { HardcodedBalsamiqSecrets, multipleSecretLists } from '@balsamiq/serverconfig/lib/secrets.js';
import { getEnvironmentSpecs } from './config-environment.ts';
import type { Config } from './configLoader.ts';
import { parseZodVariableFromJsonString } from './environment-variables-parsing.ts';
import {
    BAS_APP_CONFIG_SCHEMA,
    BAS_SERVER_API_SECRET_SCHEMA,
    CLOUD_CONFIG_SCHEMA,
    MYSQL_SECRET_SCHEMA,
    RTC_CONFIG_SECRET_SCHEMA,
} from './environment-variables-schemas.ts';

function buildECSConfig(cdkBuildProcess: boolean): Config {
    const env = getEnvironmentSpecs({ cdkBuildProcess }).getValues([
        'APP_NAME',
        'BAS_APP_CONFIG',
        'BAS_ENV',
        'BAS_FULL_DOMAIN_NAME',
        'BAS_SERVER_API_SECRET',
        'BUILD_NUMBER',
        'CLOUD_CONFIG_SECRET',
        'MYSQL_SECRET',
        'RTC_SECRET',
    ]);

    const environment = env.getString('BAS_ENV');

    const mysqlSecret = parseZodVariableFromJsonString(env.getString('MYSQL_SECRET'), 'MySQL Secret', MYSQL_SECRET_SCHEMA.passthrough());
    const serverApiSecret = parseZodVariableFromJsonString(
        env.getString('BAS_SERVER_API_SECRET'),
        'BAS Server API Secret',
        BAS_SERVER_API_SECRET_SCHEMA
    );
    const cloudConfig = parseZodVariableFromJsonString(
        env.getString('CLOUD_CONFIG_SECRET'),
        'Cloud Config Secret',
        CLOUD_CONFIG_SCHEMA.passthrough()
    );
    const basAppConfig = parseZodVariableFromJsonString(
        env.getString('BAS_APP_CONFIG'),
        'BAS App Config',
        BAS_APP_CONFIG_SCHEMA.passthrough()
    );

    return {
        port: 4000,
        clusterSize: basAppConfig.clusterSize,
        mySQLConfig: {
            credentialsSecret: { getSecret: () => mysqlSecret },
            region: basAppConfig.mysql.region,
            basDBName: 'BAS',
            permalinksDBName: 'PERMALINKS',
        },
        archiveIDPrefix: '',
        appName: env.getString('APP_NAME'),
        environmentName: environment,
        baseUrl: `https://${env.getString('BAS_FULL_DOMAIN_NAME')}`,
        shareUrls: basAppConfig.dataResidencyShares,
        defaultDataResidencyName: basAppConfig.defaultDataResidencyName,
        cloudBaseUrl: basAppConfig.cloud.baseUrl,
        archivesPath: '/dev/shm/archives',
        https: false,
        cloudProjectsMaxAgeInDays: basAppConfig.cloud.projectsMaxAgeInDays,
        cloudTimeDeltaForSavingLiveProjectsInMinutes: basAppConfig.cloud.timeDeltaForSavingLiveProjectsInMinutes,
        unloadUnusedArchivesForConnectors: ['jira', 'confluence', 'cloud', 'wd'],
        connectors: ['cloud', 'confluence', 'jira', 'wd'],
        maxNumberOfProjectsToUnload: 50,
        confluenceNamespace: basAppConfig.confluenceNamespace,
        jiraNamespace: basAppConfig.jiraNamespace,
        getRtcConfig() {
            return {
                secrets: parseZodVariableFromJsonString(env.getString('RTC_SECRET'), 'RTC Secret', RTC_CONFIG_SECRET_SCHEMA),
                websocketsUri: basAppConfig.rtc.websocketsUri,
                jwtCallbackInfo: {
                    basBaseURL: `https://${env.getString('BAS_FULL_DOMAIN_NAME')}/`, // Include trailing slash
                    secret: {
                        type: 'aws-secrets',
                        environment: environment,
                        username: 'bas',
                    },
                },
            };
        },
        getServerApiSecrets() {
            return new HardcodedBalsamiqSecrets(multipleSecretLists(), serverApiSecret);
        },
        metricRegion: basAppConfig.metricsRegion,
        metricNamespace: `balsamiq/${env.getString('APP_NAME')}`,
        buildNumber: env.getString('BUILD_NUMBER'),
        permalinkS3Storage: basAppConfig.permalinkStorage,
        kmsService: {
            region: basAppConfig.kms.region,
            key_arn: basAppConfig.kms.key_alias,
            environment: basAppConfig.kms.environment,
        },
        w2iService: {
            region: basAppConfig.w2i.region,
            key_arn: basAppConfig.w2i.lambdaFunctionArn,
        },
        i2wService: {
            url: basAppConfig.i2w.serviceUrl,
        },
        proxyConfig: basAppConfig.proxyConfig,
        redisDB: 0,
        redisURL: basAppConfig.redis.url,
        redisPort: basAppConfig.redis.port,
        reduceLogging: basAppConfig.reduceLogging,
        loggerOutputForElasticSearch: true,
        cloudConfiguration: {
            cloudServerBaseUrl: basAppConfig.cloud.baseUrl,
            jwtSecret: cloudConfig.basTokenSecretKey,
            cloudBasicAuthCredentials: {
                username: cloudConfig.basicAuthCredentials.bas.username,
                password: cloudConfig.basicAuthCredentials.bas.password,
            },
        },
    };
}

export { buildECSConfig };
