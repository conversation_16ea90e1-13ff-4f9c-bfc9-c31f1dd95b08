/** @prettier */
import { constants as con } from './constants.ts';

export const minRoleForAccess = {
    [con.API_REFRESHSESSION]: con.ROLE_VIEWER,
    [con.API_FETCH]: con.ROLE_VIEWER,
    [con.API_OPEN]: con.ROLE_VIEWER,
    [con.API_CLOSE]: con.ROLE_VIEWER,
    [con.API_RESTORE]: con.ROLE_EDITOR,
    [con.API_GETARCHIVEREVISION]: con.ROLE_VIEWER,
    //[con.API_GETARCHIVEATTRIBUTES]: con.ROLE_VIEWER,
    //[con.API_GETBRANCHES]: con.ROLE_VIEWER,
    [con.API_GETBRANCHATTRIBUTES]: con.ROLE_VIEWER,
    [con.API_GETTOC]: con.ROLE_VIEWER,
    [con.API_GETRESOURCEATTRIBUTES]: con.ROLE_VIEWER,
    [con.API_GETRESOURCEDATA]: con.ROLE_VIEWER,
    [con.API_GETRESOURCESDATA]: con.ROLE_VIEWER,
    [con.API_GETHEURISTICARCHIVESIZE]: con.ROLE_VIEWER,
    [con.API_GETTHUMBNAIL]: con.ROLE_VIEWER,
    [con.API_CREATEBRANCH]: con.ROLE_EDITOR,
    [con.API_DELETEBRANCHES]: con.ROLE_EDITOR,
    [con.API_SETBRANCHATTRIBUTES]: con.ROLE_EDITOR,
    [con.API_CREATERESOURCE]: con.ROLE_EDITOR,
    [con.API_DELETERESOURCES]: con.ROLE_EDITOR,
    [con.API_SETRESOURCEATTRIBUTES]: con.ROLE_EDITOR,
    [con.API_SETRESOURCEADATA]: con.ROLE_EDITOR,
    [con.API_SETRESOURCEABRANCHID]: con.ROLE_EDITOR,
    [con.API_CREATETHUMBNAIL]: con.ROLE_EDITOR,
    [con.API_SETTHUMBNAIL]: con.ROLE_EDITOR,
    [con.API_DELETETHUMBNAILS]: con.ROLE_EDITOR,
    [con.API_FLUSH]: con.ROLE_EDITOR,
    [con.API_DOWNLOAD]: con.ROLE_VIEWER,
    [con.API_CREATECOMMENT]: con.ROLE_COMMENTER,
    [con.API_IMPORTCOMMENT]: con.ROLE_EDITOR,
    [con.API_GETCOMMENTSDATA]: con.ROLE_VIEWER,
    [con.API_SETCOMMENTDATA]: con.ROLE_COMMENTER,
    [con.API_DELETECOMMENTS]: con.ROLE_COMMENTER,
    // [con.API_SETCOMMENTREAD]: con.ROLE_COMMENTER,
    // [con.API_SETCOMMENTLIKED]: con.ROLE_COMMENTER,
    // [con.API_SETCOMMENTTRASHED]: con.ROLE_COMMENTER,
    [con.API_UPDATECOMMENTATTRIBUTES]: con.ROLE_COMMENTER,
    [con.API_UPDATECOMMENTSATTRIBUTES]: con.ROLE_COMMENTER,
    [con.API_CREATEMYUSER]: con.ROLE_COMMENTER,
    [con.API_UPDATEMYUSER]: con.ROLE_COMMENTER,
    [con.API_GETARCHIVEUSERSLIST]: con.ROLE_VIEWER,
    [con.API_CREATE]: con.ROLE_ADMIN,
    [con.API_DELETE]: con.ROLE_ADMIN,
    [con.API_UNLOAD_ARCHIVE]: con.ROLE_EDITOR,
    [con.API_UNLOAD_ARCHIVE_IF_NOT_SYNC]: con.ROLE_EDITOR,
    [con.API_SETARCHIVEATTRIBUTES]: con.ROLE_ADMIN,
    [con.API_SETPLATFORMARCHIVENAME]: con.ROLE_ADMIN,
    [con.API_GETUSERSLIST]: con.ROLE_VIEWER,
    [con.API_BROADCASTMESSAGE]: con.ROLE_VIEWER,
    [con.API_GETUSERINFO]: con.ROLE_VIEWER,
    [con.API_UPDATEUSERINFO]: con.ROLE_VIEWER,
    [con.API_UPLOADTEMPFILE]: con.ROLE_VIEWER,
    [con.API_DOWNLOADTEMPFILE]: con.ROLE_VIEWER,
    [con.API_UNLOAD_ARCHIVE_OFFLINE]: con.ROLE_ADMIN,
    [con.API_LOGUSEREVENT]: con.ROLE_COMMENTER,
    [con.API_GETPROJECTMEMBERS]: con.ROLE_VIEWER,
    [con.API_PERMALINK]: con.ROLE_VIEWER,
    [con.API_CREATE_OR_UPDATE_IMAGE_LINK]: con.ROLE_EDITOR,
    [con.API_PUBLIC_SHARE]: con.ROLE_VIEWER,
    [con.API_SETPERMALINKDATA]: con.ROLE_EDITOR,
    [con.API_GETPERMALINKDATA]: con.ROLE_VIEWER,
    [con.API_DELETEPERMALINKDATA]: con.ROLE_EDITOR,
    [con.API_GETPERMALINKSDATA]: con.ROLE_EDITOR,
    [con.API_GET_USER_AUTH_TOKEN]: con.ROLE_EDITOR,
    [con.API_FLUSH_OFFLINE]: con.ROLE_ADMIN,
    [con.API_TOUCH_RESOURCE]: con.ROLE_EDITOR,
} as const;

export type MinRoleForAccess = typeof minRoleForAccess;
