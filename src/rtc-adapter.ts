/** @prettier */
import type { Logger } from '@balsamiq/logging';
import type { Metrics } from './metrics.ts';
import type { BackendRTCClient } from '@balsamiq/serverconfig/lib/rtc-config.js';

export type BackendRTCInterface = Pick<BackendRTCClient, 'herenow' | 'sendMessage' | 'getFrontendConfig' | 'buildRTCTokenInfo'>;

class RtcAdapter {
    private backendRTCClient: BackendRTCInterface;
    private logger: Logger;
    private metrics: Metrics;

    constructor({ backendRTCClient, logger, metrics }: { backendRTCClient: BackendRTCInterface; logger: Logger; metrics: Metrics }) {
        this.logger = logger.getLogger({ module: 'rtc-adapter' });
        this.metrics = metrics;
        this.backendRTCClient = backendRTCClient;
    }

    async herenow(channelName: string, logger: Logger) {
        logger = logger.getLogger({ module: 'rtc-adapter' });
        const uuids = await this.backendRTCClient.herenow(channelName, logger);
        return uuids;
    }

    sendMessage(channelID: string, msg: any) {
        const logger = this.logger.getLogger({ action: 'sendMessage' });

        if (!msg) {
            logger.info('Ignore null or undefined msg');
            return;
        }

        msg.channel = msg.channel || channelID;

        this.backendRTCClient.sendMessage(channelID, msg, this.logger);
        this.metrics.addValue('Num. WebSockets messages sent', 1, 'Count');
    }

    async getFrontendConfig(logger: Logger) {
        logger = logger.getLogger({ module: 'rtc-adapter' });
        const config = await this.backendRTCClient.getFrontendConfig(logger);
        return config;
    }

    async buildRTCTokenInfo(channelName: string, logger: Logger, extraFields: { [k: string]: unknown }) {
        logger = logger.getLogger({ module: 'rtc-adapter' });
        const tokenInfo = await this.backendRTCClient.buildRTCTokenInfo(channelName, logger, extraFields);
        return tokenInfo;
    }
}

export { RtcAdapter };
