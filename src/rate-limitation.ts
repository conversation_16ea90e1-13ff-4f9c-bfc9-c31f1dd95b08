import { type RateLimitDefinition, RateLimiterConfiguration, type RateLimiterRedisStoreInterface } from "@balsamiq/ratelimit";
import type { Metrics as BalsamiqMetrics, Bucket, IntervalTracker } from "@balsamiq/metrics";
import type { RedisAdapter } from "./redisAdapter.ts";
import type { Logger } from "@balsamiq/logging";
import { Metrics } from "./metrics.ts";

const MAX_NUM_STORED_FAILURES = 100;

const RATE_LIMITERS: RateLimitDefinition<string, string>[] = [
    {
        name: "create:global",
        // There's only gonna be 1 global bucket for the whole application,
        // but we still need to provide a property, so we're going to
        // always feed the same property "application = bas" (but any fixed value would equally work).
        propName: "application",
        description: "Create project",
        maxValue: 50, // 8-16 is a normal range for production. 50 should be plenty for regular work, but low for malicious use (30 Jun 2020)
        expirationInSeconds: 60,
        publicErrorMessage: "Too many projects are being created. Please wait 60 seconds and try again.",

        // NOTE Stefano 5 Jun 2020
        // We want to eventually implement per-user rate limitation, but it's currently
        // difficult due to the fact that some actions are performed, on behalf of the user,
        // by the Cloud server. Therefore, in order to implement a proper limitation we would
        // first need to forward such information from the Cloud server, i.e. user ID and IP address.

        // }, {
        //     name: 'create:userSession',
        //     propName: 'userSession',
        //     description: 'Create project',
        //     maxValue: 10,
        //     expirationInSeconds: 60,
        //     publicErrorMessage: 'You can only create 5 projects a minute. Please wait 60 seconds and try again.',
    },
    {
        name: "getRtcAuthToken",
        propName: "sessionToken",
        description: "Create RTC auth token",
        maxValue: 10, // 1 request every 10-20 minutes (per user session) is the expected rate. 10 is to play safe and still protect the server in case of a rogue client.
        expirationInSeconds: 20 * 60,
        publicErrorMessage: "Too many RTC auth tokens requests",
    },
    {
        name: "usersLeftArchive",
        propName: "application",
        description: "RTC notification that users left",
        maxValue: 100, // <10 notifications every minute is the expected global rate. In case of RTC problems we've seen tens of thousands, and we want to protect from that.
        expirationInSeconds: 60,
        publicErrorMessage: "Too many user left RTC notifications",
    },
    {
        name: "healthCheck",
        propName: "application",
        description: "Health check",
        maxValue: 2, // This is just for safety. The health endpoint is password protected, so it's not expected to be abused.
        expirationInSeconds: 60,
        publicErrorMessage: "Too many health checks",
    },
] as const;

function makeRateLimiterConfiguration(rateLimiters: typeof RATE_LIMITERS, metrics: Metrics, redisAdapter: RedisAdapter, logger: Logger) {
    return new RateLimiterConfiguration(
        "rtlm",
        MAX_NUM_STORED_FAILURES,
        new RedisRateLimitationAdapter(redisAdapter, "bas"),
        rateLimiters,
        new MetricsAdapter({ metrics, logger })
    );
}

// -- Adapters -----------

class MetricsAdapter implements BalsamiqMetrics {
    metrics: Metrics;
    logger: Logger;
    aggregateValues: null;
    metricsCollector: null;

    constructor({ logger, metrics }: { metrics: Metrics; logger: Logger }) {
        this.logger = logger.getLogger({ subsystem: "rate-limiter-metrics-adapter" });
        this.metrics = metrics;
        this.aggregateValues = null;
        this.metricsCollector = null;
    }

    incrementCounter(amount: number, buckets: Bucket[]): void {
        for (let { dimensions, metricName } of buckets) {
            if (!dimensions) dimensions = [];
            this.metrics.addValueWithDimensions(
                dimensions.map(({ name, value }) => ({ Name: name, Value: value })),
                metricName,
                amount,
                "Count"
            );
        }
    }

    // WARNING: The following are unimplemented methods that are not expected be called from the RedisRateLimitationAdapter
    async flush(): Promise<void> {
        this._logNotImplementedMethod("flush");
    }
    startCollectingMemoryInfo(): void {
        this._logNotImplementedMethod("startCollectingMemoryInfo");
    }
    logValue({ dimensions, metricName }: Bucket, value: number, unit: string): void {
        this._logNotImplementedMethod("logValue");
    }
    trackInterval(): IntervalTracker {
        this._logNotImplementedMethod("trackInterval");
        return { stop: () => 0 };
    }
    _logNotImplementedMethod(methodName: string) {
        this.logger.error(
            `RateLimiter called a method not implemented in MetrcisAdapter: ${methodName}`,
            new Error("Method not implemented.")
        );
    }
}

class RedisRateLimitationAdapter implements RateLimiterRedisStoreInterface<Logger>{
    redisAdapter: RedisAdapter;
    keyPrefix: string;

    constructor(redisAdapter: RedisAdapter, keyPrefix: string) {
        this.redisAdapter = redisAdapter;
        this.keyPrefix = keyPrefix;
    }

    async evalLua(script: string, keys: string[], args: (string | number)[], logger: Logger): Promise<any> {
        logger.info(`running eval script`, { subsystem: 'rate-limitation-redis-adapter' });
        return await this.redisAdapter.eval(script, keys.map(key => this.keyPrefix + key), args.map((arg) => arg.toString()));
    }
    async delete(key: string, logger: Logger): Promise<number> {
        logger.info(`delete key ${key}`, { subsystem: 'rate-limitation-redis-adapter' });
        return await this.redisAdapter.del(this.keyPrefix + key);
    }
    async fetch(key: string, logger: Logger): Promise<string | null> {
        logger.info(`get key ${key}`, { subsystem: 'rate-limitation-redis-adapter' });
        return await this.redisAdapter.get(this.keyPrefix + key);
    }
    async lrange(key: string, start: number, stop: number, logger: Logger): Promise<string[]> {
        logger.info(`LRANGE ${key} ${start} ${stop}`, { subsystem: 'rate-limitation-redis-adapter' });
        return await this.redisAdapter.lrange(this.keyPrefix + key, start, stop);
    }
}

export { RATE_LIMITERS, makeRateLimiterConfiguration };
