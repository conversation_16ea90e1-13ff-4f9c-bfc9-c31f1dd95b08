import { callWithLegacyCallback } from './calling-style.ts';

const CONNECTOR_KIND = "cloud";
const NUM_SYNC_ATTEMPTS = 3;
const SYNC_FAILURE_DELAY = 5000;
const DELAY_BETWEEN_ARCHIVES = 200;
const DELTA_ACTIVE_SESSION = 11 * 60 * 1000; // 11 minutes


/**
 * @param {Object} param0
 * @param {JSDocTypes.ServerUtils} param0.serverUtils
 * @param {JSDocTypes.SessionManager} param0.sessionManager
 * @param {JSDocTypes.Logger} param0.logger
 * @param {JSDocTypes.Metrics} param0.metrics
 * @param {number} param0.timeDeltaForSavingLiveProjects
 * @param {JSDocTypes.Clock} param0.clock
 * @param {JSDocTypes.CloudConnector} param0.cloudConnector
 * @returns {Promise<void>}
 */
async function cloudGardening({ serverUtils, sessionManager, logger, metrics, timeDeltaForSavingLiveProjects, clock, cloudConnector }) {
    await sessionManager.withSession(logger, 'cloud-gardening', async sessionData => {
        const { thresholdForExpiredSessions, thresholdForSavingLiveProjects, expiredArchiveIDs, liveArchiveIDs, spacesUsageStats } = await filterArchives({ sessionData, timeDeltaForSavingLiveProjects, metrics, logger, clock });
        try {
            await cloudConnector.publishSpacesUsageStats(logger, spacesUsageStats);
        } catch (err) {
            logger.error('Error while publishing Space usage stats', err);
        }
        // Check among projects that are not currently edited to see if some are edited and need saving on the platform
        await processExpiredArchives({ expiredArchiveIDs, logger, sessionData, thresholdForExpiredSessions, serverUtils, clock });
        // Check among projects being actively edited to see if some need auto-saving
        await processLiveArchives({ liveArchiveIDs, sessionData, thresholdForSavingLiveProjects, logger, serverUtils, clock });
    });
}

async function filterArchives({ sessionData, timeDeltaForSavingLiveProjects, logger, metrics, clock }) {
    const now = clock.now();
    const thresholdForSavingLiveProjects = now - timeDeltaForSavingLiveProjects;
    const thresholdForExpiredSessions = now - DELTA_ACTIVE_SESSION;

    let oldestSessionsMap = new Map();
    let usersMap = new Map();
    const { rows } = await callWithLegacyCallback(cb => sessionData.dbConnector.getAllSessionsForKind(CONNECTOR_KIND, cb));
    for (let row of rows) {
        // Find the oldest session for each archive ID
        if (!oldestSessionsMap.has(row.ARCHIVE_ID) || row.TIMESTAMP > oldestSessionsMap.get(row.ARCHIVE_ID).TIMESTAMP) {
            oldestSessionsMap.set(row.ARCHIVE_ID, row);
        }

        // Store, for each archive ID, the list of users
        const userId = JSON.parse(row.USERINFO).userId;
        if (userId) {
            if (!usersMap.has(row.ARCHIVE_ID)) {
                usersMap.set(row.ARCHIVE_ID, new Set());
            }
            usersMap.get(row.ARCHIVE_ID).add(userId);
        }
    }

    // Fetch all archives, to determine Cloud space ID
    let spacesMap = new Map();
    const allArchives = await sessionData.dbConnector.getArchivesfromBASArchiveIds(Array.from(usersMap.keys()));
    for (let archiveRow of allArchives) {
        let users = usersMap.get(archiveRow.BAS_ARCHIVE_ID);
        const spaceId = archiveRow.PLATFORM_SITE_ID;
        if (!spacesMap.has(spaceId)) {
            spacesMap.set(spaceId, new Set());
        }
        let allUsersForThisSpace = spacesMap.get(spaceId);
        for (let userId of users) {
            allUsersForThisSpace.add(userId);
        }
    }

    const spacesUsageStats = Array.from(spacesMap.entries()).map(([spaceId, usersSet]) => ({ spaceId: parseInt(spaceId), userIds: Array.from(usersSet)}));

    // Separate oldest sessions into expired and live
    let expiredArchiveIDs = [];
    let liveArchiveIDs = [];
    for (let row of oldestSessionsMap.values()) {
        if (row.TIMESTAMP <= thresholdForExpiredSessions) {
            expiredArchiveIDs.push(row.ARCHIVE_ID);
        } else {
            liveArchiveIDs.push(row.ARCHIVE_ID);
        }
    }

    logger.info("Number of old archives: " + expiredArchiveIDs.length);
    logger.info("Number of archives in use: " + liveArchiveIDs.length);

    metrics.addValue('active-sessions', liveArchiveIDs.length, 'Count');
    metrics.addValue('stale-sessions', expiredArchiveIDs.length, 'Count');

    return { thresholdForExpiredSessions, thresholdForSavingLiveProjects, expiredArchiveIDs, liveArchiveIDs, spacesUsageStats };
}

async function processExpiredArchives({ expiredArchiveIDs, logger, sessionData, thresholdForExpiredSessions, serverUtils: {flushToConnectorHoldingTheTruth, diskUsageTask}, clock }) {
    for (let [idx, archiveID] of expiredArchiveIDs.entries()) {
        logger.info(`processExpiredArchives: there are no more active sessions, try to sync if necessary archive ${archiveID}, ${expiredArchiveIDs.length-idx-1} more archives to go`);
        const { success, archiveSynced } = await retrySyncWithDelay({
            label: `processExpiredArchives: archive ID ${archiveID}, ${idx+1} of ${expiredArchiveIDs.length}`,
            flushToConnectorHoldingTheTruth, sessionData, archiveID, diskUsageTask, logger, clock,
        });

        // [30 Nov 2023]: we do not need to delete the BAS sessions associated with the archive. The zombie sessions will be deleted by the main gardening (after 24h of inactivity)
        //                This should avoid the need to open a new BAS session in case the client go idle
        // if (success) {
        //     try {
        //         await callWithLegacyCallback(cb => sessionData.dbConnector.deleteZombieSessionForArchiveID(archiveID, thresholdForExpiredSessions, cb));
        //     } catch (err) {
        //         logger.error(`processExpiredArchives: deleteZombieSessionForArchiveID ${archiveID}: ${err.message}`);
        //     }
        // }
        if (archiveSynced) {
            logger.info("processExpiredArchives: waiting few secs before next round");
            await clock.sleep(DELAY_BETWEEN_ARCHIVES);
        }
    }
}

async function processLiveArchives({ liveArchiveIDs, sessionData, thresholdForSavingLiveProjects, logger, serverUtils: {flushToConnectorHoldingTheTruth, diskUsageTask}, clock }) {
    let { rows } = await callWithLegacyCallback(cb => sessionData.dbConnector.filterArchiveIdsByLastUpdate(CONNECTOR_KIND, thresholdForSavingLiveProjects, liveArchiveIDs, cb));
    for (let [idx, row] of rows.entries()) {
        const archiveID = row.ARCHIVE_ID;
        logger.info(`processLiveArchives: try to sync if necessary archive ${archiveID}, ${liveArchiveIDs.length-idx-1} more archives to go`);
        const { archiveSynced } = await retrySyncWithDelay({
            label: `processLiveArchives: archive ID ${archiveID}, ${idx+1} of ${liveArchiveIDs.length}`,
            flushToConnectorHoldingTheTruth, sessionData, archiveID, diskUsageTask, logger, clock,
        });
        if (archiveSynced) {
            logger.info("processLiveArchives: waiting few secs before next round");
            await clock.sleep(DELAY_BETWEEN_ARCHIVES);
        }
    }
}

async function retrySyncWithDelay({ label, flushToConnectorHoldingTheTruth, sessionData, archiveID, diskUsageTask, logger, clock }) {
    let remainingAttempts = NUM_SYNC_ATTEMPTS;
    while (remainingAttempts > 0) {
        try {
            remainingAttempts -= 1;
            let { wasAlreadyUpdated } = await callWithLegacyCallback(cb => flushToConnectorHoldingTheTruth(logger, sessionData, archiveID, null, CONNECTOR_KIND, null, true, cb));
            return { success: true, archiveSynced: !wasAlreadyUpdated };
        } catch (err) {
            logger.warn(`${label}. Failed. RemainingAttempts: ${remainingAttempts}. Error: ${err.message}`);
            diskUsageTask(logger);
            await clock.sleep(SYNC_FAILURE_DELAY);
        }
    }
    return { success: false, archiveSynced: false };
}


export {
    cloudGardening,
};
