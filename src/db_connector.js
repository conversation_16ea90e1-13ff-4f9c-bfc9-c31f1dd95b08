import * as uuid from 'uuid';
import { DB_IDENTIFIER_REGEXP } from './mysql-driver.ts';
import { callSaneFunctionFromLegacy } from './calling-style.ts';
import { callWithLegacyCallback } from './calling-style.ts';
import Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js';

var resetDatabase = false;

/**
 * @typedef {Object} User
 * @property {string} TOKEN
 * @property {string} USERNAME
 * @property {string} USERINFO
 * @property {string} INTERNAL_ID
 * @property {string} ARCHIVE_ID
 * @property {number} STARTING_TIME
 * @property {string} PERMISSIONS
 * @property {string} PLATFORM_TOKEN
 * @property {number} TIMESTAMP
 */

/**
 * @typedef {Object} ConnectorData
 * @property {string} ID
 * @property {string} DATA
 */

/**
 * Creates an instance of DBConnectorPool.
 * 
 * @class
 * @param {Object} options - The options for the DBConnectorPool.
 * @param {JSDocTypes.MySQLDriverPool} options.mySqlDriverInstance - An instance of MySQLDriverPool.
 * @param {JSDocTypes.Logger} options.logger - The logger instance.
 * @param {JSDocTypes.Config} options.config - The configuration object.
 * @param {JSDocTypes.RedisAdapter} options.redisAdapter - An instance of RedisAdapter.
 * @param {() => void} options.callback - The callback function.
 */
var DBConnectorPool = function ({mySqlDriverInstance, logger, config, redisAdapter, callback}) {
    this.mySqlDriverInstance = mySqlDriverInstance;
    this.logger = logger.getLogger({module: 'dbConnectorPool'});
    logger = this.logger.getLogger({action: 'startup'});
    this.config = config;
    this.redisAdapter = redisAdapter;
    this.isTransitionActive = config.isTransitionActive;

    this.mySqlDriverInstance.getConnection(function(obj) {
        var connection;
        if (obj.error) {
            logger.error("Unexpected error getting connection from DB - Shutting down" + obj.error);
            throw obj.error;
        }
        connection = obj;

        var checkPermalinkInfoExistence = function() {
            // NOTE: it's ok to use the "USE" statement and string concatenation here, instead of "changeUser". The idea is to detect whether the database exists, but the "changeUser"
            // would prevent execution of subsequent queries in case the database doesn't exist, defeating the whole purpose.
            if (!config.mySQLConfig.permalinksDBName.match(DB_IDENTIFIER_REGEXP)) {
                throw new Error('Invalid permalinks database ID');
            }
            connection.query(`USE ${config.mySQLConfig.permalinksDBName}`, function (err/*, rows, fields*/) {
                if (err) {
                    if (err.code === "ER_BAD_DB_ERROR") {
                        logger.info("#DBConnector# connected, crating permalinks database");
                        setupPermalinkDB(connection, config.mySQLConfig.permalinksDBName, function(obj) {
                            if (obj.error) {
                                this.mySqlDriverInstance.releaseConnection(connection);
                                throw obj.error;
                            } else {
                                this.mySqlDriverInstance.releaseConnection(connection);
                                callback();
                            }
                        }.bind(this));
                    } else {
                        this.mySqlDriverInstance.releaseConnection(connection);
                        throw obj.error;
                    }
                } else {
                    this.mySqlDriverInstance.releaseConnection(connection);
                    logger.info("#DBConnector# permalinks database exists");
                    callback();
                }
            }.bind(this));
        }.bind(this);

        var checkBASExistence = function() {
            connection.query(`USE ${config.mySQLConfig.basDBName}`, function (err/*, rows, fields*/) {
                if (err) {
                    if (err.code === "ER_BAD_DB_ERROR") {
                        logger.info("#DBConnector# connected, creating BAS database");
                            setupDBs(connection, config, function(obj) {
                                if (obj.error) {
                                    this.mySqlDriverInstance.releaseConnection(connection);
                                    throw obj.error;
                                } else {
                                    checkPermalinkInfoExistence();
                                }
                            }.bind(this));
                    } else {
                        this.mySqlDriverInstance.releaseConnection(connection);
                        throw obj.error;
                    }
                } else {
                    logger.info("#DBConnector# BAS database exists");
                    checkPermalinkInfoExistence();
                }
            }.bind(this));
        }.bind(this);

        if (resetDatabase) {
            deleteAllProjects(connection, logger, this.config.archiveIDPrefix, this.config.mySQLConfig.basDBName, function () {
                checkBASExistence();
            }.bind(this));
        } else {
            checkBASExistence();
        }
    }.bind(this));
};

function setupDBs(connection, config, callback) {
    const basDBName = config.mySQLConfig.basDBName;

    function initConnectorData(connection, callback) {
        var connector_id = config.connectorData.kind + "_" + config.connectorData.id;
        var data_string = JSON.stringify(config.connectorData.data);
        var values = [connector_id, data_string, data_string];
        connection.query(`INSERT INTO ${basDBName}.CONNECTOR_DATA (ID, DATA) VALUES (?, ?) ON DUPLICATE KEY UPDATE DATA=?`, values, function (err/*, rows, fields*/) {
            if (err) {
                callback && callback({error: err});
            } else {
                callback && callback({});
            }
        });
    }

    function createConnectorTable(connection, callback) {
        connection.query('CREATE TABLE CONNECTOR_DATA (ID VARCHAR(255) PRIMARY KEY NOT NULL UNIQUE, DATA TEXT)', function (err/*, rows, fields*/) {
            if (err) {
                callback && callback({error: err});
            } else if (config.connectorData) {
                initConnectorData(connection, callback);
            } else {
                callback && callback({});
            }
        });
    }

    function createPlatformInfoTable(connection, callback) {
        connection.query('CREATE TABLE PLATFORM_INFO (BAS_ARCHIVE_ID VARCHAR(255), PLATFORM_SITE_ID VARCHAR(255), PLATFORM_ARCHIVE_ID VARCHAR(255), PLATFORM_KIND TEXT, PLATFORM_ARCHIVE_NAME TEXT, TIMESTAMP BIGINT, PLATFORM_INFO TEXT, WARNING_FLAG TINYINT(1), UNIQUE (PLATFORM_SITE_ID, PLATFORM_ARCHIVE_ID), PRIMARY KEY (BAS_ARCHIVE_ID))', function (err/*, rows, fields*/) {
            if (err) {
                callback && callback({error: err});
            } else {
                createConnectorTable(connection, callback);
            }
        });
    }

    function createUsersTable(connection, callback) {
        // TODO: delete USERNAME column?
        connection.query('CREATE TABLE USERS (TOKEN VARCHAR(255) PRIMARY KEY NOT NULL UNIQUE, USERNAME TEXT, USERINFO TEXT, INTERNAL_ID TEXT, ARCHIVE_ID TEXT, STARTING_TIME BIGINT, PERMISSIONS TEXT, PLATFORM_TOKEN TEXT, TIMESTAMP BIGINT)', function (err/*, rows, fields*/) {
            if (err) {
                callback && callback({error: err});
            } else {
                createPlatformInfoTable(connection, callback);
            }
        });
    }

    connection.query(`CREATE DATABASE IF NOT EXISTS ${basDBName} CHARACTER SET utf8 COLLATE utf8_general_ci`, function (err/*, rows, fields*/) {
        if (err) {
            callback && callback({error: err});
        } else {
            connection.query(`USE ${basDBName}`, function (err/*, rows, fields*/) {
                if (err) {
                    callback && callback({error: err});
                } else {
                    createUsersTable(connection, callback);
                }
            });
        }
    });
}

// DBConnectorPool.prototype.deleteAllProjects = function (callback) {
//     this.mySqlDriverInstance.getConnection((connection) => {
//         deleteAllProjects(connection, this.logger, this.config.archiveIDPrefix, this.config.mySQLConfig.basDBName, () => {
//             connection.release();
//             callback()
//         });
//     });
// };

function watchdog(contentData, callback)
{
    var i, key,
        keys = Object.keys(contentData),
        completed = 0, error = null;
    for (i=0; i<keys.length; i++)
    {
        key = keys[i];
        if (contentData[key].error) {
            error = contentData[key].error;
            break;
        } else if (contentData[key].status == "onprogress") {
            break;
        } else {
            // success
            completed++;
        }
    }

    if (completed === keys.length)
    {
        callback({status: "completed"});
    } else if (error) {
        callback({error: error});
    } else {
        // retry later
        setTimeout(function() {
            watchdog(contentData, callback);
        }, 200);
    }
}

function deleteAllProjects(connection, logger, archiveIDPrefix, basDBName, callback) {
    var contentData = {};
    connection.query(`DELETE FROM ${basDBName}.PLATFORM_INFO`, function (/*err, rows*/) {
        connection.query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA", function (err, rows/*, fields*/) {
            if (err) {
                callback && callback({error: err});
            } else {
                for (var id in rows) {
                    if (rows.hasOwnProperty(id))
                    {
                        var obj = rows[id];
                        if (obj.SCHEMA_NAME == "information_schema"
                            || obj.SCHEMA_NAME == "mysql"
                            || obj.SCHEMA_NAME == "innodb"
                            || obj.SCHEMA_NAME == "performance_schema"
                            || obj.SCHEMA_NAME == "tmp"
                            || obj.SCHEMA_NAME.indexOf("mybalsamiq_") === 0
                            || !obj.SCHEMA_NAME.startsWith(archiveIDPrefix)) {
                            continue;
                        }

                        if (!obj.SCHEMA_NAME.match(DB_IDENTIFIER_REGEXP)) {
                            contentData[id] = {status: "error", error: new Error('Invalid database name')};
                            continue;
                        }

                        contentData[id] = {status: "onprogress"};

                        (function(obj, id) {
                            logger = logger.getLogger({action: 'ops'});
                            logger.info("#DBConnector# dropping database " + obj.SCHEMA_NAME);
                            connection.query("DROP DATABASE " + obj.SCHEMA_NAME, function(err) {
                                if (err) {
                                    contentData[id] = {status: "error", error: err};
                                } else {
                                    contentData[id] = {status: "success"};
                                }
                            });
                        })(obj, id);
                    }
                }
                watchdog(contentData, callback);
            }
        });
    });
}

/**
 *
 * @param {JSDocTypes.Logger} logger
 * @param {Omit<JSDocTypes.SessionData, 'dbConnector'>} sessionData
 * @param {null | 'READ'} lockType
 * @param {JSDocTypes.BASLegacyCallback<DBConnector>} callback
 */
DBConnectorPool.prototype.getDBConnector = function(logger, sessionData, lockType, callback) {
    var dbConnector;
    const { redisAdapter, config } = this;
    sessionData.connection.query(`USE ${this.config.mySQLConfig.basDBName}`, function (err/*, rows, fields*/) {
        // test if the connection is functioning
        // TODO: remove the test on the connection?
        if (err) {
            callback({error: err});
        } else {
            dbConnector = new DBConnector(sessionData, logger, config, redisAdapter);
            if (lockType)
            {
                dbConnector.lock(lockType, function(obj) {
                    if (obj.error) {
                        callback(obj);
                    } else {
                        callback(dbConnector);
                    }
                });
            } else {
                callback(dbConnector);
            }
        }
    }.bind(this));
};

/**
 * @class
 * @param {JSDocTypes.SessionData} sessionData 
 * @param {JSDocTypes.Logger} logger 
 * @param {JSDocTypes.Config} config 
 * @param {JSDocTypes.RedisAdapter} redisAdapter 
 */
var DBConnector = function(sessionData, logger, config, redisAdapter) {
    this.sessionData = sessionData;
    this.connection = this.sessionData.connection;
    this.logger = logger;
    this.basDBName = config.mySQLConfig.basDBName;
    this.redisAdapter = redisAdapter;
    this.permalinksDBName = config.mySQLConfig.permalinksDBName;
    this.archiveIDPrefix = config.archiveIDPrefix;
};


DBConnector.prototype.pauseSession = function(callback) {
    var sessionManager = this.sessionData.sessionManager;
    var dbConnector = this;
    var sessionData = this.sessionData;
    sessionManager.releaseSession(this.sessionData, function(obj) {
        if (obj.error) {
            callback(obj);
        } else {
            callback({
                resumeSession: function(action, resumeCallback) {
                    sessionManager.resumeSession(action, sessionData, function(obj) {
                        if (obj.error) {
                            resumeCallback(obj);
                        } else {
                            dbConnector.connection = obj.connection;
                            resumeCallback({});
                        }
                    });
                }
            });
        }
    });
};

/**
 * @param {JSDocTypes.BASLegacyCallback<{}>} callback
 */
DBConnector.prototype.releaseSession = function(callback) {
    // nothing to do UNLOCK for all the tables is done by the sessionManager
    callback({});
};

DBConnector.prototype.unlock = function(callback) {
    if (this.isTransitionActive) {
        try {
            this.connection.query('UNLOCK TABLES', function (err /*, rows, fields*/) {
                if (err) {
                    //console.log("UNLOCK ALL <<< ERR " + err);
                    callback && callback({error: err});
                } else {
                    //console.log("UNLOCK ALL <<<");
                    callback && callback({});
                }
            });
        } catch (e)
        {
            callback && callback({error: 'Unexpected exception :' + e.message});
        }
    } else {
        callback && callback({});
    }
};


DBConnector.prototype.lock = function(lockType, callback) {
    var query;
    if (this.isTransitionActive) {
        if (lockType === 'READ')
        {
            query = `LOCK TABLES ${this.basDBName}.CONNECTOR_DATA READ, ${this.basDBName}.USERS READ, ${this.basDBName}.PLATFORM_INFO READ`;
        } else
        {
            query = `LOCK TABLES ${this.basDBName}.CONNECTOR_DATA WRITE, ${this.basDBName}.USERS WRITE, ${this.basDBName}.PLATFORM_INFO WRITE`;
        }

        try {
            this.connection.query(query, function (err /*, rows, fields*/) {
                if (err) {
                    //console.log("LOCK <<<" + lockType + " ERR " + err);
                    callback && callback({error: err});
                } else {
                    //console.log("LOCK <<<" + lockType);
                    callback && callback({});
                }
            });
        } catch (e)
        {
            callback && callback({error: 'Unexpected exception :' + e.message});
        }
    } else {
        callback && callback({});
    }
};

DBConnector.prototype.dropArchive = function(archiveID, callback) {
    let logger = this.logger.getLogger({action:"dropArchive", module: "gar"})
    var ret;
    if (!archiveID.match(DB_IDENTIFIER_REGEXP)) {
        callback({error: new Error('Invalid archive ID')});
        return;
    }
    this.connection.query("DROP DATABASE " + archiveID, function(err) {
        if (err) {
            logger.error("drop archive failed " + archiveID + " " + err, err, );
            ret = {error: err};
        } else {
            logger.info("dropped archive " + archiveID);
            ret = {};
        }
        callback(ret);
    });
};

DBConnector.prototype.dropZombieArchive = function(callback) {
    var contentData = {}, archiveID;
    let logger = this.logger.getLogger({action:"dropZombieArchive", module: "gar"});
    this.connection.query(`SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA LEFT OUTER JOIN ${this.basDBName}.PLATFORM_INFO ON INFORMATION_SCHEMA.SCHEMATA.SCHEMA_NAME = ${this.basDBName}.PLATFORM_INFO.BAS_ARCHIVE_ID WHERE ${this.basDBName}.PLATFORM_INFO.BAS_ARCHIVE_ID IS NULL`, function (err, rows/*, fields*/) {
        if (err) {
            logger.error("select failed " + err);
        } else {
            // delay the deletion of selected archives in order to avoid race condition
            setTimeout(function() {
                for (var keys in rows) {
                    if (rows.hasOwnProperty(keys)) {
                        var obj = rows[keys];

                        if (obj.SCHEMA_NAME.startsWith("wd_") ||
                            obj.SCHEMA_NAME.startsWith("gd_") ||
                            obj.SCHEMA_NAME.startsWith("jira_") ||
                            obj.SCHEMA_NAME.startsWith("cloud_") ||
                            obj.SCHEMA_NAME.startsWith("confluence_") ||
                            obj.SCHEMA_NAME.startsWith("fs_"))
                        {
                            archiveID = obj.SCHEMA_NAME;

                            if (!archiveID.match(DB_IDENTIFIER_REGEXP)) {
                                contentData[archiveID] = {status: "error", error: new Error('Invalid archive ID')};
                                continue;
                            }

                            contentData[archiveID] = {status: "onprogress"};

                            (function (archiveID, thisObj) {
                                thisObj.getPlatformData(archiveID, function (obj) {
                                    if (obj.error) {
                                        contentData[archiveID] = {status: "error", error: err};
                                    } else if (obj.BAS_ARCHIVE_ID) {
                                        // archive exists, avoid the race condition
                                        logger.info("archive exists, avoid race condition " + archiveID);
                                        contentData[archiveID] = {status: "success"};
                                    } else {
                                        thisObj.connection.query("DROP DATABASE " + archiveID, function (err) {
                                            if (err) {
                                                contentData[archiveID] = {status: "error", error: err};
                                                logger.warn("drop database failed " + err);
                                            } else {
                                                contentData[archiveID] = {status: "success"};
                                                logger.info("dropped zombie archive " + archiveID);
                                            }
                                        });
                                    }
                                });
                            })(archiveID, this);
                        }
                    }
                }
                watchdog(contentData, callback);
            }.bind(this), 30000);
        }
    }.bind(this));
};


DBConnector.prototype.deleteSessionZombieEntries = function(callback) {
    let logger = this.logger.getLogger({action:"deleteSessionZombieEntries", module: "gar"});
    const basDBName = this.basDBName;
    this.connection.query(`SELECT ${basDBName}.USERS.INTERNAL_ID FROM ${basDBName}.USERS LEFT OUTER JOIN ${basDBName}.PLATFORM_INFO ON ${basDBName}.USERS.ARCHIVE_ID = ${basDBName}.PLATFORM_INFO.BAS_ARCHIVE_ID WHERE ${basDBName}.PLATFORM_INFO.BAS_ARCHIVE_ID IS NULL`, function (err, rows /*, fields*/) {
        if (err) {
            logger.error("select failed  " + err, err);
            callback(err);
        } else {
            if (rows.length) {
                var values = rows.map(function (row) { return row.INTERNAL_ID; });

                this.connection.query(`DELETE FROM ${basDBName}.USERS WHERE INTERNAL_ID IN (${makeSqlPlaceholdersForArray(values)})`, values, function (err, rows /*, fields*/) {
                    if (err) {
                        logger.error("delete failed " + err, err);
                        callback({});
                    } else {
                        if (rows.affectedRows) {
                            logger.info("deleted " + rows.affectedRows + " session zombies entries");
                        }
                        callback({});
                    }
                }.bind(this));
            } else {
                logger.info("no session zombies entries to be deleted");
                callback({});
            }
        }
    }.bind(this));
};

DBConnector.prototype.deleteArchiveZombieEntries = function(callback) {
    let logger = this.logger.getLogger({action:"deleteArchiveZombieEntries", module: "gar"});
    var date = new Date();
    const basDBName = this.basDBName;

    if (date.getDate() === 0 && date.getHours() < 13) {
        // run the job on Sunday
        this.connection.query(`SELECT ${basDBName}.PLATFORM_INFO.BAS_ARCHIVE_ID FROM ${basDBName}.PLATFORM_INFO LEFT OUTER JOIN INFORMATION_SCHEMA.SCHEMATA ON ${basDBName}.PLATFORM_INFO.BAS_ARCHIVE_ID = INFORMATION_SCHEMA.SCHEMATA.SCHEMA_NAME WHERE INFORMATION_SCHEMA.SCHEMATA.SCHEMA_NAME IS NULL`, function (err, rows /*, fields*/) {
            if (err) {
                logger.error("select failed  " + err, err);
                callback(err);
            } else {
                if (rows.length) {
                    var values = rows.map(function (row) { return row.BAS_ARCHIVE_ID; });
                    this.connection.query(`DELETE FROM ${basDBName}.PLATFORM_INFO WHERE BAS_ARCHIVE_ID IN (${makeSqlPlaceholdersForArray(values)})`, values, function (err, rows /*, fields*/) {
                        if (err) {
                            logger.error("delete failed: " + err, err);
                            callback(err);
                        } else {
                            logger.info("deleted " + rows.affectedRows + " archive zombies entries");
                            callback({});
                        }
                    }.bind(this));
                } else {
                    logger.info("no archive zombies entries to be deleted");
                    callback({});
                }
            }
        }.bind(this));
    } else {
        // skip the job on other days
        logger.info("skip job, will execute next Sunday morning");
        callback({});
    }
};

DBConnector.prototype.deleteZombieSession = function(timestamp, callback) {
    let logger = this.logger.getLogger({action:"deleteZombieSession", module: "gar"});
    this.connection.query(`SELECT TOKEN FROM ${this.basDBName}.USERS WHERE TIMESTAMP < ?`, [timestamp], function (err, rows /*, fields*/) {
        if (err) {
            logger.error("select zombie sessions failed: " + err, err);
        } else {
            if (rows.length) {
                let concatenatedString = rows.map(obj => obj.TOKEN).join(' ');
                logger.info("deleted zombie sessions in users database: " + concatenatedString);
            }
        }

        this.connection.query(`DELETE FROM ${this.basDBName}.USERS WHERE TIMESTAMP < ?`, [timestamp], function (err, rows /*, fields*/) {
            if (err) {
                logger.error("delete failed: " + err, err);
                callback(err);
            } else {
                if (rows.affectedRows) {
                    logger.info("deleted " + rows.affectedRows + " zombie sessions in users database");
                }
                callback({});
            }
        }.bind(this));
    }.bind(this));
};

DBConnector.prototype.deleteZombieSessionForArchiveID = function(archiveID, timestamp, callback) {
    let logger = this.logger.getLogger({action:"deleteZombieSessionForArchiveID", module: "gar"});
    this.connection.query(`DELETE FROM ${this.basDBName}.USERS WHERE TIMESTAMP <= ? AND ARCHIVE_ID = ?`, [timestamp, archiveID], function (err, rows /*, fields*/) {
        if (err) {
            logger.error("delete failed: " + err, err);
            callback(err);
        } else {
            if (rows.affectedRows) {
                logger.info("deleted " + rows.affectedRows + " zombie sessions in users database for archiveID " + archiveID);
            }
            callback({affectedRows: rows.affectedRows});
        }
    }.bind(this));
};

function setupPermalinkDB(connection, permalinksDBName, callback) {
    function createPermalinkInfoTable(connection, callback) {
        connection.query('CREATE TABLE PERMALINK_INFO (PERMALINK_ID VARCHAR(255), PLATFORM_SITE_ID VARCHAR(255), PLATFORM_ARCHIVE_ID VARCHAR(255), PLATFORM_KIND TEXT, RESOURCE_ID VARCHAR(255), BRANCH_ID VARCHAR(255), PERMALINK_INFO TEXT, PLATFORM_INFO TEXT, DIRTY BOOL DEFAULT FALSE, PERMALINK_KIND ENUM(\'image\', \'public_share\', \'image_unfurling\', \'url_unfurling\') NOT NULL DEFAULT \'image\', TIMESTAMP BIGINT, UNIQUE (PLATFORM_SITE_ID, PLATFORM_ARCHIVE_ID, RESOURCE_ID, BRANCH_ID, PERMALINK_KIND), PRIMARY KEY (PERMALINK_ID))', function (err/*, rows, fields*/) {
            var ret = {};
            if (err) {
                ret = {error: err};
            }
            callback && callback(ret);
        });
    }

    if (!permalinksDBName.match(DB_IDENTIFIER_REGEXP)) {
        throw new Error('Invalid permalinks database ID');
    }

    connection.query(`CREATE DATABASE IF NOT EXISTS ${permalinksDBName} CHARACTER SET utf8 COLLATE utf8_general_ci`, function (err/*, rows, fields*/) {
        if (err) {
            callback && callback({error: err});
        } else {
            connection.query(`USE ${permalinksDBName}`, function (err/*, rows, fields*/) {
                if (err) {
                    callback && callback({error: err});
                } else {
                    createPermalinkInfoTable(connection, callback);
                }
            });
        }
    });
}

DBConnector.prototype.makePermalinkFromDBRow = function (row) {
    return {
        permalinkID: row.PERMALINK_ID,
        platformKind: row.PLATFORM_KIND,
        platformArchiveID: row.PLATFORM_ARCHIVE_ID,
        platformSiteID: row.PLATFORM_SITE_ID,
        resourceID: row.RESOURCE_ID,
        branchID: row.BRANCH_ID,
        timestamp: row.TIMESTAMP,
        dirty: !!row.DIRTY,
        permalinkKind: row.PERMALINK_KIND,
        permalinkInfo: JSON.parse(row.PERMALINK_INFO),
        platformInfo: JSON.parse(row.PLATFORM_INFO)
    };
};

DBConnector.prototype.savePermalinkData = function ({permalinkID, platformKind, platformSiteID, platformArchiveID, resourceID, branchID, dirty, permalinkKind, permalinkInfo, platformInfo, timestamp}, callback) {
    var values = [
        permalinkID,
        platformKind,
        platformSiteID,
        platformArchiveID,
        resourceID,
        branchID,
        timestamp,
        dirty,
        permalinkKind,
        permalinkInfo ? JSON.stringify(permalinkInfo) : null,
        platformInfo ? JSON.stringify(platformInfo) : null
    ];

    if (!this.permalinksDBName.match(DB_IDENTIFIER_REGEXP)) {
        throw new Error('Invalid permalinks database ID');
    }

    this.connection.query(`INSERT INTO ${this.permalinksDBName}.PERMALINK_INFO (PERMALINK_ID, PLATFORM_KIND, PLATFORM_SITE_ID, PLATFORM_ARCHIVE_ID, RESOURCE_ID, BRANCH_ID, TIMESTAMP, DIRTY, PERMALINK_KIND, PERMALINK_INFO, PLATFORM_INFO) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, values, function (err/*, rows, fields*/) {
        var message;
        var resp = {};
        if (err) {
            message = err.message ? err.message : JSON.stringify(err);
            resp = {error: message};
            if (err.code == "ER_DUP_ENTRY")
            {
                // there is a concurrent request to load an archive
                resp.busy = true;
            }
            callback && callback(resp);
        }
        else {
            callback && callback({});
        }
    });
};

DBConnector.prototype.insertOrUpdatePermalink = async function ({permalinkID, platformKind, platformSiteID, platformArchiveID, resourceID, branchID, dirty, permalinkKind, permalinkInfo, platformInfo, timestamp}) {
    permalinkInfo = permalinkInfo ? JSON.stringify(permalinkInfo) : null;
    platformInfo = platformInfo ? JSON.stringify(platformInfo) : null;
    const values = [
        permalinkID,
        platformKind,
        platformSiteID,
        platformArchiveID,
        resourceID,
        branchID,
        timestamp,
        dirty,
        permalinkKind,
        permalinkInfo,
        platformInfo,
        // on update
        timestamp,
        dirty,
        platformInfo,
    ];
    //NB - IMPORTANT: on update the permalinkInfo doesn't have to be changed because contains permalink image properties
    let insertOrUpdateResult = await this.runQuery(`INSERT INTO ${this.permalinksDBName}.PERMALINK_INFO
        (PERMALINK_ID, PLATFORM_KIND, PLATFORM_SITE_ID, PLATFORM_ARCHIVE_ID, RESOURCE_ID, BRANCH_ID, TIMESTAMP, DIRTY, PERMALINK_KIND, PERMALINK_INFO, PLATFORM_INFO)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
            TIMESTAMP = ?,
            DIRTY = ?,
            PLATFORM_INFO = ?
        `,
        values
    );

    let selectResults = await this.runQuery(`SELECT * FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE
        PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? AND RESOURCE_ID = ? AND BRANCH_ID = ? AND PERMALINK_KIND = ?`,
        [platformKind, platformSiteID, platformArchiveID, resourceID, branchID, permalinkKind]
    );

    return [ this.makePermalinkFromDBRow(selectResults[0]), insertOrUpdateResult.affectedRows ];  // 1 = insert, 2 = updated, 0 = unchanged (updated with same values)
};

DBConnector.prototype.markPermalinkAsOutdated = async function ({basArchiveID, resourceID, branchID}) {
    const platformInfo = await this.getArchiveIdentifiersByArchiveID(basArchiveID);
    if (!platformInfo) {
        throw new Error(`Archive not found with ID ${basArchiveID}`);
    }
    const platformArchiveId = platformInfo.PLATFORM_ARCHIVE_ID;
    const platformSiteId = platformInfo.PLATFORM_SITE_ID;
    const platformKind = platformInfo.PLATFORM_KIND;

    let res = await this.runQuery(
        `UPDATE ${this.permalinksDBName}.PERMALINK_INFO SET DIRTY = true WHERE PLATFORM_ARCHIVE_ID = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_KIND = ? AND RESOURCE_ID = ? AND BRANCH_ID = ?`,
        [platformArchiveId, platformSiteId, platformKind, resourceID, branchID]
    );
    return res.affectedRows;
};

DBConnector.prototype.markPermalinkByPermalinkIDs = async function ({platformKind, platformSiteID, platformArchiveID, permalinkIDs, dirty, timestamp}) {
    let res = await this.runQuery(
        `UPDATE ${this.permalinksDBName}.PERMALINK_INFO SET DIRTY = ?, TIMESTAMP = ? WHERE PLATFORM_ARCHIVE_ID = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_KIND = ? AND PERMALINK_ID IN (${makeSqlPlaceholdersForArray(permalinkIDs)})`,
        [dirty, timestamp, platformArchiveID, platformSiteID, platformKind, ...permalinkIDs]
    );
    return res.affectedRows;
};

DBConnector.prototype.getLockedQueries = async function () {
    let query, mySqlVersion, ret;

    try {
        ret = await this.runQuery("SELECT VERSION()");
        mySqlVersion = ret["0"]["VERSION()"];

        if (mySqlVersion.startsWith("5.7")) {
            query = `
            SELECT
              r.trx_id waiting_trx_id,
              r.trx_mysql_thread_id waiting_thread,
              r.trx_query waiting_query,
              b.trx_id blocking_trx_id,
              b.trx_mysql_thread_id blocking_thread,
              b.trx_query blocking_query
            FROM       information_schema.innodb_lock_waits w
            INNER JOIN information_schema.innodb_trx b
              ON b.trx_id = w.blocking_trx_id
            INNER JOIN information_schema.innodb_trx r
              ON r.trx_id = w.requesting_trx_id`;
        } else if (mySqlVersion.startsWith("8.")) {
            query = `
            SELECT
                r.trx_id waiting_trx_id,
                r.trx_mysql_thread_id waiting_thread,
                r.trx_query waiting_query,
                b.trx_id blocking_trx_id,
                b.trx_mysql_thread_id blocking_thread,
                b.trx_query blocking_query
            FROM       performance_schema.data_lock_waits w
            INNER JOIN information_schema.innodb_trx b
            ON b.trx_id = w.blocking_engine_transaction_id
            INNER JOIN information_schema.innodb_trx r
            ON r.trx_id = w.requesting_engine_transaction_id`;
        } else {
            // unknown MySql version
            return [];
        }
        return await this.runQuery(query);
    } catch (e) {
        return [];
    }
};

DBConnector.prototype.markPermalinksAsOutdated = async function ({platformKind, platformSiteID, platformArchiveID}) {
    let res = await this.runQuery(
        `UPDATE ${this.permalinksDBName}.PERMALINK_INFO SET DIRTY = true WHERE
        PLATFORM_ARCHIVE_ID = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_KIND = ?`,
        [platformArchiveID, platformSiteID, platformKind]
    );
    return res.affectedRows;
};

DBConnector.prototype.getPermalinksByArchiveIDAndMinTimestamp = async function ({platformKind, platformSiteID, platformArchiveID, timestamp}) {
    let row, query, params;

    if (platformSiteID) {
        query = `SELECT * FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? AND TIMESTAMP > ?`;
        params =  [platformKind, platformSiteID, platformArchiveID, timestamp];
    } else {
        // in Cloud platformSiteID cannot be available when the archive is deleted
        query = `SELECT * FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_ARCHIVE_ID = ? AND TIMESTAMP > ?`;
        params =  [platformKind, platformArchiveID, timestamp];
    }

    let rows = await this.runQuery(query, params);

    let res = { permalinksData: [] };
    for (let i=0; i<rows.length; i++) {
        row = rows[i];
        res.permalinksData.push(
            this.makePermalinkFromDBRow(row)
        );
    }
    return res;
};

DBConnector.prototype.getPermalinkData = function (permalinkID, callback) {
    this.getPermalinkDataByID(permalinkID).then((permalinkData) => {
        if (permalinkData !== null) {
            callback && callback(permalinkData);
        } else {
            callback && callback({error: "No permalink found with ID " + permalinkID, notFound: true});
        }
    }).catch(err => {
        callback && callback({error: 'Unexpected exception :' + err.message});
    });
};


DBConnector.prototype.getPermalinkDataByID = async function (permalinkID) {
    let rows = await this.runQuery(`SELECT * FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PERMALINK_ID = ?`, [permalinkID]);
    if (rows.length === 0) {
        return null;
    }

    return this.makePermalinkFromDBRow(rows[0]);
};

DBConnector.prototype.deletePermalinkData = async function ({platformKind, platformSiteID, platformArchiveID, resourceID, branchID, permalinkKind}) {
    const _permalinkKind = permalinkKind || Consts.PermalinkKind.image;
    let res = await this.runQuery(
        `DELETE FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? AND RESOURCE_ID = ? AND BRANCH_ID = ? AND PERMALINK_KIND = ?`,
        [platformKind, platformSiteID, platformArchiveID, resourceID, branchID, _permalinkKind]
    );
    if (res.affectedRows === 0) {
        let err = new Error(`No permalink found for the requested resource`);
        err.notFound = true;
        throw err;
    }

    return {affectedRows: res.affectedRows};
};

DBConnector.prototype.deletePermalinksByPlatformKindAndPlatformArchiveIDs = async function ({platformKind, platformArchiveIDs}) {
    if (platformArchiveIDs.length === 0) {
        return {affectedRows: 0};
    }
    const results = await this.runQuery(`DELETE FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_ARCHIVE_ID IN (?)`, [platformKind, platformArchiveIDs]);
    return {
        affectedRows: results.affectedRows,
    };
};

DBConnector.prototype.getPermalinkDataByPlatformKindAndPlatformArchiveIDs = async function ({platformKind, platformArchiveIDs}) {
    if (platformArchiveIDs.length === 0) {
        return {
            permalinksData: [],
            affectedRows: 0
        };
    }
    const rows = await this.runQuery(`SELECT * FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_ARCHIVE_ID IN (?)`, [platformKind, platformArchiveIDs]);
    let res = {
        permalinksData: [],
        affectedRows: rows.length
    };
    for (let i=0; i<rows.length; i++) {
        res.permalinksData.push(
            this.makePermalinkFromDBRow(rows[i])
        );
    }
    return res;
};

DBConnector.prototype.getPermalinkIDsByPlatformKindAndPlatformArchiveIDsAndPermalinkKind = async function ({platformKind, permalinkKind, platformArchiveIDs}) {
    if (platformArchiveIDs.length === 0) {
        return {affectedRows: 0};
    }
    const rows = await this.runQuery(`SELECT PERMALINK_ID FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PERMALINK_KIND = ? AND PLATFORM_ARCHIVE_ID IN (?)`, [platformKind, permalinkKind, platformArchiveIDs]);
    return rows.map(row => row.PERMALINK_ID);
};

DBConnector.prototype.deletePermalinksByPlatformKindAndPlatformArchiveIDsAndPermalinkKind = async function ({platformKind, permalinkKind, platformArchiveIDs}) {
    if (platformArchiveIDs.length === 0) {
        return {affectedRows: 0};
    }
    const results = await this.runQuery(`DELETE FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PERMALINK_KIND = ? AND PLATFORM_ARCHIVE_ID IN (?)`, [platformKind, permalinkKind, platformArchiveIDs]);
    return {
        affectedRows: results.affectedRows,
    };
};

DBConnector.prototype.getPermalinksDataByResourceIDs = async function ({platformKind, platformSiteID, platformArchiveID, resourceIDs, branchID, permalinkKind}) {
    if (resourceIDs.length === 0) {
        return {
            permalinksData: [],
            affectedRows: 0
        };
    }
    const query = `SELECT * FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? AND RESOURCE_ID IN (${makeSqlPlaceholdersForArray(resourceIDs)}) AND BRANCH_ID = ? AND PERMALINK_KIND = ?`;
    const params = [platformKind, platformSiteID, platformArchiveID, ...resourceIDs, branchID, permalinkKind];
    const rows = await this.runQuery(query, params);
    let res = {
        permalinksData: [],
        affectedRows: rows.length
    };
    for (let i=0; i<rows.length; i++) {
        res.permalinksData.push(
            this.makePermalinkFromDBRow(rows[i])
        );
    }
    return res;
};

DBConnector.prototype.deletePermalinksByPermalinkIDs = async function ({platformKind, platformSiteID, platformArchiveID, permalinkIDs, permalinkKind}) {
    if (permalinkIDs.length === 0) {
        return {affectedRows: 0};
    }
    const query = `DELETE FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? AND PERMALINK_ID IN (${makeSqlPlaceholdersForArray(permalinkIDs)}) AND PERMALINK_KIND = ?`;
    const params = [platformKind, platformSiteID, platformArchiveID, ...permalinkIDs, permalinkKind];
    const results = await this.runQuery(query, params);
    return {
        affectedRows: results.affectedRows,
    };
};

DBConnector.prototype.deletePermalinksData = function (platformKind, platformSiteID, platformArchiveID, callback) {
    var query, params;
    try {
        if (platformSiteID) {
            query =`DELETE FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ?`;
            params = [platformKind, platformSiteID, platformArchiveID];
        } else {
            // in Cloud platformSiteID cannot be available when the archive is deleted
            query =`DELETE FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_ARCHIVE_ID = ?`;
            params = [platformKind, platformArchiveID];
        }

        this.connection.query(query, params, function (err, res/*, fields*/) {
            if (err) {
                callback && callback({error: err});
            } else if (res.affectedRows) {
                res = {
                    affectedRows: res.affectedRows
                };
                callback && callback(res);
            } else {
                callback && callback({error: "No permalinks found for the requested archive", notFound: true});
            }
        });
    } catch (e) {
        callback && callback({error: 'Unexpected exception :' + e.message});
    }
};

DBConnector.prototype.deletePermalinksByArchiveIDAndMinTimestamp = async function ({platformKind, platformSiteID, platformArchiveID, timestamp}) {
    let query, params;
    if (platformSiteID) {
        query = `DELETE FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? AND TIMESTAMP > ?`;
        params =  [platformKind, platformSiteID, platformArchiveID, timestamp];
    } else {
        // in Cloud platformSiteID cannot be available when the archive is deleted
        query = `DELETE FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_ARCHIVE_ID = ? AND TIMESTAMP > ?`;
        params =  [platformKind, platformArchiveID, timestamp];
    }
    let res = await this.runQuery(query, params);
    return res.affectedRows;
};


DBConnector.prototype.getPermalinksDataFromArchiveID = function (platformKind, platformSiteID, platformArchiveID, callback) {
    callSaneFunctionFromLegacy(
        this.getPermalinksByArchiveIDAndMinTimestamp({platformKind, platformSiteID, platformArchiveID, timestamp: 0}),
        callback
    );
};

DBConnector.prototype.getPermalinkDataFromResourceID = async function ({platformKind, platformSiteID, platformArchiveID, resourceID, branchID, permalinkKind}) {
    const _permalinkKind = permalinkKind || Consts.PermalinkKind.image;
    let rows = await this.runQuery(
        `SELECT * FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? AND RESOURCE_ID = ? AND BRANCH_ID = ?  AND PERMALINK_KIND = ?`,
        [platformKind, platformSiteID, platformArchiveID, resourceID, branchID, _permalinkKind]
    );
    if (rows.length === 0) {
        return {};
    }
    return this.makePermalinkFromDBRow(rows[0]);
};

DBConnector.prototype.hasPublicShare = async function ({platformKind, platformSiteID, platformArchiveID}) {
    let rows = await this.runQuery(
        `SELECT * FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? AND PERMALINK_KIND = ?`,
        [platformKind, platformSiteID, platformArchiveID, Consts.PermalinkKind.public_share]
    );
    return {
        hasPublicShare: (rows.length !== 0)
    };
};

DBConnector.prototype.getPermalinkDataFromPermalinkID = async function ({platformKind, platformSiteID, platformArchiveID, permalinkID}) {
    let rows = await this.runQuery(
        `SELECT * FROM ${this.permalinksDBName}.PERMALINK_INFO WHERE PLATFORM_KIND = ? AND PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? AND PERMALINK_ID = ?`,
        [platformKind, platformSiteID, platformArchiveID, permalinkID]
    );
    if (rows.length === 0) {
        return {};
    }
    return this.makePermalinkFromDBRow(rows[0]);
};

/**
 * @param {string} kind
 * @param {string} id
 * @param {JSDocTypes.BASLegacyCallback<{error: JSDocTypes.MysqlError} | Object>} [callback]
 */
DBConnector.prototype.getConnectorData = function (kind, id, callback) {
    var connector_id = kind + "_" + id;
    var res;

    try {
        this.connection.query(`SELECT DATA FROM ${this.basDBName}.CONNECTOR_DATA WHERE ID = ?`, [connector_id], function (err, rows/*, fields*/) {
            if (err) {
                callback && callback({error: err});
            }
            else if (rows && rows.length == 1) {
                res = JSON.parse(rows[0].DATA);
                callback && callback(res);
            }
            else {
                callback && callback({});
            }
        });
    } catch (e)
    {
        callback && callback({error: 'Unexpected exception :' + e.message});
    }
};

DBConnector.prototype.getAllSessionsForKind = function (kind, callback) {
    try {
        this.connection.query(`SELECT * FROM ${this.basDBName}.USERS WHERE ARCHIVE_ID LIKE ?`
        , [this.archiveIDPrefix + kind + "_%"], function (err, rows) {
            if (err) {
                callback && callback({error: err});
            }
            else if (rows) {
                callback && callback({rows:rows});
            }
            else {
                callback && callback({rows:[]});
            }
        });
    } catch (e) {
        callback && callback({error: 'Unexpected exception:' + e.message});
    }
};

DBConnector.prototype.filterArchiveIdsByLastUpdate = function (kind, timestamp, archiveIds, callback) {
    try {
        if (archiveIds.length === 0) {
            callback && callback({rows: []});
            return;
        }
        this.connection.query(`SELECT BAS_ARCHIVE_ID AS ARCHIVE_ID FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_KIND = ? AND TIMESTAMP < ? AND BAS_ARCHIVE_ID IN (?)`,
            [kind, timestamp, archiveIds], function(err, rows) {
            if (err) {
                callback && callback({error: err});
            }
            else if (rows) {
                callback && callback({rows: rows});
            } else {
                callback && callback({rows: []});
            }
        });
    } catch (e) {
        callback && callback({error: 'Unexpected exception:' + e.message});
    }
};

DBConnector.prototype.getAllArchiveID = function (kind, callback) {

    try {
        this.connection.query(`SELECT DISTINCT ARCHIVE_ID FROM ${this.basDBName}.USERS WHERE ARCHIVE_ID LIKE ?`, [this.archiveIDPrefix + kind + "_%"], function (err, rows/*, fields*/) {
            if (err) {
                callback && callback({error: err});
            }
            else if (rows) {
                callback && callback({rows:rows});
            }
            else {
                callback && callback({rows:[]});
            }
        });
    } catch (e)
    {
        callback && callback({error: 'Unexpected exception:' + e.message});
    }
};


DBConnector.prototype.saveConnectorData = function (kind, id, data, callback) {
    var connector_id = kind + "_" + id;
    var data_string = JSON.stringify(data);
    var values = [connector_id, data_string, data_string];
    this.connection.query(`INSERT INTO ${this.basDBName}.CONNECTOR_DATA (ID, DATA) VALUES (?, ?) ON DUPLICATE KEY UPDATE DATA=?`, values, function (err/*, rows, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else {
            callback && callback({});
        }
    });
};

DBConnector.prototype.deleteConnectorData = function (kind, id, callback) {
    var connector_id = kind + "_" + id;
    var values = [connector_id];
    this.connection.query(`DELETE FROM ${this.basDBName}.CONNECTOR_DATA WHERE ID=?`, values, function (err/*, rows, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else {
            callback && callback({});
        }
    });
};

/**
 * @param {string} token
 * @param {JSDocTypes.BASLegacyCallback<User>} callback
 */
DBConnector.prototype.getUser = function (token, callback) {
    if (token) {
        this.connection.query(`SELECT * FROM ${this.basDBName}.USERS WHERE TOKEN = ?`, [token], function (err, rows/*, fields*/) {
            if (err) {
                callback && callback({error: err});
            } else if (rows && rows.length === 1) {
                callback && callback(rows[0]);
            } else {
                callback && callback({error: 'Session closed', busy: true});
            }
        });
    } else {
        callback && callback({error: 'Session token is null'});
    }
};

DBConnector.prototype.saveUser = function (token, userInfo, internalUserID, archiveID, startingTime, role, platformToken, callback) {
    var userName, userInfoStr;
    try {
        userName = userInfo.name;
        userInfoStr = JSON.stringify(userInfo);
    } catch (e) {
        userName = "unknown";
        userInfoStr = "{name: 'unknown'}"
    }
    var values = [
        token,
        userName,
        userInfoStr,
        internalUserID,
        archiveID,
        startingTime,
        role,
        platformToken,
        startingTime
    ];
    this.connection.query(`INSERT INTO ${this.basDBName}.USERS (TOKEN, USERNAME, USERINFO, INTERNAL_ID, ARCHIVE_ID, STARTING_TIME, PERMISSIONS, PLATFORM_TOKEN, TIMESTAMP) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`, values, function (err/*, rows, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else {
            callback && callback({});
        }
    });
};


DBConnector.prototype.deleteSessionByToken = function (token, callback) {
    this.connection.query(`DELETE FROM ${this.basDBName}.USERS WHERE TOKEN = ?`, [token], function (err/*, rows, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else {
            callback && callback({});
        }
    });
};

//DBConnector.prototype.listUsers = function (archiveID, callback) {
//    this.connection.query('SELECT * FROM USERS WHERE ARCHIVE_ID = ?', [archiveID], function (err, rows/*, fields*/) {
//        if (err) {
//            callback && callback({error: err});
//        }
//        else {
//            callback && callback(rows);
//        }
//    });
//};

DBConnector.prototype.list = function (platformSiteID, platformArchiveID, callback) {
    var archiveID, userInfo, userName;
    const basDBName = this.basDBName;
    this.getBASArchiveID(platformSiteID, platformArchiveID, function(obj) {
       var resp;
       if (obj.error)
       {
           callback(obj);
       } else {
           archiveID = obj.BAS_ARCHIVE_ID;
           resp = {archiveID: archiveID, list: []};
           if (archiveID) {
               this.connection.query(`SELECT * FROM ${basDBName}.USERS WHERE ARCHIVE_ID = ?`, [archiveID], function (err, rows/*, fields*/) {
                   var i;
                   if (err) {
                       callback && callback({error: err});
                   }
                   else {
                       for (i=0; i<rows.length; i++)
                       {
                           try {
                               userInfo = JSON.parse(rows[i].USERINFO);
                               userName = userInfo.name;
                           } catch (e) {
                               userName = "unknown"
                           }
                           resp.list.push({username: userName, role: rows[i].PERMISSIONS});
                       }
                       callback && callback(resp);
                   }
               });
           } else
           {
               callback && callback(resp);
           }
       }
    }.bind(this));
};

DBConnector.prototype.deleteSessionsByArchiveID = function (archiveID, callback) {
    this.connection.query(`DELETE FROM ${this.basDBName}.USERS WHERE ARCHIVE_ID = ?`, [archiveID], function (err/*, rows, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else {
            callback && callback({});
        }
    });
};

DBConnector.prototype.getUsersList = function (internalIDsList, option, callback) {
    var resp = {users: []}, i, query, userInfo, map = {};
    var minTimestamp, maxTimestamp;

    if (internalIDsList.length) {
        query = `SELECT USERINFO, PERMISSIONS, TIMESTAMP FROM ${this.basDBName}.USERS WHERE INTERNAL_ID IN (${makeSqlPlaceholdersForArray(internalIDsList)})`;
        this.connection.query(query, internalIDsList, function (err, rows/*, fields*/) {
            if (err) {
                callback && callback({error: err + " (" + query + ")"});
            }
            else {
                for (i = 0; i < rows.length; i++) {
                    try {
                        userInfo = JSON.parse(rows[i].USERINFO);
                        // platform info can contain security sensible information
                        delete userInfo.platformInfo;
                    } catch (e) {
                        userInfo = {name: rows[i].USERNAME};
                    }

                    if (option && option === "all") {
                        resp.users.push({userInfo: userInfo, role: rows[i].PERMISSIONS});
                    } else {
                        if (minTimestamp === undefined || rows[i].TIMESTAMP < minTimestamp) {
                            minTimestamp = rows[i].TIMESTAMP;
                        }

                        if (maxTimestamp === undefined || maxTimestamp < rows[i].TIMESTAMP) {
                            maxTimestamp = rows[i].TIMESTAMP;
                        }

                        if (userInfo.avatarURL) {
                            if (map[userInfo.name]) {
                                // already inserted
                            } else {
                                map[userInfo.name] = userInfo;
                                resp.users.push({userInfo: userInfo, role: rows[i].PERMISSIONS});
                            }
                        }
                    }
                }

                resp.basSessionsCount = rows.length;
                resp.minTimestamp = minTimestamp;
                resp.maxTimestamp = maxTimestamp;
                callback && callback(resp);
            }
        }.bind(this));
    } else {
        callback && callback(resp);
    }
};

DBConnector.prototype.getLoadedArchiveForKind = function (warningFlag, callback) {
    var query, i, kind, res = {};

    query = 'SELECT PLATFORM_KIND, COUNT(*) AS COUNT FROM PLATFORM_INFO WHERE WARNING_FLAG = ? GROUP BY PLATFORM_KIND';
    this.connection.query(query, [warningFlag], function (err, rows/*, fields*/) {
        if (err) {
            callback && callback({error: err + " (" + query + ")"});
        } else {
            for (i=0; i<rows.length; i++) {
                kind = rows[i].PLATFORM_KIND;
                res[kind] = rows[i].COUNT;
            }
            callback && callback(res);
        }
    });
};

DBConnector.prototype.getArchiveNotSyncForKind = function (kind, callback) {
    var query = 'SELECT * FROM PLATFORM_INFO WHERE WARNING_FLAG = ? AND PLATFORM_KIND = ?';
    this.connection.query(query, [1, kind], function (err, rows/*, fields*/) {
        if (err) {
            callback && callback({error: err + " (" + query + ")"});
        } else {
            callback && callback({rows: rows});
        }
    });
};

DBConnector.prototype.getUserInfo = function (internalID, callback) {
    var query, userInfo;

    query = `SELECT USERINFO, PERMISSIONS FROM ${this.basDBName}.USERS WHERE INTERNAL_ID = ?`;
    this.connection.query(query, [internalID], function (err, rows/*, fields*/) {
        if (err) {
            callback && callback({error: err + " (" + query + ")"});
        }
        else {
            if (rows.length === 1)
            {
                try {
                    userInfo = JSON.parse(rows[0].USERINFO)
                } catch (e) {
                    userInfo = {name: rows[0].USERNAME};
                }
                callback && callback({userInfo: userInfo, role: rows[0].PERMISSIONS});
            } else {
                callback && callback({error: "Unexpected error: token is not unique"});
            }
        }
    }.bind(this));
};

DBConnector.prototype.updateUserInfo = function (internalID, userInfo, callback) {
    var query;

    query = `UPDATE ${this.basDBName}.USERS SET USERINFO = ? WHERE INTERNAL_ID = ?`;
    this.connection.query(query, [JSON.stringify(userInfo), internalID], function (err/*, rows, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else {
            callback && callback({});
        }
    }.bind(this));
};

DBConnector.prototype.deletePlatformDataOlderThen = function (kind, timestamp, callback) {
    let logger = this.logger.getLogger({action:"deletePlatformDataOlderThen", module: "gar"});
    this.connection.query(`DELETE FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_KIND = ? AND TIMESTAMP < ?`, [kind, timestamp], function (err, rows/*, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else {
            if (rows.affectedRows) {
                logger.info("deleted " + rows.affectedRows + " platform info expired entries for " + kind);
            }
            callback && callback({});
        }
    }.bind(this));
};

DBConnector.prototype.deleteUserDataOlderThen = function (kind, timestamp, callback) {
    let logger = this.logger.getLogger({action:"deleteUserDataOlderThen", module: "gar"});
    const basDBName = this.basDBName;
    this.connection.query(`SELECT ${basDBName}.USERS.INTERNAL_ID FROM ${basDBName}.USERS INNER JOIN PLATFORM_INFO ON PLATFORM_INFO.PLATFORM_ARCHIVE_ID = ${basDBName}.USERS.ARCHIVE_ID WHERE PLATFORM_INFO.PLATFORM_KIND = ? AND ${basDBName}.USERS.STARTING_TIME < ?`, [kind, timestamp], function (err, rows/*, fields*/) {
        if (err) {
            logger.error("select failed " + err);
            callback(err);
        } else {
            if (rows.length) {
                var values = rows.map(function (row) { return row.INTERNAL_ID; });

                this.connection.query(`DELETE FROM ${basDBName}.USERS WHERE INTERNAL_ID IN (${makeSqlPlaceholdersForArray(values)})`, values, function (err, rows /*, fields*/) {
                    if (err) {
                        logger.error("deleting user sessions failed" + err);
                        callback({});
                    } else {
                        if (rows.affectedRows) {
                            logger.info("deleted " + rows.affectedRows + " expired session entries for " + kind);
                        }
                        callback({});
                    }
                }.bind(this));
            } else {
                callback({});
            }
        }
    }.bind(this));
};

DBConnector.prototype.getUsersForArchive = function (archiveID, callback) {
    this.getSessionsByArchiveId(archiveID)
        .then(rows => {
            callback && callback({users: rows});
        })
        .catch(err => {
            callback && callback({error: err.message});
        });
};

DBConnector.prototype.getSessionsByArchiveId = async function (archiveId) {
    return await this.runQuery(`SELECT * FROM ${this.basDBName}.USERS WHERE ARCHIVE_ID = ?`, [archiveId]);
};


/**
 * 
 * @param {string} basArchiveID 
 * @param {string} platformKind 
 * @param {string | null} platformSiteID 
 * @param {string} platformArchiveID 
 * @param {string} platformArchiveName 
 * @param {object} platformInfo 
 * @param {JSDocTypes.BASLegacyCallback<{}>} callback 
 */
DBConnector.prototype.saveArchivePlatformData = function (basArchiveID, platformKind, platformSiteID, platformArchiveID, platformArchiveName, platformInfo, callback) {
    var timestamp = new Date().getTime();
    var warningFlag = 0;
    var values = [
        basArchiveID,
        platformKind,
        platformSiteID,
        platformArchiveID,
        platformArchiveName,
        timestamp,
        warningFlag,
        platformInfo ? JSON.stringify(platformInfo) : null
    ];

    this.connection.query(`INSERT INTO ${this.basDBName}.PLATFORM_INFO (BAS_ARCHIVE_ID, PLATFORM_KIND, PLATFORM_SITE_ID, PLATFORM_ARCHIVE_ID, PLATFORM_ARCHIVE_NAME, TIMESTAMP, WARNING_FLAG, PLATFORM_INFO) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, values, function (err/*, rows, fields*/) {
        var message;
        var resp = {};
        if (err) {
            message = err.message ? err.message : JSON.stringify(err);
            resp = {error: message};
            if (err.code === "ER_DUP_ENTRY")
            {
                // there is a concurrent request to load an archive
                resp.busy = true;
                resp.zombie_bar = [basArchiveID];
            }
            callback && callback(resp);
        }
        else {
            callback && callback({});
        }
    });
};

/**
 *
 * @param {string} basArchiveID
 * @param {string} platformKind
 * @param {string | null} platformSiteID
 * @param {string} platformArchiveID
 * @param {string} platformArchiveName
 * @param {object} platformInfo
 * @param {JSDocTypes.BASLegacyCallback<{}>} callback
 */
DBConnector.prototype.updateArchivePlatformData = function (basArchiveID, platformKind, platformSiteID, platformArchiveID, platformArchiveName, platformInfo, callback) {
    var timestamp = new Date().getTime();
    var values = [
        platformKind,
        platformSiteID,
        platformArchiveID,
        platformArchiveName,
        timestamp,
        platformInfo ? JSON.stringify(platformInfo) : null,
        basArchiveID
    ];

    this.connection.query(`UPDATE ${this.basDBName}.PLATFORM_INFO SET PLATFORM_KIND = ?, PLATFORM_SITE_ID = ?, PLATFORM_ARCHIVE_ID = ?, PLATFORM_ARCHIVE_NAME = ?, TIMESTAMP = ?, PLATFORM_INFO = ? WHERE BAS_ARCHIVE_ID = ?`, values, function (err/*, rows, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else {
            callback && callback({});
        }
    });
};

DBConnector.prototype.updateArchivePlatformInfo = function (basArchiveID, platformInfo, callback) {
    var values = [
        platformInfo ? JSON.stringify(platformInfo) : null,
        basArchiveID
    ];

    this.connection.query(`UPDATE ${this.basDBName}.PLATFORM_INFO SET PLATFORM_INFO = ? WHERE BAS_ARCHIVE_ID = ?`, values, function (err/*, rows, fields*/) {
        if (err) {
            callback && callback({error: err});
        } else {
            callback && callback({});
        }
    });
};

DBConnector.prototype.updateArchivePlatformName = function (basArchiveID, platformArchiveName, callback) {
    var timestamp = new Date().getTime();
    var values = [
        platformArchiveName,
        timestamp,
        basArchiveID
    ];

    this.connection.query(`UPDATE ${this.basDBName}.PLATFORM_INFO SET PLATFORM_ARCHIVE_NAME = ?, TIMESTAMP = ? WHERE BAS_ARCHIVE_ID = ?`, values, function (err/*, rows, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else {
            callback && callback({});
        }
    });
};

DBConnector.prototype.updateArchiveWarningFlag = function (basArchiveID, warningFlag, callback) {
    var values = [
        warningFlag,
        basArchiveID
    ];

    this.connection.query(`UPDATE ${this.basDBName}.PLATFORM_INFO SET WARNING_FLAG = ? WHERE BAS_ARCHIVE_ID = ?`, values, function (err/*, rows, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else {
            callback && callback({});
        }
    });
};

DBConnector.prototype.getArchiveIdentifiersByArchiveID = async function (basArchiveID) {
    let platformData = await this.redisAdapter.getPlatformDataByArchiveID(basArchiveID);
    if (!platformData) {
        let rows = await this.runQuery(`SELECT BAS_ARCHIVE_ID, PLATFORM_SITE_ID, PLATFORM_ARCHIVE_ID, PLATFORM_KIND FROM ${this.basDBName}.PLATFORM_INFO WHERE BAS_ARCHIVE_ID = ?`, [basArchiveID]);
        if (rows.length > 1) {
            throw new Error(`Multiple rows for BAS_ARCHIVE_ID = ${basArchiveID}`);
        }
        platformData = (rows.length === 1) ? rows[0] : null;
        if (platformData) {
            await this.redisAdapter.setPlatformData(platformData);
        }
    }
    return platformData;
};

DBConnector.prototype.getPlatformData = function (basArchiveID, callback) {
    this.connection.query(`SELECT * FROM ${this.basDBName}.PLATFORM_INFO WHERE BAS_ARCHIVE_ID = ?`, [basArchiveID], function (err, rows/*, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else if (rows && rows.length === 0) {
            callback && callback({});   //not found
        }
        else if (rows && rows.length !== 1) {
            callback && callback({error: "Not unique"});
        }
        else {
            callback && callback(rows[0]);
        }
    });
};

DBConnector.prototype.getPlatformInfoFromPlatformArchiveID = function (partOfplatformSiteID, platformArchiveID, callback) {
    var key = "%" + partOfplatformSiteID + "%";

    this.connection.query(`SELECT PLATFORM_INFO FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_ARCHIVE_ID = ? AND PLATFORM_SITE_ID LIKE ?`, [platformArchiveID, key], function (err, rows/*, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else if (rows && rows.length === 0) {
            callback && callback({error: "Not found"});
        }
        else if (rows && rows.length !== 1) {
            callback && callback({error: "Not unique"});
        }
        else {
            callback && callback(rows[0]);
        }
    });
};

// in some connector, the archive id is sufficient to uniquely select a project
DBConnector.prototype.getPlatformInfoByPlatformArchiveID = function (platformArchiveID, callback) {
    this.connection.query(`SELECT * FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_ARCHIVE_ID = ?`, [platformArchiveID], function (err, rows/*, fields*/) {
        if (err) {
            callback && callback({error: err});
        } else if (rows && rows.length === 0) {
            callback && callback({});   //not found
        } else if (rows && rows.length !== 1) {
            callback && callback({error: "Unexpected error, IDs are tot unique"});
        } else {
            callback && callback(rows[0]);
        }
    });
};

// in some connector, the archive id is sufficient to uniquely select a project
DBConnector.prototype.getPlatformInfoByIDs = function (platformArchiveID, platformSiteID, callback) {
    this.connection.query(`SELECT * FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_ARCHIVE_ID = ? AND PLATFORM_SITE_ID = ?`, [platformArchiveID, platformSiteID], function (err, rows/*, fields*/) {
        if (err) {
            callback && callback({error: err});
        } else if (rows && rows.length === 0) {
            callback && callback({});   //not found
        } else if (rows && rows.length !== 1) {
            callback && callback({error: "Unexpected error, IDs are tot unique"});
        } else {
            callback && callback(rows[0]);
        }
    });
};

DBConnector.prototype.getPlatformInfoByPlatformSiteID = function (platformSiteID, callback) {
    this.connection.query(`SELECT * FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_SITE_ID = ?`, [platformSiteID], function (err, rows/*, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else {
            callback && callback({rows: rows});
        }
    });
};

DBConnector.prototype.beginTransaction = function (callback) {
    this.connection.query('BEGIN', function (err) {
        if (err) {
            callback({error: err});
            return;
        }
        callback({});
    }.bind(this));
};

DBConnector.prototype.closeTransaction = function (commit, callback) {
    this.connection.query(commit ? 'COMMIT' : 'ROLLBACK', function (err) {
        if (err) {
            callback({error: err});
        } else {
            callback({});
        }
    });
};

DBConnector.prototype.withTransaction = async function (fn) {
    await callWithLegacyCallback(cb => this.beginTransaction(cb));
    const _closeTransaction = async (commit) => await callWithLegacyCallback(cb => this.closeTransaction(commit, cb));
    try {
        const result = await fn();
        await _closeTransaction(true); // COMMIT
        return result;
    } finally {
        try {
            await _closeTransaction(false);  // ROLLBACK
        } catch (err) {
            // Ignore error on rollback
        }
    }
}

DBConnector.prototype.getBASArchiveIDWithExclusiveRowLock = function (platformSiteID, platformArchiveID, withTransaction, callback) {
    var selectQuery = function (cb) {
        this.connection.query(`SELECT * FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ? FOR UPDATE`, [platformSiteID, platformArchiveID], callbackForGetBASArchiveID(function (obj) {
            if (obj.error) {
                cb(obj);
                return;
            }
            cb(obj);
        }.bind(this)));
    }.bind(this);
    if (withTransaction) {
        this.beginTransaction(function (result) {
            if (result.error) {
                return result;
            }
            selectQuery(function (resultOfTheSelect) {
                if (resultOfTheSelect.error) {
                    return resultOfTheSelect;
                }
                this.closeTransaction(true, function (result) {
                    if (result.error) {
                        return result;
                    }
                    callback(resultOfTheSelect);
                });
            }.bind(this));
        }.bind(this));
    } else {
        selectQuery(callback);
    }
};

DBConnector.prototype.getBASArchiveID = function (platformSiteID, platformArchiveID, callback) {
    this.connection.query(`SELECT * FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ?`, [platformSiteID, platformArchiveID], callbackForGetBASArchiveID(callback));
};

DBConnector.prototype.replaceArchiveID = async function (oldArchiveId, newArchiveID) {
    return await this.runQuery(`UPDATE ${this.basDBName}.PLATFORM_INFO SET BAS_ARCHIVE_ID = ? WHERE BAS_ARCHIVE_ID = ?`, [newArchiveID, oldArchiveId]);
};

function callbackForGetBASArchiveID(callback) {
    return function (err, rows/*, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else if (rows && rows.length === 0) {
            callback && callback({});   //not found
        }
        else if (rows && rows.length !== 1) {
            callback && callback({error: "Not unique"});
        }
        else {
            callback && callback(rows[0]);
        }
    };
}

DBConnector.prototype.deleteArchivePlatformData = function (basArchiveID, callback) {
    this.connection.query(`DELETE FROM ${this.basDBName}.PLATFORM_INFO WHERE BAS_ARCHIVE_ID = ?`, [basArchiveID], function (err/*, rows, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else {
            callback && callback({});
        }
    });
};


DBConnector.prototype.parkArchivePlatformData = function (platformArchiveID, basArchiveID, callback) {
    var query = `UPDATE ${this.basDBName}.PLATFORM_INFO SET PLATFORM_ARCHIVE_ID = ? WHERE BAS_ARCHIVE_ID = ?`;
    var randomId = uuid.v1().replace(/-/g, '_');
    var parkedPlatformArchiveID = "PARKED_"+ platformArchiveID + "_" + randomId;
    this.connection.query(query, [parkedPlatformArchiveID, basArchiveID], function (err/*, rows, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else {
            callback && callback({});
        }
    });
};

DBConnector.prototype.getArchiveIDfromPlatformArchiveID = function (platformSiteID, platformArchiveID, callback) {
    this.connection.query(`SELECT BAS_ARCHIVE_ID FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_SITE_ID = ? AND PLATFORM_ARCHIVE_ID = ?`, [platformSiteID, platformArchiveID], function (err, rows/*, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else {
            if (rows.length !== 1) {
                callback && callback({error: "getArchiveIDfromPlatformArchiveID: Unexpected number of results (" + rows.length + ")", notFound: rows.length === 0});
            } else {
                callback && callback({archiveID: rows[0].BAS_ARCHIVE_ID});   //not found
            }
        }
    });
};

DBConnector.prototype.getArchiveIDfromPlatformArchiveIDAndKind = function(platformArchiveID, platformKind, callback) {
    this.getArchivefromPlatformArchiveIDAndKind(platformArchiveID, platformKind)
        .then(archive => {
            if (archive) {
                callback && callback({ archiveID: archive.BAS_ARCHIVE_ID });
            } else {
                callback && callback({ error: "getArchiveIDfromPlatformArchiveIDAndKind: Unexpected number of results", notFound: true });
            }
        })
        .catch(err => {
            callback && callback({ error: err.message });
        });
};

DBConnector.prototype.getArchivefromPlatformArchiveIDAndKind = async function (platformArchiveID, platformKind) {
    let rows = await this.runQuery(`SELECT BAS_ARCHIVE_ID FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_ARCHIVE_ID = ? AND PLATFORM_KIND = ?`, [platformArchiveID, platformKind]);
    return (rows.length === 1) ? rows[0] : null;
};

//DBConnector.prototype.listArchivePlatformData = function (kind, timestamp, callback) {
//    this.connection.query('SELECT * FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_KIND = ? AND TIMESTAMP < ?;', [kind, timestamp], function (err, rows/*, fields*/) {
//        if (err) {
//            callback && callback({error: err});
//        }
//        else {
//            callback && callback(rows);   //not found
//        }
//    });
//};

//DBConnector.prototype.destroyArchive = function(basArchiveID, callback)
//{
//    //delete the platform data for this archive
//    this.deleteArchivePlatformData(basArchiveID, function(obj)
//    {
//        if (obj.error) {
//            callback && callback(obj);
//        }
//        else {
//            var sessionManager = this.sessionData.sessionManager;
//            var bar = sessionManager.getBar(this.sessionData);
//            //destroy the archive from the DB
//            bar.destroy(basArchiveID, function (obj) {
//                callback(obj);
//            });
//        }
//    }.bind(this));
//};

DBConnector.prototype.updatePlatformAuthToken = function (token, platformAuthToken, clock, callback) {
    var timestamp = clock.now();
    var values, query;

    // we avoid to override the already stored platform token if not passed during as parameters
    if (platformAuthToken) {
        values = [
            platformAuthToken,
            timestamp,
            token
        ];
        query = `UPDATE ${this.basDBName}.USERS SET PLATFORM_TOKEN = ?, TIMESTAMP = ? WHERE TOKEN = ?`;
    } else {
        values = [
            timestamp,
            token
        ];
        query = `UPDATE ${this.basDBName}.USERS SET TIMESTAMP = ? WHERE TOKEN = ?`;
    }

    this.connection.query(query, values, function (err/*, rows, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else {
            callback && callback({success: "ok"});
        }
    });
};

DBConnector.prototype.selectPlatformDataToUnload = function (kind, timestamp, warningFlag, callback) {
    this.connection.query(`SELECT BAS_ARCHIVE_ID FROM ${this.basDBName}.PLATFORM_INFO LEFT OUTER JOIN ${this.basDBName}.USERS ON ${this.basDBName}.PLATFORM_INFO.BAS_ARCHIVE_ID = ${this.basDBName}.USERS.ARCHIVE_ID WHERE ${this.basDBName}.USERS.ARCHIVE_ID IS NULL AND ${this.basDBName}.PLATFORM_INFO.PLATFORM_KIND = ? AND ${this.basDBName}.PLATFORM_INFO.TIMESTAMP < ? AND ${this.basDBName}.PLATFORM_INFO.WARNING_FLAG = ?`, [kind, timestamp, warningFlag], function (err, rows/*, fields*/) {
        if (err) {
            callback && callback({error: err});
        }
        else {
            callback && callback(rows);
        }
    });
};

// DBConnector.prototype.updateUserPermissions = function (token, permissions, callback) {
//     var query;
//
//     query = 'UPDATE ${this.basDBName}.USERS SET PERMISSIONS = ? WHERE TOKEN = ?';
//     this.connection.query(query, [permissions, token], function (err, rows/*, fields*/) {
//         if (err) {
//             callback && callback({error: err});
//         } else {
//             callback && callback({affectedRows: rows.affectedRows});
//         }
//     });
// };

DBConnector.prototype.updateSessionsPermissionsForArchiveAndUsername = function (username, archiveId, permissions, callback) {
    this.updateSessionsPermissionsForArchiveIdAndUsername(archiveId, username, permissions)
        .then(rows => {
            callback && callback({affectedRows: rows.affectedRows});
        })
        .catch(err => {
            callback && callback({error: err});
        });
};

DBConnector.prototype.updateSessionsPermissionsForArchiveIdAndUsername = async function (archiveId, username, permissions) {
    return await this.runQuery(`UPDATE ${this.basDBName}.USERS SET PERMISSIONS = ? WHERE USERNAME = ? AND ARCHIVE_ID = ?`, [permissions, username, archiveId]);
};

DBConnector.prototype.getSessionsByArchiveIdAndUsername = async function (archiveId, username) {
    return await this.runQuery(`SELECT * FROM ${this.basDBName}.USERS WHERE USERNAME = ? AND ARCHIVE_ID = ?`, [username, archiveId]);
};

DBConnector.prototype.getArchiveIDsfromPlatformArchiveIDsAndKind = async function (platformArchiveIDs, platformKind) {
    if (platformArchiveIDs.length === 0) {
        return [];
    }
    let rows = await this.runQuery(`SELECT DISTINCT BAS_ARCHIVE_ID FROM ${this.basDBName}.PLATFORM_INFO WHERE PLATFORM_ARCHIVE_ID IN (?) AND PLATFORM_KIND = ?`, [platformArchiveIDs, platformKind]);
    return rows.map(row => row.BAS_ARCHIVE_ID);
};

DBConnector.prototype.getArchivesfromBASArchiveIds = async function (basArchiveIDs) {
    if (basArchiveIDs.length === 0) {
        return [];
    }
    return await this.runQuery(`SELECT * FROM ${this.basDBName}.PLATFORM_INFO WHERE BAS_ARCHIVE_ID IN (?)`, [basArchiveIDs]);
};

DBConnector.prototype.getSessionsFromArchiveIds = async function (archiveIds) {
    if (archiveIds.length === 0) {
        return [];
    }
    return await this.runQuery(`SELECT ARCHIVE_ID, TOKEN FROM ${this.basDBName}.USERS WHERE ARCHIVE_ID IN (?)`, [archiveIds]);
};

DBConnector.prototype.updateSessionsPermissionsBySessionTokens = async function ({ tokens, permissions }) {
    if (tokens.length === 0) {
        return { affectedRows: 0 };
    }
    const results = await this.runQuery(`UPDATE ${this.basDBName}.USERS SET PERMISSIONS = ? WHERE TOKEN IN (?)`, [permissions, tokens]);
    return { affectedRows: results.affectedRows };
};

DBConnector.prototype.getSessionsForUsernames = async function ({ usernames }) {
    if (usernames.length === 0) {
        return [];
    }
    return await this.runQuery(`SELECT * FROM ${this.basDBName}.USERS WHERE USERNAME IN (?)`, [usernames]);
};

DBConnector.prototype.updateSessionPermissionsByToken = async function ({ token, permissions }) {
    await this.runQuery(`UPDATE ${this.basDBName}.USERS SET PERMISSIONS = ? WHERE TOKEN = ?`, [permissions, token]);
};

DBConnector.prototype.updateSessionUserInfoByToken = async function ({ token, userInfo }) {
    await this.runQuery(`UPDATE ${this.basDBName}.USERS SET USERINFO = ? WHERE TOKEN = ?`, [JSON.stringify(userInfo), token]);
};

DBConnector.prototype.runQuery = function (query, params) {
    return new Promise((resolve, reject) => {
        this.connection.query(query, params, function (err, results, fields) {
            if (err) {
                reject(err);
            } else {
                resolve(results);
            }
        });
    });
};


function makeSqlPlaceholdersForArray(arr) {
    return arr.map(function () { return '?'; }).join(', ');
}

export {
    DBConnectorPool,
    DBConnector
};
