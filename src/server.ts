/** @prettier */
import http from 'http';
import os from 'os';
import con from './constants.ts';
import { makeAppContext } from './app-context.ts';

import { createApp } from './app.js';
import assert from 'assert';
import type { Metrics } from './metrics.ts';
import { isBASLegacyErrorObject } from './calling-style.ts';
import type { HistoryOfPendingSessionsCount } from './session-manager.ts';

async function main() {
    const appContext = await makeAppContext();

    const { config, metrics, sessionManager, serverID, serverUtils } = appContext;

    const diskUsageTask = serverUtils.diskUsageTask;

    const logger = appContext.logger.getLogger({ action: 'startup' });
    logger.info(
        config.metricDisabled
            ? 'Metric is disabled'
            : `Metric is enabled using namespace ${config.metricNamespace} in region ${config.metricRegion}`
    );
    logger.info('BMPR schema version ' + con.BMPR_CURRENT_SCHEMA_VERSION);
    logger.info('Instance serverID: ' + serverID);
    logger.info('Redis: ' + config.redisURL + ' ' + config.redisPort);

    metrics.addValue('server-start', 1, 'Count');

    // JUST FOR TEST [BEGIN]
    //dbConnectorPool.deleteAllProjects(function() {
    //    logger.info("Database reset");
    //});
    // JUST FOR TEST [END]

    const app = createApp(appContext);

    // NOTE(Snyk): This is reported as a vulnerability, but it is not. The server is not directly exposed to the internet.
    const httpServer = http.createServer(app);
    // 3601 and 3605 seconds respectively, which are only slightly longer than
    // the ALB idle timeout (AWS default: 3600) to ensure that the load balancer is
    // responsible for closing the connections.
    //
    // Ref.: https://shuheikagawa.com/blog/2019/04/25/keep-alive-timeout/
    //
    httpServer.keepAliveTimeout = 3610 * 1000;
    // This should be bigger than `keepAliveTimeout + your server's expected response time`:
    httpServer.headersTimeout = 3650 * 1000;

    httpServer.timeout = 5 * 60 * 1000; // 5 minutes max to completely receive and/or finish serving the response

    httpServer.listen(app.get('port'), function () {
        const address = httpServer.address();
        assert(address !== null, 'listening address is null');
        logger.info(`http server listening on ${typeof address === 'string' ? address : `${address.address}:${address.port}`}`);
    });

    let memoryUsageTask = function () {
        let memoryUsage = process.memoryUsage();
        if (memoryUsage.rss) {
            metrics.addValue('memoryUsage', memoryUsage.rss, 'Bytes');
        } else {
            logger.error('[MU] Unexpected error', null, { action: 'memory usage', module: 'gar' });
        }
        let free = os.freemem();
        let total = os.totalmem();
        metrics.addValue('freeMemoryPerc', (free / total) * 100, 'Percent');
    };

    function checkConsistency(
        metrics: Metrics,
        state: HistoryOfPendingSessionsCount | null,
        interval: number,
        cb: (state: HistoryOfPendingSessionsCount | null) => void
    ) {
        sessionManager.createSession(logger, 'checkConsistency', function (obj) {
            if (isBASLegacyErrorObject(obj)) {
                logger.error('unexpected error creating a session ' + obj.error, null, { action: 'checkConsistency', module: 'gar' });
            } else {
                const sessionData = obj;
                state = sessionManager.checkConsistency(metrics, state, interval);
                sessionManager.releaseSession(sessionData);
                cb && cb(state);
            }
        });
    }

    (function () {
        const TO_METRIC = 60 * 1000; // log metrics every minute
        const TO_MEMORY_METRIC = 3 * 1000; // monitor memory usage every 3 seconds
        const TO_CHECK_CONSISTENCY_METRIC = 3 * 1000; // monitor pending session and db connection usage every 3 seconds

        if (!config.metricDisabled) {
            setInterval(function () {
                memoryUsageTask();
            }, TO_MEMORY_METRIC);

            setInterval(function () {
                diskUsageTask(logger);
                metrics.putMetricData();
            }, TO_METRIC);
        }

        // check if there are action pending or resource locked
        let consistencyState: HistoryOfPendingSessionsCount | null = null;
        setInterval(function () {
            checkConsistency(metrics, consistencyState, TO_CHECK_CONSISTENCY_METRIC, (state) => {
                consistencyState = state;
            });
        }, TO_CHECK_CONSISTENCY_METRIC);
        // TEMPORARY: This is only for logging, to validate the algorithm
        setInterval(function () {
            if (consistencyState) {
                logger.info(
                    'Pending sessions state',
                    consistencyState.historyOfPendingSessionsCount[consistencyState.historyOfPendingSessionsCount.length - 1]
                );
            }
        }, 60 * 1000);
    })();
}

main();
