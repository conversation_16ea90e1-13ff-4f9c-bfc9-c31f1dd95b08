/** @prettier */
export interface Clock {
    now(): number;
    sleep(ms: number): Promise<void>;
}

const REAL_TIME_CLOCK: Clock = {
    now() {
        return new Date().getTime();
    },
    sleep(ms: number) {
        return new Promise((resolve) => setTimeout(resolve, ms));
    },
};

// A virtual clock used in tests, to simulate passage of time
class VirtualClock implements Clock {
    _now: number;
    constructor() {
        this._now = new Date().getTime();
    }
    now() {
        return this._now;
    }
    sleep(ms: number) {
        this.advance(ms);
        return Promise.resolve();
    }
    advance(ms: number) {
        this._now += ms;
    }
}

export { REAL_TIME_CLOCK, VirtualClock };
