/** @prettier */
import { BalsamiqArchiveConstants as bmprConsts } from '@balsamiq/bmpr';

export const constants = {
    ARCHIVE_NOT_LOADED: bmprConsts.ErrorCodes.ArchiveNotLoaded,
    MAINTENANCE_MODE: bmprConsts.ErrorCodes.MaintenanceMode,
    OUT_OF_DATE: bmprConsts.ErrorCodes.OutOfDate,
    BMPR_CURRENT_SCHEMA_VERSION: bmprConsts.CurrentSchemaVersion,

    ROLE_NO_ACCESS: bmprConsts.Role.ROLE_NO_ACCESS,
    ROLE_VIEWER: bmprConsts.Role.ROLE_VIEWER,
    ROLE_COMMENTER: bmprConsts.Role.ROLE_COMMENTER,
    ROLE_EDITOR: bmprConsts.Role.ROLE_EDITOR,
    ROLE_ADMIN: bmprConsts.Role.ROLE_ADMIN,

    API_OPEN: 'open',
    API_OPEN_FROM_SERVER: 'openFromServer',
    API_CLOSE: 'close',
    API_CREATE: 'create',
    API_RESTORE: 'restore',
    API_DELETE: 'delete',
    API_UNLOAD_ARCHIVE: 'unloadArchive',
    API_UNLOAD_ARCHIVE_IF_NOT_SYNC: 'unloadArchiveIfNotSync',
    API_GETTOC: 'getTOC',
    API_SETPLATFORMARCHIVENAME: 'setPlatformArchiveName',
    API_GETARCHIVEREVISION: 'getArchiveRevision',
    //API_GETARCHIVEATTRIBUTES: 'getArchiveAttributes',
    //API_GETBRANCHES: 'getBranches',
    API_GETBRANCHATTRIBUTES: 'getBranchAttributes',
    API_GETRESOURCEATTRIBUTES: 'getResourceAttributes',
    API_GETRESOURCEDATA: 'getResourceData',
    API_GETRESOURCESDATA: 'getResourcesData',
    API_GETHEURISTICARCHIVESIZE: 'getHeuristicArchiveSize',
    API_GETTHUMBNAIL: 'getThumbnail',
    API_SETARCHIVEATTRIBUTES: 'setArchiveAttributes',
    API_CREATEBRANCH: 'createBranch',
    API_DELETEBRANCHES: 'deleteBranches',
    API_SETBRANCHATTRIBUTES: 'setBranchAttributes',
    API_CREATERESOURCE: 'createResource',
    API_DELETERESOURCES: 'deleteResources',
    API_SETRESOURCEATTRIBUTES: 'setResourceAttributes',
    API_SETRESOURCEADATA: 'setResourceData',
    API_SETRESOURCEABRANCHID: 'setResourceBranchID',
    API_CREATETHUMBNAIL: 'createThumbnail',
    API_SETTHUMBNAIL: 'setThumbnail',
    API_DELETETHUMBNAILS: 'deleteThumbnails',
    API_CREATECOMMENT: 'createComment',
    API_IMPORTCOMMENT: 'importComment',
    API_GETCOMMENTSDATA: 'getCommentsData',
    API_SETCOMMENTDATA: 'setCommentData',
    API_DELETECOMMENTS: 'deleteComments',
    API_UPDATECOMMENTATTRIBUTES: 'updateCommentAttributes',
    API_UPDATECOMMENTSATTRIBUTES: 'updateCommentsAttributes',
    API_GETARCHIVEUSERSLIST: 'getArchiveUsersList',
    API_CREATEMYUSER: 'createMyUser',
    API_UPDATEMYUSER: 'updateMyUser',
    API_REFRESHSESSION: 'refreshSession',
    API_FETCH: 'fetch',
    API_FLUSH: 'flush',
    API_DOWNLOAD: 'download',
    API_GETUSERSLIST: 'getUsersList',
    API_BROADCASTMESSAGE: 'broadcastMessage',
    API_GETUSERINFO: 'getUserInfo',
    API_UPDATEUSERINFO: 'updateUserInfo',
    API_UPLOADTEMPFILE: 'uploadTempFile',
    API_DOWNLOADTEMPFILE: 'downloadTempFile',
    API_UNLOAD_ARCHIVE_OFFLINE: 'unloadArchiveOffline',
    API_DELETE_ARCHIVE_OFFLINE: 'deleteOffline',
    API_LOGUSEREVENT: 'logUserEvent',
    API_PERMALINK: 'permalink',
    API_SETPERMALINKDATA: 'setPermalinkData',
    API_GETPERMALINKDATA: 'getPermalinkData',
    API_DELETEPERMALINKDATA: 'deletePermalinkData',
    API_GETPERMALINKSDATA: 'getPermalinksData',
    API_CREATE_OR_UPDATE_IMAGE_LINK: 'createOrUpdateImageLink',
    API_PUBLIC_SHARE: 'publicShare',
    API_CREATE_SNAPSHOT: 'createSnapshot',
    API_GET_SNAPSHOT: 'getSnapshot',
    API_FLUSH_OFFLINE: 'flushOffline',
    API_HEARTBEAT: 'heartbeat',
    API_HEALTH: 'health',
    API_GETUSERSINFOANDSETTINGS: 'getUsersInfoAndSettings',
    API_GETPROJECTMEMBERS: 'getProjectMembers',
    API_ADD_TO_BLOCKLIST: 'addToBlockList',
    API_REMOVE_FROM_BLOCKLIST: 'removeFromBlockList',
    API_GET_BLOCKLIST: 'getBlockList',
    API_GET_USER_KEY_FROM_TOKEN: 'getUserKeyFromToken',
    API_GET_RTC_AUTH_TOKEN: 'getRTCAuthToken',
    API_GET_USER_AUTH_TOKEN: 'getUserAuthToken',
    API_RTC_USERS_LEFT_ARCHIVE: 'usersLeftArchive',
    API_TOUCH_RESOURCE: 'touchResource',
} as const;

export default constants;
