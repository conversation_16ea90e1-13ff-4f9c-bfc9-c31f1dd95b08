/** @prettier */
import { HardcodedBalsamiqSecrets, multipleSecretLists } from '@balsamiq/serverconfig/lib/secrets.js';
import os from 'os';
import { getEnvironmentSpecs } from './config-environment.ts';
import type { Config, RTCConfig } from './configLoader.ts';
import { parseZodVariableFromJsonString } from './environment-variables-parsing.ts';
import { RTC_CONFIG_SECRET_SCHEMA } from './environment-variables-schemas.ts';

function buildLocalConfig(cdkBuildProcess: boolean): Config {
    const env = getEnvironmentSpecs({ cdkBuildProcess, local: true }).getValues([
        'BAS_DEV_ENV_NAME',
        'S3_BUCKET_REGION',
        'S3_BUCKET_NAME',
        'KMS_REGION',
        'RTC_WEBHOOK_CALLBACK_BASE_URL',
        'RTC_WEBHOOK_CALLBACK_SECRET',
        'BAS_DB_HOST',
        'BAS_DB_USER',
        'BAS_DB_PASSWORD',
        'BAS_DB_DATABASE',
        'BAS_DB_PERMALINKS_DATABASE',
        'RTC_SECRET',
    ]);

    const devEnvName = env.getString('BAS_DEV_ENV_NAME', os.hostname());
    const rtcWebhookSecret = env.getString('RTC_WEBHOOK_CALLBACK_SECRET', 'Balsam12pgt');

    return {
        port: 4000,
        clusterSize: 1,
        mySQLConfig: {
            credentialsSecret: {
                getSecret() {
                    return {
                        host: env.getString('BAS_DB_HOST', '127.0.0.1'),
                        username: env.getString('BAS_DB_USER', 'root'),
                        password: env.getString('BAS_DB_PASSWORD', ''),
                        port: 3306,
                    };
                },
            },
            region: 'local',
            basDBName: env.getString('BAS_DB_DATABASE', 'BAS'),
            permalinksDBName: env.getString('BAS_DB_PERMALINKS_DATABASE', 'PERMALINKS'),
        },
        environmentName: 'local',
        archiveIDPrefix: '',
        appName: 'bas-local',
        baseUrl: 'https://bas-andrea.ngrok.io',
        shareUrls: {
            us: 'https://bas-andrea.ngrok.io',
            eu: 'https://bas-andrea.ngrok.io',
        },
        defaultDataResidencyName: 'us',
        cloudBaseUrl: 'https://cloud-andrea.ngrok.io',
        archivesPath: '/tmp/archives',
        https: false,
        cloudProjectsMaxAgeInDays: 30,
        unloadUnusedArchivesForConnectors: ['jira', 'confluence', 'cloud', 'wd'],
        connectors: ['cloud', 'confluence', 'jira', 'wd'],
        confluenceNamespace: 'com.balsamiq.mockups.confluence.staging',
        jiraNamespace: 'com.balsamiq.mockups.jira.staging',
        getRtcConfig(): RTCConfig {
            return {
                // 'secrets' needs to match the staging secrets stored in AWS, otherwise communication will fail.
                secrets: parseZodVariableFromJsonString(env.getString('RTC_SECRET'), 'RTC Secret', RTC_CONFIG_SECRET_SCHEMA),
                websocketsUri: 'rtcws-staging.balsamiqstaff.com',
                jwtCallbackInfo: {
                    basBaseURL: env.getString('RTC_WEBHOOK_CALLBACK_BASE_URL', 'https://bas-andrea.ngrok.io/'), // Include trailing slash
                    secret: {
                        type: 'credentials-in-clear', // Only for development!
                        username: 'bas',
                        password: rtcWebhookSecret,
                    },
                },
            };
        },

        getServerApiSecrets() {
            return new HardcodedBalsamiqSecrets(multipleSecretLists(), {
                pollingIntervalInMin: 0,
                data: {
                    bas: [rtcWebhookSecret, 'Balsam12pgt'],
                    rtc: ['Balsam12pgt'],
                },
            });
        },

        metricRegion: 'us-east-1',
        metricNamespace: 'balsamiq/bas-local',
        metricDisabled: true, // for local test
        buildNumber: '999',
        proxyConfig: [
            {
                prefix: '/bw-atlassian/',
                host: 'https://bas-andrea.ngrok.io', // Replace with your ngrok URL
                path: '/bw-atlassian-local/',
            },
        ],
        redisURL: 'localhost',
        redisPort: 6379,
        redisDB: 0,
        permalinkS3Storage: {
            us: {
                bucketRegion: env.getString('S3_BUCKET_REGION', 'us-east-1'),
                bucketName: env.getString('S3_BUCKET_NAME', 'com-balsamiqstaff-bas20-staging-us'),
                baseDir: `${devEnvName}/permalinks`,
            },
            eu: {
                bucketRegion: env.getString('S3_BUCKET_REGION', 'eu-west-1'),
                bucketName: env.getString('S3_BUCKET_NAME', 'com-balsamiqstaff-bas20-staging'),
                baseDir: `${devEnvName}/permalinks`,
            },
        },
        kmsService: {
            region: env.getString('KMS_REGION', 'us-east-1'),
            key_arn: 'alias/user-identity-api-secret',
            environment: 'staging',
        },
        w2iService: {
            region: 'eu-west-1',
            key_arn: 'arn:aws:lambda:eu-west-1:717726050199:function:w2i-staging-ExportFunction-WJKZ9V226OsI',
        },
        i2wService: {
            url: 'https://api.balsamiq.com/i2w-staging/',
        },
        reduceLogging: false,
        loggerOutputForElasticSearch: false,
        cloudConfiguration: {
            cloudServerBaseUrl: 'http://127.0.0.1:9000',
            jwtSecret: 'foobar',
            cloudBasicAuthCredentials: {
                username: 'bas',
                password: 'pass',
            },
        },
    };
}

export { buildLocalConfig };
