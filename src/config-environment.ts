/** @prettier */
import { Environment } from '@balsamiq/environment';

function getEnvironmentSpecs({ cdkBuildProcess, local = false }: { cdkBuildProcess: boolean; local?: boolean }) {
    return new Environment({
        APP_NAME: {
            description: 'APP_NAME',
            errorIfMissing: cdkBuildProcess,
        },
        BAS_APP_CONFIG: {
            description: 'BAS_APP_CONFIG',
            errorIfMissing: cdkBuildProcess,
        },
        BAS_ENV: {
            description: 'BAS_ENV',
            errorIfMissing: cdkBuildProcess,
        },
        BAS_FULL_DOMAIN_NAME: {
            description: 'BAS_FULL_DOMAIN_NAME',
            errorIfMissing: cdkBuildProcess,
        },
        BAS_DB_HOST: {
            description: 'BAS_DB_HOST',
            errorIfMissing: !cdkBuildProcess && !local,
        },
        BAS_DB_USER: {
            description: 'BAS_DB_USER',
            errorIfMissing: !cdkBuildProcess && !local,
        },
        BAS_DB_PASSWORD: {
            description: 'BAS_DB_PASSWORD',
            errorIfMissing: !cdkBuildProcess && !local,
        },
        BAS_DB_DATABASE: {
            description: 'BAS_DB_DATABASE',
            errorIfMissing: false,
        },
        BAS_DB_PERMALINKS_DATABASE: {
            description: 'BAS_DB_PERMALINKS_DATABASE',
            errorIfMissing: false,
        },
        MYSQL_REGION: {
            description: 'MYSQL_REGION',
            errorIfMissing: cdkBuildProcess,
        },
        MYSQL_SECRET: {
            description: 'MYSQL_SECRET',
            errorIfMissing: cdkBuildProcess,
        },
        RTC_SECRET: {
            description: 'RTC_SECRET',
            errorIfMissing: cdkBuildProcess,
        },
        RTC_WEBHOOK_CALLBACK_BASE_URL: {
            description: 'Callback URL for RTC (include trailing slash)',
            errorIfMissing: false,
        },
        RTC_WEBHOOK_CALLBACK_SECRET: {
            description: 'Callback secret for RTC',
            errorIfMissing: false,
        },
        REDIS_URL: {
            description: 'REDIS_URL',
            errorIfMissing: true,
        },
        REDIS_PORT: {
            description: 'REDIS_PORT',
            errorIfMissing: true,
            process: (value) => Number.parseInt(value, 10),
        },
        BUILD_NUMBER: {
            description: 'BUILD_NUMBER',
            errorIfMissing: true,
        },
        BAS_DEV_ENV_NAME: {
            description: 'Name of the development environment (stefano, sax, andrea, etc.)',
            errorIfMissing: false,
        },
        S3_BUCKET_REGION: {
            description: 'S3_BUCKET_REGION',
            errorIfMissing: false,
        },
        S3_BUCKET_NAME: {
            description: 'S3_BUCKET_NAME',
            errorIfMissing: false,
        },
        KMS_REGION: {
            description: 'KMS_REGION',
            errorIfMissing: false,
        },
        BAS_SERVER_API_SECRET: {
            description: 'BAS_SERVER_API_SECRET',
            errorIfMissing: cdkBuildProcess,
        },
        CLOUD_CONFIG_SECRET: {
            description: 'CLOUD_CONFIG_SECRET',
            errorIfMissing: true,
        },
    });
}

export { getEnvironmentSpecs };
