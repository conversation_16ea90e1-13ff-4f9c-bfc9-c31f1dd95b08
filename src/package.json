{"name": "balsamiq-archive-server", "version": "0.1.0", "type": "module", "engines": {"node": "22", "npm": ">=9"}, "scripts": {"start": "node --import=./register-ts-node.js server.ts", "test": "npm run test:format && npm run test:types && npm run test:unit", "debug": "node --inspect --import=./register-ts-node.js server.ts", "coverage": "npm run test:format && npm run test:types && c8 --reporter=lcov --reporter=text npm run test:unit", "test:unit": "NODE_OPTIONS='--import=./register-ts-node.js' mocha", "test:format": "prettier -c \"**/*.{js,ts,md}\"", "test:types": "tsc --noEmit"}, "dependencies": {"@aws-sdk/client-cloudwatch": "^3.405.0", "@aws-sdk/client-kms": "^3.405.0", "@aws-sdk/client-lambda": "^3.405.0", "@aws-sdk/client-s3": "^3.405.0", "@aws-sdk/lib-storage": "^3.405.0", "@balsamiq/bmpr": "^3.17.1", "@balsamiq/environment": "^8.1.1", "@balsamiq/logging": "^8.1.1", "@balsamiq/metrics": "^8.1.1", "@balsamiq/ratelimit": "^8.1.1", "@balsamiq/rtc": "^11.1.1", "@balsamiq/saas-utils": "^8.1.1", "@balsamiq/serverconfig": "^9.9.2", "@types/express": "^4.17.21", "@types/mysql": "^2.15.26", "@types/node": "^22.10.6", "@types/uuid": "^10.0.0", "atlassian-jwt": "^2.0.1", "base64url": "^3.0.1", "basic-auth": "^2.0.1", "body-parser": "^1.18.3", "busboy": "^1.6.0", "chalk": "^5.3.0", "compression": "^1.7.3", "cors": "~2.8.0", "diskspace": "^2.0.0", "dtrace-provider": "^0.8.7", "elementtree": "~0.1.6", "express": "^4.16.4", "express-body-parser-error-handler": "^1.0.4", "express-http-proxy": "^2.1.1", "form-data": "^4.0.1", "get-folder-size": "^5.0.0", "hsts": "^2.1.0", "httpreq": "~0.4.2", "ip": "^2.0.1", "jose": "^6.0.11", "jsuri": "~1.3.x", "jszip": "^3.10.1", "lodash": "^4.17.10", "moment": "^2.20.1", "mysql": "^2.16.0", "redis": "^4.7.0", "sax": "^1.4.1", "short-uuid": "^5.2.0", "sqlite3": "^5.1.7", "superagent": "^10.1.1", "ts-node": "^10.9.2", "tsscmp": "^1.0.6", "typescript": "^5.3.3", "uuid": "^10.0.0", "valid-url": "1.x", "xss": "^1.0.15", "zod": "^3.24.2"}, "optionalDependencies": {"fsevents": "*"}, "devDependencies": {"@types/basic-auth": "^1.1.8", "@types/chai": "^5.0.0", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express-http-proxy": "^1.6.6", "@types/luxon": "^3.6.2", "@types/mocha": "^10.0.7", "@types/supertest": "^6.0.2", "@types/tsscmp": "^1.0.2", "c8": "^10.1.2", "chai": "^5.1.1", "mocha": "^11.1.0", "nock": "^13.5.5", "prettier": "^3.3.3", "supertest": "^7.0.0"}}