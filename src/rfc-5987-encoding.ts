/** @prettier */
//
// This file was originally downloaded from a cached copy at https://npm.balsamiq.com/rfc-5987-encoding/-/rfc-5987-encoding-0.2.0.tgz.
// The original source code was, at the time, available at https://github.com/Myhlamaeus/rfc-5987-encoding/tree/45fe6894b2599a8cb2576f259c9f3741324088c7
//

type EncodableInput = string | { lang: string; str: string };

function encode(input: EncodableInput): string {
    let lang: string = encode.defaultLang;
    let str: string;

    if (typeof input === 'object') {
        lang = input.lang;
        str = input.str;
    } else {
        str = input;
    }

    return (
        "UTF-8'" +
        lang +
        "'" +
        encodeURIComponent(str)
            .replace(/['()]/g, function (match) {
                return '%' + match.charCodeAt(0).toString(16);
            })
            .replace(/\*/g, '%2A')
            .replace(/%(7C|60|5E)/g, function (_, match) {
                return String.fromCharCode(parseInt(match, 16));
            })
    );
}

encode.defaultLang = 'en-US';

function decode(encodedStr: string): { lang: string; str: string } {
    const parts = encodedStr.split("'");

    if (parts.length !== 3) {
        throw new Error('Invalid string: ' + encodedStr);
    }
    if (parts[0].toUpperCase() !== 'UTF-8') {
        throw new Error(`Unsupported encoding: ${parts[0]}`);
    }

    return {
        lang: parts[1],
        str: decodeURIComponent(parts[2]),
    };
}

export { decode, encode };
