import { callWithLegacyCallback } from './calling-style.ts';
import { updatePermalinkImages } from './permalinks.js';
import con from './constants.ts';

async function usersLeftArchive({
    req,
    verifyAdminCredential,
    broadcastRTCMessage,
    getConnector,
    getSessionData,
    rateLimiterConfiguration,
    clock,
}) {
    if (!(await verifyAdminCredential())) {
        return { error: 'wrong credentials' };
    }
    const jsonObj = req.body;
    const channels = jsonObj.channels;
    const sessionData = await getSessionData();

    async function _updatePermalinkImage(sessionData, channel) {
        let resp = {};
        let channelID = channel.id;
        let userIds = channel.userIds;

        req.bas.addLoggerInfo({ channelID });
        // req.bas.logger.info('usersLeftArchive');

        const rateLimiterGroup = rateLimiterConfiguration.getRateLimitersGroup(
            ['usersLeftArchive'],
            { application: 'bas' },
            req.bas.logger
        );
        const rateLimiterStatus = await rateLimiterGroup.checkExceeded();
        if (rateLimiterStatus.hasExceeded) {
            req.bas.sendStatusCodeJsonResponse(429, {
                error: rateLimiterStatus.publicInfo.publicErrorMessage,
            });
            return;
        }

        const dbConnector = sessionData.dbConnector;
        const archiveID = channelID;
        const platformData = await callWithLegacyCallback((callback) => _getPlatformData({ req, archiveID, dbConnector, callback }));
        if (platformData.error) {
            return platformData;
        }
        if (!platformData.platformArchiveID) {
            return { error: 'no platformArchiveID found for channelID: ' + archiveID };
        }
        const usersForArchive = await callWithLegacyCallback((callback) => _getUsersForArchive({ archiveID, dbConnector, callback }));
        if (usersForArchive.error) {
            return usersForArchive;
        }

        const atLeastOneUserIsEditor =
            userIds.findIndex((x) => {
                return (
                    usersForArchive.users.findIndex((userForArchive) => {
                        return x === userForArchive.INTERNAL_ID && userForArchive.PERMISSIONS >= con.ROLE_EDITOR;
                    }) !== -1
                );
            }) !== -1;

        if (atLeastOneUserIsEditor) {
            req.bas.addLoggerInfo({ platformData });
            // req.bas.logger.info("updatePermalinkImages");
            resp = await updatePermalinkImages({
                broadcastRTCMessage,
                dbConnector,
                archiveID,
                getConnector,
                platformArchiveID: platformData.platformArchiveID,
                platformKind: platformData.kind,
                platformSiteID: platformData.platformSiteID,
                timestamp: clock.now(),
                logger: req.bas.logger,
            });
        } else {
            resp = { msg: 'no editor found' };
        }

        return resp;
    }

    return {
        resp: await Promise.all(channels.map((channel) => _updatePermalinkImage(sessionData, channel))),
    };
}

function _getPlatformData({ req, archiveID, dbConnector, callback }) {
    dbConnector.getPlatformData(archiveID, function (obj) {
        let kind;
        if (obj.error) {
            callback(obj);
        } else {
            kind = obj.PLATFORM_KIND;
            const platformArchiveID = obj.PLATFORM_ARCHIVE_ID;
            const platformSiteID = obj.PLATFORM_SITE_ID;
            req.bas_kind = kind;
            callback({ platformArchiveID, platformSiteID, kind });
        }
    });
}

function _getUsersForArchive({ archiveID, dbConnector, callback }) {
    dbConnector.getUsersForArchive(archiveID, function (obj) {
        if (obj.error) {
            callback(obj);
        } else {
            callback(obj);
        }
    });
}

export {
    usersLeftArchive,
};
