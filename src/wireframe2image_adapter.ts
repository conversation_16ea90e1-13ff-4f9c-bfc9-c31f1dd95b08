/** @prettier */

import { InvocationType, type InvokeCommandOutput, LogType } from '@aws-sdk/client-lambda';
import type { DataResidencyName } from './environment-variables-schemas.ts';
import type { BucketResidencyProps } from './s3adapter.ts';

export type ILambda = {
    invoke(args: unknown, cb: (err: any, data?: InvokeCommandOutput) => void): void;
};

class Wireframe2image_adapter {
    private lambda: ILambda;
    private name: string;

    private dataResidencies: Record<DataResidencyName, BucketResidencyProps>;

    constructor({
        lambda,
        name,
        dataResidencies,
    }: {
        lambda: ILambda;
        name: string;
        dataResidencies: Record<DataResidencyName, BucketResidencyProps>;
    }) {
        this.lambda = lambda;
        this.name = name;
        this.dataResidencies = dataResidencies;
    }

    invoke({
        baseUrl,
        branchID,
        resourceID,
        platformArchiveID,
        platformSiteID,
        platformInfo,
        platformToken,
        platformKind,
        permalinkID,
        maxSize,
        permalinkInfo,
        thumbnailSize = 0,
        authorizationHeaderString = '',
        dataResidency,
    }: {
        baseUrl: string;
        branchID: string;
        resourceID: string;
        platformArchiveID: string;
        platformSiteID: string;
        platformInfo: {
            image: {
                transparentBackground: string;
            };
        };
        platformToken: string;
        platformKind: string;
        permalinkID: string;
        maxSize?: number;
        permalinkInfo?: {
            image?: {
                format?: string;
                quality?: string;
            };
        };
        thumbnailSize?: number;
        authorizationHeaderString?: string;
        dataResidency: DataResidencyName;
    }): Promise<string | boolean> {
        const { bucketRegion, bucketName, baseDir } = this.dataResidencies[dataResidency];

        const payload = {
            url: baseUrl,
            resources: [
                {
                    branchID: branchID,
                    resourceID: resourceID,
                },
            ],
            platformArchiveID: platformArchiveID,
            platformSiteID: platformSiteID,
            platformToken: platformToken,
            platformInfo: platformInfo,
            kind: platformKind,
            permalinkID: permalinkID,
            bucketRegion: bucketRegion,
            bucketName: bucketName,
            baseDir: baseDir,
            maxSize: maxSize,
            thumbnailSize,
            authorizationHeaderString,
            format: permalinkInfo?.image?.format,
            quality: permalinkInfo?.image?.quality,
            transparentBackground: platformInfo?.image?.transparentBackground,
        };

        const params = {
            FunctionName: this.name,
            InvocationType: InvocationType.RequestResponse,
            LogType: LogType.None,
            Payload: JSON.stringify(payload),
        };

        return new Promise((resolve, reject) => {
            try {
                this.lambda.invoke(params, (err: any, data?: InvokeCommandOutput) => {
                    try {
                        const dataPayload = err ? null : data && data.Payload && JSON.parse(data.Payload.transformToString());
                        const body = dataPayload ? dataPayload.response : null;
                        if (body && body.img && body.img.length > 0) {
                            resolve(body.img[0]);
                        } else if (body && body.success) {
                            resolve(body.success);
                        } else {
                            reject(new Error(err ? JSON.stringify(err) : JSON.stringify({ ...data, Payload: dataPayload })));
                        }
                    } catch (e) {
                        reject(e);
                    }
                });
            } catch (e) {
                reject(e);
            }
        });
    }
}

export { Wireframe2image_adapter };
