/** @prettier */

// The definition for the Error object returned by legacy callbacks functions.
export interface BASLegacyErrorObject {
    error: string | Error;
    busy?: boolean;
    ignore?: boolean;
    zombie_bar?: string[];
}

// The definition for the legacy callback function
export type BASLegacyCallback<T> = (result: BASLegacyErrorObject | T) => void;

interface BASCustomError extends Error {
    resultObj?: object;
    params?: BASLegacyErrorObject;
}

// A wrapper class to handle errors in the legacy callbacks
export class BASCallbackError extends Error implements BASCustomError {
    resultObj?: object;
    params?: BASLegacyErrorObject;

    constructor(message: string) {
        super(message);
    }
}

export function isBASLegacyErrorObject(obj: unknown): obj is BASLegacyErrorObject {
    return !!obj && typeof obj === 'object' && 'error' in obj && (typeof obj['error'] === 'string' || obj['error'] instanceof Error);
}

function callWithLegacyCallback<ResultType>(fn: (cb: BASLegacyCallback<ResultType>) => void) {
    return new Promise<ResultType>((resolve, reject) => {
        fn((resultObj) => {
            if (isBASLegacyErrorObject(resultObj)) {
                const errorObj = resultObj as BASLegacyErrorObject;
                let err = new BASCallbackError(`${errorObj.error}`);
                err.resultObj = errorObj;
                reject(err);
            } else {
                resolve(resultObj as ResultType);
            }
        });
    });
}

function makeLegacyResultFromError(err: BASCustomError) {
    if (err.resultObj) {
        return err.resultObj;
    } else {
        return {
            error: err.message,
        };
    }
}

function callSaneFunctionFromLegacy<T>(promise: Promise<T>, cb: BASLegacyCallback<T>) {
    promise
        .then((result) => {
            cb(result);
        })
        .catch((err: BASCustomError) => {
            cb({
                error: err.message,
                ...err.resultObj,
                ...err.params,
            });
        });
}

export { callWithLegacyCallback, makeLegacyResultFromError, callSaneFunctionFromLegacy };
