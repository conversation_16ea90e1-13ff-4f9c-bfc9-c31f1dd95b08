import { z, ZodSchema } from 'zod';

export function parseZodVariableFromJsonString<Schema extends ZodSchema>(jsonString: string, name: string, schema: Schema): z.infer<Schema> {
    const parseResult = schema.safeParse(JSON.parse(jsonString));
    if (!parseResult.success) {
        throw new Error(
            `Invalid ${name}: ${parseResult.error.errors.map((error) => `${error.path.join('/')}: ${error.message}`).join(', ')}`
        );
    }
    return parseResult.data;
}