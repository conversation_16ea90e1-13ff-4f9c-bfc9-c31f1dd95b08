/** @prettier */
import { REAL_TIME_CLOCK } from './clock.ts';
import { makeAppContext } from './app-context.ts';
import { cloudGardening } from './cloud_gardening.js';
import { CloudConnector } from './connectors/cloud.js';
import assert from 'assert';

const METRICS_DELAY = 5 * 1000; // Small delay for metrics to be sent to AWS CloudWatch.

async function main() {
    const initializationData = await makeAppContext();
    const logger = initializationData.logger.getLogger({ action: 'cloud-gardening', module: 'gar' });
    const config = initializationData.config;
    const metrics = initializationData.metrics;
    const sessionManager = initializationData.sessionManager;
    const timeDeltaForSavingLiveProjectsInMinutes = initializationData.config.cloudTimeDeltaForSavingLiveProjectsInMinutes || 60;
    const serverUtils = initializationData.serverUtils;
    const cloudConnector = initializationData.getConnector('cloud');
    assert(cloudConnector instanceof CloudConnector, 'Cloud connector not found');

    serverUtils.acquireApplicationLockOnNewConnection('CLOUD_GARDENING', function (obj) {
        if (obj.error) {
            logger.error(`${obj.error.toString()}`);
            process.exit(1);
        } else {
            logger.info(`Application lock CLOUD_GARDENING acquired`);

            logger.info('Cloud gardening started');

            // log the initial status of the disk usage
            serverUtils.diskUsageTask(logger);

            const timer = metrics.trackTimeInterval('Cloud gardening duration');
            cloudGardening({
                serverUtils,
                sessionManager,
                logger,
                metrics,
                timeDeltaForSavingLiveProjects: timeDeltaForSavingLiveProjectsInMinutes * 60 * 1000,
                clock: REAL_TIME_CLOCK,
                cloudConnector,
            })
                .catch((err) => {
                    logger.error(`Unexpected error in cloudGardening: ${err.message}`, err as Error);
                })
                .then(function () {
                    const elapsed = timer.stop();
                    logger.info('Cloud gardening ended. Elapsed ' + (elapsed / 1000 / 60).toFixed(1) + ' min');

                    if (!config.metricDisabled) {
                        metrics.putMetricData();
                        setTimeout(function () {
                            process.exit(0);
                        }, METRICS_DELAY);
                    } else {
                        process.exit(0);
                    }
                });
        }
    });
}

main();
