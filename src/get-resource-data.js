import { callWithLegacyCallback } from './calling-style.ts';

import {
    getValidUserForRole as sane_getValidUserForRole,
} from './sane-helpers.js';
import { getSessionIdFromBearerToken } from './utils.ts';

async function getResourceData({req, res, metrics, minRoleForAccess, sessionManager, getSessionData}) {
    const branchID = req.query.branchID;
    const resourceID = req.query.resourceID;
    const basArchiveID = req.query.basArchiveID;
    let token = req.query.token;

    req.bas.addLoggerInfo({
        branchID,
        resourceID,
        basArchiveID,
    });

    if (!branchID || !resourceID) {
        return {error: "missing parameter in request"};
    }

    const returnEtag = !!basArchiveID;  // Etag is returned only when the url contains all the bits to identify a resource across all the projects.

    if (!token) {
        try {
            token = getSessionIdFromBearerToken(req);
        } catch (err) {
            return {error: err.message};
        }
    }

    const sessionData = await getSessionData({token});
    const dbConnector = sessionData.dbConnector;

    const user = await sane_getValidUserForRole({ token, dbConnector, metrics, logger: req.bas.logger, role: minRoleForAccess, sessionData });
    const archiveID = user.ARCHIVE_ID;

    if (basArchiveID && basArchiveID !== archiveID) {
        req.bas.addLoggerInfo({ archiveID });
        return { error: "basArchiveID doesn't match session's archiveID" };
    }

    const archive = await callWithLegacyCallback(cb => sessionManager.openBarLocked(sessionData, archiveID, "READ", cb));
    const bar = archive.bar;

    let sha1, data, archiveRevision;
    try {
        ({ sha1, data, archiveRevision } = await callWithLegacyCallback(cb => bar.getResourceDataWithOptions(resourceID, branchID, {computeSHA1: returnEtag}, cb)));
    } catch (e) {
        return e.resultObj;
    }

    if (returnEtag) {
        res.set({
            'ETag': `"${sha1}"`,
            'X-Balsamiq-Archive-Revision': archiveRevision,
            'Cache-Control': 'no-cache, private, must-revalidate',
        });

        return { data };
    } else {
        return {
            data,
            archiveRevision,
        };
    }
}

export {
    getResourceData,
};
