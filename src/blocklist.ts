/** @prettier */
import con from './constants.ts';
import type { RedisAdapter } from './redisAdapter.ts';
import type { Application, Response, NextFunction } from 'express';
import type { BASRequest } from './request-context.ts';
import { basRequestHandler } from './middlewares.ts';

const PLATFORM_ARCHIVE_TYPE = 'platformArchive';
const USER_KEY_TYPE = 'userKey';
const PRODUCT_BUILD = 'productBuild';

interface PlatformArchiveParams {
    kind: string;
    platformSiteID: string;
    platformArchiveID: string;
}

interface UserKeyParams {
    userKey: string;
}

interface ProductBuildParams {
    productName: string;
    buildNumber: string | '*';
}

class BlockList {
    private redisAdapter: RedisAdapter;
    private blockListKey: string;

    constructor({ redisAdapter }: { redisAdapter: RedisAdapter }) {
        this.redisAdapter = redisAdapter;
        this.blockListKey = `!bl!`;
    }

    async _block(key: string, obj: unknown): Promise<unknown> {
        const res = await this.redisAdapter.hmset(this.blockListKey, { [key]: JSON.stringify(obj) });
        return res;
    }

    async _unblock(key: string): Promise<boolean> {
        const res = await this.redisAdapter.hdel(this.blockListKey, key);
        return res !== 0;
    }

    async _isBlocked(key: string): Promise<boolean> {
        const res = await this.redisAdapter.hexists(this.blockListKey, key);
        return res;
    }

    async _getBlocked(): Promise<string[]> {
        return await this.redisAdapter.hvals(this.blockListKey);
    }

    _platformArchiveKey({ kind, platformSiteID, platformArchiveID }: PlatformArchiveParams): string {
        return `pa:${kind}:${platformSiteID}:${platformArchiveID}`;
    }

    _userKeyKey({ userKey }: UserKeyParams): string {
        return `uid:${userKey}`;
    }

    _productBuildKey({ productName, buildNumber }: ProductBuildParams): string {
        return `pb:${productName}:${buildNumber}`;
    }

    blockUserKey({ userKey }: UserKeyParams): Promise<unknown> {
        return this._block(this._userKeyKey({ userKey }), { userKey, type: USER_KEY_TYPE });
    }

    blockProductBuild({ productName, buildNumber }: ProductBuildParams): Promise<unknown> {
        return this._block(this._productBuildKey({ productName, buildNumber }), { productName, buildNumber, type: PRODUCT_BUILD });
    }

    blockPlatformArchive({ kind, platformSiteID, platformArchiveID }: PlatformArchiveParams): Promise<unknown> {
        return this._block(this._platformArchiveKey({ kind, platformSiteID, platformArchiveID }), {
            kind,
            platformSiteID,
            platformArchiveID,
            type: PLATFORM_ARCHIVE_TYPE,
        });
    }

    unblockUserKey({ userKey }: UserKeyParams): Promise<boolean> {
        return this._unblock(this._userKeyKey({ userKey }));
    }

    unblockProductBuild({ productName, buildNumber }: ProductBuildParams): Promise<boolean> {
        return this._unblock(this._productBuildKey({ productName, buildNumber }));
    }

    unblockPlatformArchive({ kind, platformSiteID, platformArchiveID }: PlatformArchiveParams): Promise<boolean> {
        return this._unblock(this._platformArchiveKey({ kind, platformSiteID, platformArchiveID }));
    }

    getBlocked(): Promise<string[]> {
        return this._getBlocked();
    }

    isUserKeyBlocked({ userKey }: UserKeyParams): Promise<boolean> {
        return this._isBlocked(this._userKeyKey({ userKey }));
    }

    isPlatformArchiveBlocked({ kind, platformSiteID, platformArchiveID }: PlatformArchiveParams): Promise<boolean> {
        const archiveKey = this._platformArchiveKey({ kind, platformSiteID, platformArchiveID });
        return this._isBlocked(archiveKey);
    }

    isProductBlocked({ productName }: { productName: string }): Promise<boolean> {
        return this._isBlocked(this._productBuildKey({ productName, buildNumber: '*' }));
    }

    isProductBuildBlocked({ productName, buildNumber }: ProductBuildParams): Promise<boolean> {
        return this._isBlocked(this._productBuildKey({ productName, buildNumber }));
    }

    isUserOrPlatformArchiveBlocked(userParams: UserKeyParams, archiveParams: PlatformArchiveParams): Promise<boolean> {
        return Promise.all([this.isUserKeyBlocked(userParams), this.isPlatformArchiveBlocked(archiveParams)]).then((result) =>
            result.some((r) => r)
        );
    }
}

async function getUserKeyFromToken(req: BASRequest, res: Response) {
    /*
        ?platformToken=[...]&kind=[cloud|gd|...]
    */

    const { kind, platformToken } = req.query;
    const connector = req.bas.getConnector(kind as string);

    if (!kind || !platformToken || !connector || typeof platformToken !== 'string') {
        return req.bas.sendStatusCodeJsonResponse(400, { error: 'Bad token or kind' });
    }

    const result = await req.bas.withSession(async (sessionData) => {
        const dbConnector = sessionData.dbConnector;
        return {
            userKey: await connector.getUserKeyFromPlatformToken({ platformToken, dbConnector, logger: req.bas.logger }),
        };
    });

    return req.bas.sendStatusCodeJsonResponse(200, result);
}

async function handleAddToBlockList(req: BASRequest, res: Response) {
    /* {
        items: [
            {"type": "userKey", "userKey": "..."},
            {"type": "platformArchive", "kind": "[cloud|gd|...]", "platformSiteID": "...", "platformArchiveID": "..."}
        ]
    } */

    const { blockList } = req.bas;
    const items = req.body.items || [];
    let addedCount = 0;
    for (let item of items) {
        let { type, ...props } = item;
        if (type === PLATFORM_ARCHIVE_TYPE && props.kind && props.platformArchiveID) {
            if (await blockList.blockPlatformArchive(props)) {
                addedCount += 1;
            }
        } else if (type === USER_KEY_TYPE && props.userKey) {
            if (await blockList.blockUserKey(props)) {
                addedCount += 1;
            }
        } else if (type === PRODUCT_BUILD && props.buildNumber && props.productName) {
            if (await blockList.blockProductBuild(props)) {
                addedCount += 1;
            }
        } else {
            req.bas.logger.info(`Unknown item type: ${type}`);
        }
    }

    return req.bas.sendStatusCodeJsonResponse(200, { addedCount });
}

async function handleRemoveFromBlockList(req: BASRequest, res: Response) {
    /* {
        items: [
            {"type": "userKey", "userKey": "..."},
            {"type": "platformArchive", "kind": "[cloud|gd|...]", "platformSiteID": "...", "platformArchiveID": "..."}
        ]
    } */

    const { blockList } = req.bas;
    const items = req.body.items || [];
    const errorItems: any[] = [];
    let removedCount = 0;
    for (let item of items) {
        let { type, ...props } = item;
        if (type === PLATFORM_ARCHIVE_TYPE && props.kind && props.platformArchiveID) {
            if (await blockList.unblockPlatformArchive(props)) {
                removedCount += 1;
            }
        } else if (type === USER_KEY_TYPE && props.userKey) {
            if (await blockList.unblockUserKey(props)) {
                removedCount += 1;
            }
        } else if (type === PRODUCT_BUILD && props.buildNumber && props.productName) {
            if (await blockList.unblockProductBuild(props)) {
                removedCount += 1;
            }
        } else {
            errorItems.push(item);
            req.bas.logger.info(`Unknown item type: ${type}`);
        }
    }

    return req.bas.sendStatusCodeJsonResponse(200, { removedCount });
}

async function handleGetBlockList(req: BASRequest, res: Response) {
    const { blockList } = req.bas;
    let items = await blockList.getBlocked();
    items = items.map((item) => JSON.parse(item));
    return req.bas.sendStatusCodeJsonResponse(200, { items });
}

async function verifyAdminCredential(req: BASRequest, _res: Response, next: NextFunction) {
    if (!(await req.bas.verifyAdminCredential())) {
        req.bas.logger.info(`Unauthorized attempt to access the blocklist at ${req.path}`);
        return req.bas.sendStatusCodeJsonResponse(401, { error: 'wrong credentials' });
    }
    next();
}

function registerBlockListAPI(app: Application): void {
    const verifyAdminCredentialMiddleware = basRequestHandler(verifyAdminCredential);

    app.get(`/${con.API_GET_USER_KEY_FROM_TOKEN}`, verifyAdminCredentialMiddleware, basRequestHandler(getUserKeyFromToken));
    app.post(`/${con.API_ADD_TO_BLOCKLIST}`, verifyAdminCredentialMiddleware, basRequestHandler(handleAddToBlockList));
    app.post(`/${con.API_REMOVE_FROM_BLOCKLIST}`, verifyAdminCredentialMiddleware, basRequestHandler(handleRemoveFromBlockList));
    app.get(`/${con.API_GET_BLOCKLIST}`, verifyAdminCredentialMiddleware, basRequestHandler(handleGetBlockList));
}

export { BlockList, registerBlockListAPI };
