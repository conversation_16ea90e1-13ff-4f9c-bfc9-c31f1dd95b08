import { type Logger } from "@balsamiq/logging";
import type { RedisAdapter } from "./redisAdapter.ts";
import type { Request, Response, NextFunction } from "express";
import type { Metrics } from "./metrics.ts";

export class BuildNumberTracker {
    metrics: Metrics;
    redisAdapter: RedisAdapter;
    logger: Logger;
    checkServerAPICredentials: (req: Request) => Promise<boolean>;

    constructor(config: {
        metrics: Metrics;
        redisAdapter: RedisAdapter;
        logger: Logger;
        checkServerAPICredentials: (req: Request) => Promise<boolean>;
    }) {
        this.metrics = config.metrics;
        this.redisAdapter = config.redisAdapter;
        this.logger = config.logger.getLogger({ module: "build-number", action: "set_build_number" });
        this.checkServerAPICredentials = config.checkServerAPICredentials;
    }

    async asyncMiddleware(req: Request, res: Response, next: NextFunction) {
        const requestBuildInfo = parseBuildInfoFromRequest(req);

        if (requestBuildInfo !== null) {
            const currentBuildNumber = await this._getCurrentBuildNumber(requestBuildInfo.product);
            if (currentBuildNumber === null) {
                // Tracks errors and missing values fetching the current build number
                this.metrics.addValueWithDimensions([{ Name: "Product", Value: "-error-" }], "Num. requests", 1, "Count");
            } else {
                const currentBuildText = renderBuildNumberDifferenceAsText(currentBuildNumber, requestBuildInfo.number);
                // Counts requests per product and release number
                this.metrics.addValueWithDimensions(
                    [
                        { Name: "Product", Value: requestBuildInfo.product },
                        { Name: "Client release num.", Value: currentBuildText },
                    ],
                    "Num. requests",
                    1,
                    "Count"
                );
                // Counts requests per product
                this.metrics.addValueWithDimensions([{ Name: "Product", Value: requestBuildInfo.product }], "Num. requests", 1, "Count");

                // Tracks the difference between the client and server build numbers
                const difference = currentBuildNumber - requestBuildInfo.number;
                this.metrics.addValueWithDimensions(
                    [
                        { Name: "Product", Value: requestBuildInfo.product },
                        { Name: "Difference", Value: difference.toString() },
                    ],
                    "Num. requests",
                    1,
                    "Count"
                );
            }
        }

        // Counts requests in general
        this.metrics.addValueWithDimensions([], "Num. requests", 1, "Count");

        // API entrypoint used by Jenkins to set the current version number
        if (req.path === "/set_build_number" && req.method === "POST" && req.body) {
            let allowed;
            try {
                allowed = await this.checkServerAPICredentials(req);
            } catch (error: unknown) {
                const err = error as Error;
                this.logger.error("Unexpected error when checking server API credentials", err);
                res.setHeader("Content-Type", "text/plain");
                res.status(500).send("Unexpected error");
                return;
            }

            if (allowed) {
                res.setHeader("Content-Type", "application/json");
                if (req.body.product && req.body.buildNumber) {
                    this._setCurrentBuildNumber(req.body.product, req.body.buildNumber);
                    res.status(200).send(JSON.stringify({ success: true }));
                } else {
                    res.status(400).send(JSON.stringify({ error: 'Missing "product" and "buildNumber" keys' }));
                }
            } else {
                res.setHeader("Content-Type", "text/plain");
                res.status(403).send("Unauthorized");
            }
            return;
        }

        next();
    }

    private async _setCurrentBuildNumber(product: string, buildNumber: number) {
        this.logger.info("Setting build number " + product + " " + buildNumber);
        this.redisAdapter.setBuildNumber(product, buildNumber);
    }

    private async _getCurrentBuildNumber(product: string) {
        const buildNumber = await this.redisAdapter.getBuildNumber(product);
        return buildNumber;
    }
}

function parseBuildInfoFromRequest(req: Request) {
    let headerValue = req.headers["x-balsamiq-build-num"];
    if (headerValue && headerValue.length > 0) {
        let parts = headerValue[0].split("-");
        if (parts.length === 2) {
            let product = parts[0];
            let buildNumber = parseInt(parts[1]);
            if (!isNaN(buildNumber)) {
                return {
                    product: product,
                    number: buildNumber,
                };
            }
        }
    }
    return null;
}

function renderBuildNumberDifferenceAsText(serverBuildNumber: number, clientBuildNumber: number) {
    if (serverBuildNumber === clientBuildNumber) {
        return "current";
    } else {
        const difference = clientBuildNumber - serverBuildNumber;
        if (difference < -5) {
            return "ancient";
        } else {
            return "current " + difference;
        }
    }
}
