import { BASApiClient } from './test/utils/apiclient.ts';

async function health({ app, req, res, clock, sessionManager, config, getSessionData, releaseSessionIfNotReleased, serverID, serverUtils, getConnector, rateLimiterConfiguration }) {
    let stat = req.query.stat;
    let now = clock.now();
    let reset = req.query.reset;
    let testDbConnection = req.query.db === 'true';
    let testWebdemo = req.query.webdemo === 'true';
    let paramServerID = req.query.serverID;

    let resp = {
        now: now,
        build: config.buildNumber,
        status: 'ok',
    };

    req.bas.addLoggerInfo({testDbConnection, testWebdemo, paramServerID});

    if (testDbConnection) {
        let sessionData;
        try {
            sessionData = await getSessionData();
        } catch(err) {
            req.bas.logger.error("Connection to database is unavailable " + obj.error);
            res.status(503).send("Connection to database is unavailable");
            return;
        }
        await releaseSessionIfNotReleased();
        resp.dbConnection = "ok"; // Add db connection info
        return resp;
    } else if (testWebdemo) {
        const authorized = await serverUtils.checkServerAPICredentials(req);
        if (!authorized) {
            res.status(401).send("Authorization failed");
            return;
        }

        const rateLimiterGroup = rateLimiterConfiguration.getRateLimitersGroup(
            ['healthCheck'],
            { application: 'bas' },
            req.bas.logger
        );
        const rateLimiterStatus = await rateLimiterGroup.checkExceeded();
        if (rateLimiterStatus.hasExceeded) {
            req.bas.sendStatusCodeJsonResponse(429, {
                error: rateLimiterStatus.publicInfo.publicErrorMessage,
            });
            return;
        }
        rateLimiterGroup.incr();

        const basClient = new BASApiClient(app, {
            kind: 'wd',
            userName: 'health-check',
            getPlatformToken: () => getConnector("wd").generalAccessPlatformToken,
        });

        let result = await basClient.create();
        if (!result.ok || !result.body.platformArchiveID) {
            req.bas.logger.error(`Health check failed to create webdemo test archive: ${result.body.error}`);
            res.status(503).send("Health check failed to create webdemo test archive");
            return;
        }
        basClient.platformArchiveID = result.body.platformArchiveID;

        result = await basClient.open();
        if (!result.ok || !result.body.token) {
            req.bas.logger.error(`Health check failed to open webdemo test archive ${result.body.error}`);
            res.status(503).send("Health check failed to open webdemo test archive");
            return;
        }
        const token = result.body.token;
        
        result = await basClient.delete({token});
        if (!result.ok || result.body.error) {
            req.bas.logger.error(`Health check failed to delete webdemo test archive ${result.body.error}`);
            req.status(503).send("Health check failed to delete webdemo test archive");
            return;
        }

        result = await basClient.close({token});
        if (!result.ok) {
            req.bas.logger.error(`Health check failed to close webdemo test archive ${result.body.error}`);
            res.status(503).send("Health check failed to close webdemo test archive");
            return;
        }
            
        resp.webdemo = 'ok';
        return resp;
    } else if (reset === "edeef418-ddf6-4f1c-bfe5-e60237938adc") {
        req.bas.addLoggerInfo({ action: "startup" });
        if (paramServerID === serverID) {
            req.bas.logger.info("Going to restart the server " + serverID);
            resp.status = 'exiting';
            res.json(resp);
            await sleep(500);
            process.exit();
        } else {
            resp.status = "Ignoring the reset command " + paramServerID + " != " + serverID;
            req.bas.logger.info(resp.status);
        }
    }

    if (stat) {
        resp.stat = sessionManager.getSessionStat();
        resp.serverID = serverID;
    }

    return resp;
}

export {
    health,
};
