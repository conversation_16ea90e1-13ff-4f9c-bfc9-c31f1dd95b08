import { makeAppContext } from './app-context.ts';

let sessionCachedLimit = 24*60*60*1000; // 24h
let sessionManager, serverUtils;

let mainGardeningJob = function(logger, onDone) {
    let startingTaskTS;
    let logObj = {action: "main-gardening",  module: "gar"};
    logger = logger.getLogger({action: "main-gardening",  module: "gar"});

    let logTaskDuration = function(jobName) {
        let now = new Date().getTime();
        let duration = (now - startingTaskTS) / 1000;
        startingTaskTS = now;
        logger.info("metric [" + jobName + "] duration " + duration + " secs");
    };

    let runJob = function(sessionData) {
        if (sessionData) {
            sessionManager.releaseSession(sessionData, function () {
                onDone();
            });
        }
    };

    sessionManager.createSession(logger, "main-gardening", function (obj) {
        let sessionData, dbConnector;
        if (obj.error) {
            logger.error("unexpected error: " + obj.error);
            runJob(obj);
            return;
        }
        sessionData = obj;
        // add the log info to the current session in order to better trace gardening sub-tasks
        sessionData.logObj = logObj;
        dbConnector = sessionData.dbConnector;
        startingTaskTS = new Date().getTime();
        // delete not consistent entries in the DB
        dbConnector.deleteArchiveZombieEntries(function () {
            // unload zombie archive
            logTaskDuration("deleteArchiveZombieEntries");
            dbConnector.deleteSessionZombieEntries(function() {
                logTaskDuration("deleteSessionZombieEntries");
                dbConnector.dropZombieArchive(function () {
                    let ts = new Date().getTime() - sessionCachedLimit;
                    logTaskDuration("dropZombieArchive");
                    dbConnector.deleteZombieSession(ts, function() {
                        logTaskDuration("deleteZombieSession");
                        serverUtils.unloadUnusedArchiveJob(logger, sessionData, null, function () {
                            logTaskDuration("unloadUnusedArchiveJob");
                            runJob(sessionData);
                        });
                    });
                });
            });
        });
    });
};

async function main() {
    let metricsDelay = 5 * 1000; // Small delay for metrics to be sent to AWS CloudWatch.
    let initializationData = await makeAppContext();
    let logger = initializationData.logger.getLogger({action: "main-gardening", module: "gar"});
    let config = initializationData.config;
    let metrics = initializationData.metrics;
    sessionManager = initializationData.sessionManager;
    serverUtils = initializationData.serverUtils;

    serverUtils.acquireApplicationLockOnNewConnection('MAIN_GARDENING', function (obj) {
        if (obj.error) {
            logger.error(obj.error);
            process.exit(1);
        } else {
            logger.info(`Application lock MAIN_GARDENING acquired`);

            logger.info("Main gardening started");
            let timer = metrics.trackTimeInterval('main gardening duration');
            mainGardeningJob(logger, function () {
                let elapsed = timer.stop();
                logger.info("Main gardening ended. Elapsed " + (elapsed/1000/60).toFixed(1) + " min");

                if (!config.metricDisabled) {
                    metrics.putMetricData();
                    setTimeout(function() {
                        process.exit(0);
                    }, metricsDelay);
                } else {
                    process.exit(0);
                }
            });
        }
    });
}

main();
