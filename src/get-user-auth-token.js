// const { callWithLegacyCallback } = require('./calling-style');
import { getValidUserForRole as sane_getValidUserForRole } from './sane-helpers.js';

// TODO: add to the config?
const JWT_EXPIRATION_IN_MSECS = 15 * 60 * 1000;  // 15 minutes

function getPlatformKindFromArchiveID(config, archiveID) {
    let str = archiveID.replace(config.archiveIDPrefix, "");
    let tmp = str.split("_");
    return tmp[0];
}

async function getTokenFromKMS(jwtKmsAdapter, key_arn, uniqueUserId) {
    // call AWS KMS service
    // Create a JWT token using a KMS key identified by a key_arn
    return jwtKmsAdapter.createSignedJWT(
        key_arn,
        {userId: uniqueUserId, iss: "bas"},
        {expires: new Date(Date.now() + JWT_EXPIRATION_IN_MSECS)}
    )
}

async function getUserAuthToken({req, metrics, jwtKmsAdapter, minRoleForAccess, getSessionData, getConnector, config}) {
    let token = req.query.token;

    const sessionData = await getSessionData({token});
    const dbConnector = sessionData.dbConnector;

    const user = await sane_getValidUserForRole({
        token,
        dbConnector,
        metrics,
        logger: req.bas.logger,
        role: minRoleForAccess,
        sessionData
    });
    const archiveID = user.ARCHIVE_ID;
    const userId = user.USERNAME;
    const platformKind = getPlatformKindFromArchiveID(config, archiveID);
    const connector = getConnector(platformKind);
    if (!connector) {
        return {error: "connector unavailable for platformKind " + platformKind};
    }
    const uniqueUserId = connector.getUniqueUserId(user);
    let userAuthToken;

    req.bas.addLoggerInfo({archiveID, platformKind, userId});

    // call KMS
    userAuthToken = await getTokenFromKMS(jwtKmsAdapter, config.kmsService.key_arn, uniqueUserId);

    req.bas.logger.info(`Get User Auth Token ${userAuthToken}`);

    return {
        userAuthToken: userAuthToken,
        services: {
            i2w: {
                url: config.i2wService.url,
            },
        },
    };
}

export {
    getUserAuthToken,
};
