import { callWithLegacyCallback } from '../calling-style.ts';
import Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js';

async function deletePermalinkData({
    req,
    config,
    getSessionData,
    broadcastRTCMessage,
    sane_getValidUserForRole,
    metrics,
    minRoleForAccess,
    actionName,
    getConnector,
}) {
    const jsonObj = req.body;
    const token = req.query.token;

    let resourceID = jsonObj.resourceID;
    let branchID = jsonObj.branchID;
    let permalinkID = jsonObj.permalinkID;
    const permalinkKind = jsonObj.permalinkKind || Consts.PermalinkKind.image;

    if (!token) {
        return {error: "wrong credentials"};
    }

    if ((!resourceID || !branchID) && !permalinkID) {
        return {error: "invalid parameters"};
    }

    req.bas.addLoggerInfo({resourceID, branchID, permalinkID, permalinkKind});

    const sessionData = await getSessionData({token});
    const dbConnector = sessionData.dbConnector;

    const user = await sane_getValidUserForRole({ token, dbConnector, metrics, logger: req.bas.logger, role: minRoleForAccess, sessionData });
    const archiveID = user.ARCHIVE_ID;
    const internalUserID = user.INTERNAL_ID;
    const username = user.USERNAME;

    req.bas.addLoggerInfo({archiveID});

    let platformData = await callWithLegacyCallback(cb => dbConnector.getPlatformData(archiveID, cb));
    const platformSiteID = platformData.PLATFORM_SITE_ID;
    const platformArchiveID = platformData.PLATFORM_ARCHIVE_ID;
    const platformKind = platformData.PLATFORM_KIND;

    req.bas.addLoggerInfo({
        platformArchiveID,
        platformSiteID,
        platformKind,
    });

    if (!platformArchiveID || !platformKind) {
        return {error: "platform data unavailable for archiveID " + archiveID};
    }

    const connector = getConnector(platformKind);
    if (!connector) {
        return {error: "connector unavailable for platformKind " + platformKind};
    }

    let permalinkData;
    if (resourceID && branchID) {
        permalinkData = await dbConnector.getPermalinkDataFromResourceID({platformKind, platformSiteID, platformArchiveID, resourceID, branchID, permalinkKind});
    } else {
        permalinkData = await dbConnector.getPermalinkDataFromPermalinkID({platformKind, platformSiteID, platformArchiveID, permalinkID});
    }

    if (!permalinkData.permalinkID) {
        return {warning: "no permalink found for the requested resource"};
    }

    // fill in missing resourceID and branchID info in case we got the permalink from permalinkID
    resourceID = resourceID || permalinkData.resourceID;
    branchID = branchID || permalinkData.branchID;

    if (permalinkData.permalinkKind === Consts.PermalinkKind.image ||
        permalinkData.permalinkKind === Consts.PermalinkKind.image_unfurling) {
        await connector.deletePermalinkImages({permalinkIDs: [permalinkData.permalinkID], format: permalinkData.permalinkInfo?.image?.format || 'png', dataResidency: permalinkData.permalinkInfo?.dataResidency || config.defaultDataResidencyName});
        await connector.deletePermalinkThumbnailImages({permalinkIDs: [permalinkData.permalinkID], format: permalinkData.permalinkInfo?.image?.format || 'png', dataResidency: permalinkData.permalinkInfo?.dataResidency || config.defaultDataResidencyName});
    }

    try {
        await dbConnector.deletePermalinkData({platformKind, platformSiteID, platformArchiveID, resourceID, branchID, permalinkKind: permalinkData.permalinkKind});

        broadcastRTCMessage(archiveID, null, internalUserID, username, {
            operation: actionName,
            resourceID,
            branchID,

            platformKind: platformKind,
            platformArchiveID: platformArchiveID,
            platformSiteID: platformSiteID,
            permalinkUUID: permalinkData.permalinkID
        });
    } catch (err) {
        if (err.notFound) {
            return {warning: "no permalink found for the requested resource"};
        }
        throw err;
    }
    return {message: "permalink deleted"};
}

export {
    deletePermalinkData,
};
