import { callWithLegacyCallback } from '../calling-style.ts';
import Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js';

async function getPermalinkData({
    req,
    res,
    getSessionData,
    makePermalinkObject,
    metrics,
    sane_getValidUserForRole,
    minRoleForAccess,
    getConnector,
}) {
    const token = req.query.token;
    const resourceID = req.query.resourceID;
    const branchID = req.query.branchID;
    let permalinkID = req.query.permalinkID;
    const permalinkKind = req.query.permalinkKind || Consts.PermalinkKind.image;

    if (!token) {
        return {error: "wrong credentials"};
    }

    if ((!resourceID || !branchID) && !permalinkID) {
        return {error: "invalid parameters"};
    }

    req.bas.addLoggerInfo({resourceID, branchID, permalinkID, permalinkKind});
    res.setHeader("Cache-Control", "no-store, no-cache");

    const sessionData = await getSessionData({token});
    const dbConnector = sessionData.dbConnector;

    const user = await sane_getValidUserForRole({
        token,
        dbConnector,
        metrics,
        logger: req.bas.logger,
        role: minRoleForAccess,
        sessionData,
    });

    const archiveID = user.ARCHIVE_ID;
    req.bas.addLoggerInfo({archiveID});

    const platformData = await callWithLegacyCallback(cb => dbConnector.getPlatformData(archiveID, cb));

    const platformSiteID = platformData.PLATFORM_SITE_ID;
    const platformArchiveID = platformData.PLATFORM_ARCHIVE_ID;
    const platformKind = platformData.PLATFORM_KIND;

    req.bas.addLoggerInfo({
        platformSiteID,
        platformArchiveID,
        platformKind,
    });

    if (!platformArchiveID || !platformKind) {
        return {error: "platform data unavailable for archiveID " + archiveID};
    }

    const connector = getConnector(platformKind);
    if (!connector) {
        return {error: "connector unavailable for platformKind " + platformKind};
    }

    let permalinkData;
    if (resourceID && branchID) {
        permalinkData = await dbConnector.getPermalinkDataFromResourceID({platformKind, platformSiteID, platformArchiveID, resourceID, branchID, permalinkKind});
    } else {
        permalinkData = await dbConnector.getPermalinkDataFromPermalinkID({platformKind, platformSiteID, platformArchiveID, permalinkID});
    }

    if (!permalinkData.permalinkID) {
        return {message: 'permalink does not exist'};
    }

    return makePermalinkObject(permalinkData, connector);
}

export {
    getPermalinkData
};
