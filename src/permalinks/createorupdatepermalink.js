import busboy from 'busboy';
import fs from 'fs';
import { callWithLegacyCallback } from '../calling-style.ts';
import Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js';

async function createOrUpdateImageLink({
    req,
    getSessionData,
    releaseSessionIfNotReleased,
    sane_getValidUserForRole,
    metrics,
    minRoleForAccess,
    makePermalinkObject,
    broadcastRTCMessage,
    actionName,
    getConnector,
    clock,
}) {
    const token = req.query.token;

    let params = await parseFormData(req);
    let {attachment, permalinkInfo, resourceID, branchID} = params;

    try {
        permalinkInfo = JSON.parse(permalinkInfo);
    } catch (err) {
        permalinkInfo = {};
    }

    if (!token || !resourceID || !branchID ) {
        return {error: "wrong credentials or invalid paramenters", token, resourceID, branchID};
    }

    if (attachment !== null && attachment.mimeType !== 'image/png') {
        return {error: `unsupported image mimeType: ${attachment.mimeType}`};
    }

    req.bas.addLoggerInfo({resourceID, branchID});

    const sessionData = await getSessionData({token});
    const dbConnector = sessionData.dbConnector;

    const user = await sane_getValidUserForRole({ token, dbConnector, metrics, logger: req.bas.logger, role: minRoleForAccess, sessionData });
    const internalUserID = user.INTERNAL_ID;
    const username = user.USERNAME;
    const archiveID = user.ARCHIVE_ID;

    req.bas.addLoggerInfo({archiveID});

    const platformData = await callWithLegacyCallback(cb => dbConnector.getPlatformData(archiveID, cb));
    const platformSiteID = platformData.PLATFORM_SITE_ID;
    const platformArchiveID = platformData.PLATFORM_ARCHIVE_ID;
    const platformKind = platformData.PLATFORM_KIND;
    let platformInfo;
    try {
        platformInfo = JSON.parse(platformData.PLATFORM_INFO);
    } catch (e) {
        platformInfo = {};
    }

    req.bas.addLoggerInfo({
        platformArchiveID,
        platformSiteID,
        platformKind,
    });

    if (!platformArchiveID || !platformKind) {
        return {error: "platform data unavailable for archiveID " + archiveID};
    }

    const connector = getConnector(platformKind);
    if (!connector?.makePermalinkID) {
        return {error: "platform does not support permalink"};
    }

    // no need to add to the message platformArchiveID, platformSiteID and platformKind because already included with addLoggerInfo
    req.bas.logger.info('start createOrUpdateImageLink');

    // Prepare a new permalinkID to use in case of insert or use the one passed in the request
    let tempPermalinkID = permalinkInfo.permalinkUUID ? permalinkInfo.permalinkUUID : connector.makePermalinkID({platformKind, platformSiteID, platformArchiveID, resourceID, branchID});

    // As of now only Cloud supports data residency, so we are saving ourselves the trouble of calling the database or the connector unnecessarily:
    if (connector.getDataResidency) {
        let existingPermalink = await dbConnector.getPermalinkDataFromResourceID({
           platformKind,
           platformSiteID,
           platformArchiveID,
           resourceID,
           branchID,
        });
        if(!existingPermalink.permalinkID) {
            existingPermalink = null;
        }

        if (existingPermalink) {
            // Data residency of existing permalinks should never change:
            permalinkInfo.dataResidency = existingPermalink.permalinkInfo.dataResidency;
        } else {
          try {
              // Ask the data residency to the authority which is the connector:
              permalinkInfo.dataResidency = await connector.getDataResidency(req.bas.logger, platformSiteID, platformArchiveID);
          } catch (err) {
              req.bas.logger.error(`Unexpected error in connector.getDataResidency: ${err.message}`, err);

              // Note: This is a comment that underlines the fact that if anything goes wrong with the call to the connector above then we fall back to leaving the data residency not explicit set by not doing anything here.
          }
        }
    }

    const [permalinkData, created] = await dbConnector.insertOrUpdatePermalink({
        permalinkID: tempPermalinkID,
        platformKind,
        platformSiteID,
        platformArchiveID,
        resourceID,
        branchID,
        dirty: false,
        permalinkKind: Consts.PermalinkKind.image,
        permalinkInfo,
        platformInfo,
        timestamp: clock.now(),
    });

    await releaseSessionIfNotReleased();

    req.bas.addLoggerInfo({
        permalinkID: permalinkData.permalinkID
    });

    // TODO: maybe in the future the attachment will be always null and this code will be removed
    if (attachment !== null) {
        // attachment format is "png" by design
        await connector.storePermalinkImage({
            dataStream: attachment.file,
            mimeType: attachment.mimeType,
            permalinkID: permalinkData.permalinkID,
        });

        // TODO: lazy image generation for comparison, in the future we'll remove it
        connector.generatePermalinkImage({logger: req.bas.logger, permalinkData, suffix: "_w2i"}).catch(
            (err) => {
                req.bas.logger.error(`Unexpected error in connector.generatePermalinkImage (attachment): ${err.message}`, err);
            });
    }
    else {
        // upload a dummy image
        if ( created === 1) {
            const htmlContentStream = fs.createReadStream('./permalinks/permalink_try_later.html');
            await connector.storePermalinkImage({
                dataStream: htmlContentStream,
                mimeType: "text/html",
                permalinkID: permalinkData.permalinkID,
                permalinkInfo: permalinkData.permalinkInfo
            });
        }

        //lazy image generation
        connector.generatePermalinkImage({logger: req.bas.logger, permalinkData}).catch(
            (err) => {
                req.bas.logger.error(`Unexpected error in connector.generatePermalinkImage (no attachment): ${err.message}`, err);
            });
    }

    req.bas.logger.info(`Permalink ${permalinkData.permalinkID} ${ created === 1 ? 'Inserted' : 'Updated' }`);

    broadcastRTCMessage(archiveID, null, internalUserID, username, {
        operation: actionName,
        resourceID,
        branchID,

        platformKind: platformKind,
        platformArchiveID: platformArchiveID,
        platformSiteID: platformSiteID,
        permalinkUUID: permalinkData.permalinkID
    });

    return makePermalinkObject(permalinkData, connector);
}

function parseFormData(req) {
    if (!req.is('multipart/form-data')) {
        return {attachment: null};  // No formdata to parse
    }

    const fieldLimits = {  // Block some kind of malicious requests
        fileSize: 20 * 1024 * 1024,
        fields: 10,
        parts: 10,
        files: 1,
    };
    const bb = busboy({ headers: req.headers, limits: fieldLimits});
    return new Promise((resolve, reject) => {
        let params = {};
        let attachment = null;
        bb.on('field', function (fieldname, val) {
            params[fieldname] = val;
        });

        bb.on('file', function(fieldname, file, info) {
            const { filename, encoding, mimeType } = info;
            attachment = {fieldname, file, filename, encoding, mimeType};
            resolve({...params, attachment});
        });

        bb.on('error', reject);
        bb.on('finish', () => resolve({...params, attachment}));

        req.pipe(bb).on('error', reject);
    });
}

export {
    createOrUpdateImageLink,
};
