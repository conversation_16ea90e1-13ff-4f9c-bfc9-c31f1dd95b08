import { callWithLegacyCallback } from '../calling-style.ts';
import superagent from 'superagent';

async function getPublicShare({
                                  req,
                                  res,
                                  getSessionData,
                                  getStaticFileRepository,
                              }) {
    // https://share.balsamiq.com/ps/52s8pKS9KdtDCyWUJBkB3Q?f=N4IgUiBcCMA0IDkpxAYWfAMhkAhHAsjgFo4DSUA2gLoC%2BQA%3D
    let url;
    let staticFileRepository = getStaticFileRepository();

    if (staticFileRepository === null) {
        req.bas.logger.error("Unexpected error: BAS misconfiguration: missing config.proxyConfig for public share");
        res.status(500).send("Unexpected error: wrong configuration");
        return;
    }

    const sessionData = await getSessionData();
    let dbConnector = sessionData.dbConnector;
    let permalinkData;
    const permalinkID = req.params.id;
    req.bas.addLoggerInfo({permalinkID: permalinkID});

    try {
        // if permalinkId is not specified the API has been already failed before
        permalinkData = await callWithLegacyCallback(cb => dbConnector.getPermalinkData(permalinkID, cb));
        url = staticFileRepository + "share/public_share.html";
        req.bas.logger.info("Getting Public Share link " + url);
    } catch (err) {
        // not a valid permalink ID
        if (err.resultObj.notFound) {
            req.bas.logger.info("Public Share link not found");
            url = staticFileRepository + "share/404.html";
            res.setHeader('Cache-Control', 'no-cache, no-store');
            superagent.get(url).end((error, response) => {
                if (error) {
                    req.bas.logger.error("Error fetching 404 page", error);
                    res.status(500).send("Unexpected error: unable to fetch 404 page");
                } else {
                    res.status(404).send(response.text);
                }
            });
            return;
        } else {
            req.bas.logger.error("Public Share link not found: Unexpected error ", err);
            throw err;
        }
    }

    res.setHeader('Cache-Control', 'no-cache, no-store');
    superagent.get(url).end((error, response) => {
        if (error) {
            req.bas.logger.error("Error fetching public share page", error);
            res.status(500).send("Unexpected error: unable to fetch public share page");
        } else {
            res.send(response.text);
        }
    });
}

export {
    getPublicShare
};
