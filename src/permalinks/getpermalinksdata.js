import { callWithLegacyCallback } from '../calling-style.ts';

async function getPermalinksData({
     req,
     res,
     getSessionData,
     metrics,
     sane_getValidUserForRole,
     minRoleForAccess,
     getConnector,
     serverUtils,
 }) {
    const token = req.query.token;

    if (!token) {
        return {error: "wrong credentials"};
    }

    const sessionData = await getSessionData({token});
    const dbConnector = sessionData.dbConnector;

    const user = await sane_getValidUserForRole({
        token,
        dbConnector,
        metrics,
        logger: req.bas.logger,
        role: minRoleForAccess,
        sessionData,
    });


    const archiveID = user.ARCHIVE_ID;
    req.bas.addLoggerInfo({archiveID});
    res.setHeader("Cache-Control", "no-store, no-cache");

    const platformData = await callWithLegacyCallback(cb => dbConnector.getPlatformData(archiveID, cb));

    const platformSiteID = platformData.PLATFORM_SITE_ID;
    const platformArchiveID = platformData.PLATFORM_ARCHIVE_ID;
    const platformKind = platformData.PLATFORM_KIND;

    req.bas.addLoggerInfo({
        platformSiteID,
        platformArchiveID,
        platformKind,
    });

    if (!platformArchiveID || !platformKind) {
        return {error: "platform data unavailable for archiveID " + archiveID};
    }

    const connector = getConnector(platformKind);
    if (!connector) {
        return {error: "connector unavailable for platformKind " + platformKind};
    }

    return await serverUtils.getPermalinksData({
        siteId: platformSiteID, projectId: platformArchiveID, kind: platformKind, dbConnector, connector
    })
}

export {
    getPermalinksData
};
