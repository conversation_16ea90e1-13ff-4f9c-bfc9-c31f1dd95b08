import { callWithLegacyCallback } from '../calling-style.ts';
import Consts from '@balsamiq/bmpr/lib/BalsamiqArchiveConstants.js';
const allowedPermalinkKinds = new Set([Consts.PermalinkKind.image, Consts.PermalinkKind.public_share]);

async function setPermalink({
    req,
    metrics,
    broadcastRTCMessage,
    sane_getValidUserForRole,
    minRoleForAccess,
    makePermalinkObject,
    getSessionData,
    verifyAdminCredential,
    getConnector,
    actionName,
    clock,
}) {
    // Parse params
    const token = req.query.token;
    const jsonObj = req.body;
    const resourceID = jsonObj.resourceID;
    const branchID = jsonObj.branchID;
    const permalinkKind = jsonObj.permalinkKind || Consts.PermalinkKind.image;
    const check = jsonObj.check;

    let permalinkID,
        permalinkInfo;
    try {
        permalinkInfo = JSON.parse(jsonObj.permalinkInfo);
        permalinkID = permalinkInfo.permalinkUUID;
    } catch (e) {
        permalinkInfo = {};
    }

    if (token) {
        return await setPermalinkAsUser();
    } else {
        return await setPermalinkAsBASAdmin();  // If no token assume it's an admin request
    }

    async function setPermalinkAsUser() {
        req.bas.addLoggerInfo({resourceID, branchID, permalinkKind});

        // empty resourceID and branchID allowed only for public_share kind
        if (!allowedPermalinkKinds.has(permalinkKind) ||
            permalinkKind === Consts.PermalinkKind.image && (!resourceID || !branchID)) {
            return {error: "wrong parameters"};
        }

        const sessionData = await getSessionData({token});
        const dbConnector = sessionData.dbConnector;

        const user = await sane_getValidUserForRole({
            token,
            dbConnector,
            metrics,
            logger: req.bas.logger,
            role: minRoleForAccess,
            sessionData,
        });

        const archiveID = user.ARCHIVE_ID;
        const internalUserID = user.INTERNAL_ID;
        const username = user.USERNAME;

        req.bas.addLoggerInfo({archiveID});

        const platformData = await callWithLegacyCallback(cb => dbConnector.getPlatformData(archiveID, cb));

        const platformSiteID = platformData.PLATFORM_SITE_ID;
        const platformArchiveID = platformData.PLATFORM_ARCHIVE_ID;
        const platformKind = platformData.PLATFORM_KIND;

        let platformInfo;
        try {
            platformInfo = JSON.parse(platformData.PLATFORM_INFO);
        } catch (e) {
            platformInfo = {};
        }

        req.bas.addLoggerInfo({
            platformArchiveID,
            platformSiteID,
            platformKind,
        });

        if (!platformArchiveID || !platformKind) {
            return {error: "platform data unavailable for archiveID " + archiveID};
        }

        const connector = getConnector(platformKind);
        if (!connector) {
            return {error: "connector unavailable for platformKind " + platformKind};
        }

        if (check) {
            let permalink = await dbConnector.getPermalinkDataFromResourceID({platformKind, platformSiteID, platformArchiveID, resourceID, branchID, permalinkKind});

            if (permalink.permalinkID) {
                return makePermalinkObject(permalink, connector);
            } else {
                return {message: "permalink not existing"};
            }
        }


        if (!permalinkID) {
            let existingPermalink = await dbConnector.getPermalinkDataFromResourceID({platformKind, platformSiteID, platformArchiveID, resourceID, branchID, permalinkKind});  // Check if permalink for specified resource already exists

            if (existingPermalink.permalinkID) {  // Permalink exists
                return makePermalinkObject(existingPermalink, connector);
            }

            // generate uuid for new permalink
            permalinkID = connector.makePermalinkID({platformKind, platformSiteID, platformArchiveID, resourceID, branchID});
        }

        req.bas.logger.info("Setting a permalink with code " + permalinkID);

        const permalinkData = {
            permalinkID,
            platformKind,
            platformSiteID,
            platformArchiveID,
            resourceID,
            branchID,
            dirty: false,
            permalinkKind,
            permalinkInfo,
            platformInfo,
            timestamp: clock.now(),
        };
        try {
            await callWithLegacyCallback(cb => dbConnector.savePermalinkData(
                permalinkData,
                cb,
            ));
        } catch (err) {
            if (err.resultObj.busy) { // Ignore duplicate-entry errors: ER_DUP_ENTRY
                req.bas.logger.error("Duplicated permalink", err);
            } else {
                throw err;
            }
        }
        req.bas.logger.info("Permalink created");

        let permalinkResponse = makePermalinkObject(permalinkData, connector);

        if (permalinkKind === Consts.PermalinkKind.public_share) {
            // nothing to do, for the moment we do not send RTC message
        } else {
            // "image"
            broadcastRTCMessage(archiveID, null, internalUserID, username, {
                operation: actionName,
                branchID,
                resourceID,

                platformKind: platformKind,
                platformArchiveID: platformArchiveID,
                platformSiteID: platformSiteID,
                permalinkUUID: permalinkID
            });
        }
        return permalinkResponse;
    }

    async function setPermalinkAsBASAdmin() {
        if (!(await verifyAdminCredential())) {
            return {error: "wrong credentials"};
        }

        // Trust the platforminfo that's coming from the request
        const platformSiteID = jsonObj.platformSiteID;
        const platformArchiveID = jsonObj.platformArchiveID;
        const platformKind = jsonObj.platformKind;
        let platformInfo;
        try {
            platformInfo = JSON.parse(jsonObj.platformInfo);
        } catch (e) {
            platformInfo = {};
        }

        req.bas.addLoggerInfo({
            platformArchiveID,
            platformSiteID,
            platformKind,
            platformInfo,
        });

        if (!platformArchiveID || !platformSiteID || !platformKind) {
            return {error: "wrong parameters"};
        }

        const connector = getConnector(platformKind);
        if (!connector) {
            return {error: "connector unavailable for platformKind " + platformKind};
        }
        const sessionData = await getSessionData();
        const dbConnector = sessionData.dbConnector;
        const permalinkID = connector.makePermalinkID({platformKind, platformSiteID, platformArchiveID, resourceID, branchID});

        const newPermalinkData = {
            permalinkID,
            platformKind,
            platformSiteID,
            platformArchiveID,
            resourceID,
            branchID,
            dirty: false,
            permalinkKind,
            permalinkInfo,
            platformInfo,
            timestamp: clock.now(),
        };
        await callWithLegacyCallback(cb => dbConnector.savePermalinkData(
            newPermalinkData,
            cb
        ));

        req.bas.logger.info("Permalink created");

        return makePermalinkObject(newPermalinkData, connector);
    }
}

export {
    setPermalink
};
