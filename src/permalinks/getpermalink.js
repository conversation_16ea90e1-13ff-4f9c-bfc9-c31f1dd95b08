import path from 'path';
import { getThumbnailFromDump, pipeToNewRequest } from '../utils.ts';
import { callWithLegacyCallback, makeLegacyResultFromError } from '../calling-style.ts';

async function getPermalink({
    req,
    res,
    sessionManager,
    getSessionData,
    releaseSessionIfNotReleased,
    getStaticFileRepository,
    getConnector,
    createBARFromBuffer,
}) {
    let permalinkID = req.params.id || req.query.ID;

    if (!permalinkID) {
        return {error: "invalid parameters"};
    }

    if (permalinkID.endsWith(".png")) {
        permalinkID = path.basename(permalinkID, ".png");
    } else if (permalinkID.endsWith(".jpg")) {
        permalinkID = path.basename(permalinkID, ".jpg");
    }

    // in case of direct call we need to clean the ID
    if (permalinkID.includes("_thumbnail")) {
        permalinkID = permalinkID.replace("_thumbnail", "");
    }

    const query = req.query.query || req.query.q || "none"; // "exists" || "metadata" || "thumbnail-only" || "none" if not set
    const platformToken = req.query.platformToken;

    req.bas.addLoggerInfo({query, permalinkID, platformToken});

    res.setHeader('Cache-Control', 'no-cache, no-store');

    const sessionData = await getSessionData();
    const dbConnector = sessionData.dbConnector;
    let permalinkData;
    try {
        permalinkData = await callWithLegacyCallback(cb => dbConnector.getPermalinkData(permalinkID, cb));
    } catch (err) {
        if (err.resultObj.notFound) {
            if (query === 'none') { // Request coming from the browser -> answer text
                return permalinks404Response();
            } else {  // Request coming from some integration -> Answer json
                return {error: 'No permalink found', notFound: true};
            }
        }
        throw err;
    }

    const {
        platformKind,
        platformInfo,
        platformSiteID,
        platformArchiveID,
        resourceID,
        branchID,
        permalinkInfo,
        permalinkKind,
        timestamp,
    } = permalinkData;


    const connector = getConnector(platformKind);
    if (!connector) {
        return {error: "connector unavailable for platformKind " + platformKind};
    }
    req.bas.addLoggerInfo({ platformKind, platformSiteID, platformArchiveID, resourceID, branchID});

    if (query === "exists") {
        req.bas.logger.info("Check permalink existance");
        return {permalinkID};
    } else if (query === "none") {
        req.bas.logger.info("Serving permalink image");
        if (!connector || !connector.getPermalink) {
            return statusCodeResponse(501, "Not Implemented");
        }

        // [PUBLIC_SHARE] We allow to retrieve the thumbnail associated to the Public Share link
        // if (permalinkKind !== 'image') {
        //     // Do not serve images for permalinkKind !== 'image'
        //     return permalinks404Response();
        // }

        // Pass the request down to the connector
        try {
            await callWithLegacyCallback(cb => connector.getPermalink({
                sessionData,
                dbConnector,
                permalinkData,
                releaseSessionIfNotReleased,
                req,
                res
            }, cb));
            return; // If connector succeded, we're done
        } catch (err) {
            if (err.resultObj.info) {
                req.bas.logger.info(err.message);
            } else {
                req.bas.logger.error(`Unexpected error in connector.getPermalink: ${err.message}`, err);
            }
            if (res.headersSent) {  // Failed while streaming the data. Cannot fix this anymore.
                res.end();
                return;
            }
        }

        return permalinks404Response();
    } else if (query === "metadata") {
        if (req.query && req.query.agent) {
            req.bas.addLoggerInfo({agent: req.query.agent})
        }
        req.bas.logger.info("Getting permalink metadata");

        let thumbnailInfo, archiveID;
        try {
            ({thumbnailInfo, archiveID} = await getThumbnailAndArchiveID({
                platformKind,
                platformSiteID,
                platformArchiveID,
                platformInfo,
                resourceID,
                branchID
            }, req.bas.logger));
        } catch (err) {
            return makeLegacyResultFromError(err);
        }

        let authenticated = false;
        if (platformToken && connector && connector.authorizedForArchive) {
            try {
                await callWithLegacyCallback(cb => connector.authorizedForArchive(req.bas.logger, sessionData, platformArchiveID, platformSiteID, platformInfo, platformToken, cb));
                authenticated = true;
            } catch(err) {
                authenticated = false;
            }
        }

        if (!authenticated) {
            return {
                ...thumbnailInfo,
                permalinkData: {
                    permalinkID,
                    permalinkKind: permalinkData.permalinkKind,
                    platformKind: permalinkData.platformKind,
                    timestamp,
                    permalinkInfo: {
                        ...permalinkInfo,
                        ...connector.getPermalinkExtraFromPermalinkData(permalinkData),
                    }
                }
            };
        } else {
            return {
                ...thumbnailInfo,
                permalinkData: {
                    ...permalinkData,
                    permalinkInfo: {
                        ...permalinkData.permalinkInfo,
                        ...connector.getPermalinkExtraFromPermalinkData(permalinkData),
                    },
                    rtcInfo: {
                        rtcChannel: archiveID,
                        newRtc: true,
                    },
                }
            };
        }
    } else if (query === "thumbnail-only") {
        req.bas.logger.info("Getting permalink thumbnail");
        let image;
        try {
            image = await connector.asyncGetPermalinkThumbnail(permalinkData, req.bas.logger);
        } catch(err) {
            req.bas.logger.error("No thumbnail found on S3 ", err);
            return statusCodeResponse(404, "Not Found");
        }
        const format = permalinkInfo?.image?.format || 'png';
        return await base64ImageResponse({data: image, format});
    } else {
        req.bas.logger.error("unexpected action: " + query);
        return {error: "unexpected action"};
    }

    // HELPER FUNCTIONS

    async function statusCodeResponse(code, message) {
        req.bas.logger.info(`Response status: ${code} - ${message}`);
        res.status(code).send(message);
    }

    async function permalinks404Response() {
        const staticFileRepository = getStaticFileRepository();
        const url = staticFileRepository + "share/404_image_permalink.html";
        pipeToNewRequest(req, res, {
            url: url,
            incomingResHandler: (incomingRes) => {
                incomingRes.statusCode = 404;
            }
        },
        req.bas.logger,
        (result) => {
            if (result.error) { res.status(404).end(); }
        });
    }

    async function base64ImageResponse({data, filename=null, format='png'}) {
        await releaseSessionIfNotReleased();  // release the session before streaming the image

        const binaryData = Buffer.from(data, 'base64');
        res.setHeader('Content-Type', 'image/' + format);
        res.setHeader('Content-Length', Buffer.byteLength(binaryData));
        if (filename) {
            res.setHeader('content-disposition', `inline; filename=${filename}`);
        }
        res.write(binaryData);
        res.end();
    }

    async function getThumbnailAndArchiveID({platformKind, platformSiteID, platformArchiveID, platformInfo, resourceID, branchID}, logger) {
        let archiveInfo = await callWithLegacyCallback(cb => dbConnector.getBASArchiveIDWithExclusiveRowLock(platformSiteID, platformArchiveID, true, cb));
        let ret;

        if (!archiveInfo.BAS_ARCHIVE_ID) {  // Retrieve the archive id from connector
            ret = await _getThumbnailFromConnector({platformKind, platformSiteID, platformArchiveID, platformInfo, resourceID, branchID});
        } else {
            const archiveID = archiveInfo.BAS_ARCHIVE_ID;
            ret = await _getThumbnailFromBAS({archiveID, platformSiteID, platformArchiveID, platformInfo, resourceID, branchID});
        }
        if (ret.thumbnailInfo.image) {
            // nothing to do
            logger.info("Thumbnail saved in the BMPR");
        } else {
            logger.info("Get thumbnail from S3");
            try {
                ret.thumbnailInfo.image = await connector.asyncGetPermalinkThumbnail(permalinkData, logger);
            } catch (e) {
                logger.error("No thumbnail found on S3: " + e);
            }
        }

        return ret;
    }

    async function _getThumbnailFromConnector({platformKind, platformSiteID, platformArchiveID, platformInfo, resourceID, branchID}) {
        const connector = getConnector(platformKind);
        const authInfo = await callWithLegacyCallback(cb => connector.getAuthTokenFromPlatform(req.bas.logger, platformInfo, cb));
        const platformToken = authInfo.platformToken;

        const archive = await callWithLegacyCallback(cb => connector.loadFromPlatform(req.bas.logger, dbConnector, platformToken, platformSiteID, platformArchiveID, null, platformInfo, cb));
        const archiveID = archive.id;
        const buffer = archive.buffer;

        platformInfo = archive.platformInfo || platformInfo;  // update the platformInfo if changed by the loadFromPlatform
        const bar = await callWithLegacyCallback(cb => createBARFromBuffer(archiveID, buffer, sessionData, platformInfo, platformKind, platformSiteID, platformArchiveID, null, dbConnector, req.bas.logger, cb));

        let thumbnailInfo = await callWithLegacyCallback(cb => getThumbnailFromDump(resourceID, branchID, bar.dump, cb));

        if (!thumbnailInfo.image && bar.dump.Thumbnails.length > 0) {
            ({resourceID, branchID} = bar.dump.Thumbnails[0].ATTRIBUTES);
            thumbnailInfo = await callWithLegacyCallback(cb => getThumbnailFromDump(resourceID, branchID, bar.dump, cb));
        }

        return { thumbnailInfo, archiveID };
    }

    async function _getThumbnailFromBAS({archiveID, platformSiteID, platformArchiveID, resourceID, branchID}) {
        req.bas.addLoggerInfo({archiveID});
        req.bas.logger.info(`permalink accessing an already opened basArchiveID:${archiveID} platformArchiveID:${platformArchiveID} platformSiteID: ${platformSiteID}`);

        try {
            const archive = await callWithLegacyCallback(cb => sessionManager.openBarLocked(sessionData, archiveID, "READ", cb));
            const bar = archive.bar;
            const { attributes } = await callWithLegacyCallback(cb => bar.getArchiveAttributes(cb));
            let thumbnailInfo;
            try {
                thumbnailInfo = await callWithLegacyCallback(cb => bar.getThumbnailFromResourceID(resourceID, branchID, cb));
            } catch (err) {
                // Try again by picking the first thumbnail of the archive
                const { thumbnails } = await callWithLegacyCallback(cb => bar.getThumbnails(cb));
                if (thumbnails.length === 0) {
                    throw new Error(`No Thumbnails found in archive`);
                }
                ({branchID, resourceID} = thumbnails[0].ATTRIBUTES);
                thumbnailInfo = await callWithLegacyCallback(cb => bar.getThumbnailFromResourceID(resourceID, branchID, cb));
            }
            return { thumbnailInfo: {...thumbnailInfo, projectName: attributes.name}, archiveID };
        } finally {
            await callWithLegacyCallback(cb => sessionManager.unlockConnection(sessionData, cb));
        }
    }

}

export {
    getPermalink
};
