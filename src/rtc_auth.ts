/** @prettier */
import type { BASRequest } from './request-context.ts';
import { getValidUserForRole as sane_getValidUserForRole } from './sane-helpers.js';
import con from './constants.ts';
import type { User } from './db_connector.js';
import { getSessionIdFromBearerToken } from './utils.ts';

async function getRtcAuthToken({
    req,
    sessionManager,
    metrics,
    config,
    rateLimiterConfiguration,
    rtcAdapter,
}: {
    req: BASRequest;
    sessionManager: JSDocTypes.SessionManager;
    metrics: JSDocTypes.Metrics;
    config: JSDocTypes.Config;
    rateLimiterConfiguration: any;
    rtcAdapter: JSDocTypes.RtcAdapter;
}) {
    let token;
    try {
        token = getSessionIdFromBearerToken(req);
    } catch (err) {
        return { error: 'Error fetching the session token' };
    }

    const rateLimiterGroup = rateLimiterConfiguration.getRateLimitersGroup(['getRtcAuthToken'], { sessionToken: token }, req.bas.logger);
    const rateLimiterStatus = await rateLimiterGroup.checkExceeded();
    if (rateLimiterStatus.hasExceeded) {
        req.bas.sendStatusCodeJsonResponse(429, {
            error: rateLimiterStatus.publicInfo.publicErrorMessage,
        });
        return;
    }

    const response = await sessionManager.withSession(req.bas.logger, '/' + con.API_GET_RTC_AUTH_TOKEN, async (dbSession) => {
        let user: User;
        try {
            user = await sane_getValidUserForRole({
                token,
                dbConnector: dbSession.dbConnector,
                metrics,
                logger: req.bas.logger,
                role: con.ROLE_VIEWER,
                sessionData: dbSession,
            });
        } catch (error) {
            const err = error as Error & { params?: { busy?: boolean } };
            if (err.params && err.params.busy) {
                return {
                    body: { error: 'Not authenticated' },
                    statusCode: 403,
                };
            } else {
                throw err;
            }
        }

        // Reducing daily almost 500K lines in the logs
        // It is also the only API where we log not anonymized PII (email, full name)
        // req.bas.logger.info('getRtcAuthToken', { action: 'getRtcAuthToken', ...req.bas.getUserSessionInfo(userSession) });

        const {
            jwtCallbackInfo: { basBaseURL, secret },
        } = config.getRtcConfig();
        const { heartbeatIntervalInSec } = await rtcAdapter.getFrontendConfig(req.bas.logger);
        const jwtExtraFields = {
            sessionExpirationCallback: {
                url: basBaseURL + con.API_RTC_USERS_LEFT_ARCHIVE,
                secret,
            },
            heartbeatDurationInSec: heartbeatIntervalInSec,
        };

        // Reducing daily almost 500K lines in the logs
        // req.bas.logger.info('Calling new Lambda-RTC buildRTCTokenInfo');
        const tokenInfo = await rtcAdapter.buildRTCTokenInfo(user.ARCHIVE_ID, req.bas.logger, jwtExtraFields);
        return {
            body: tokenInfo,
            statusCode: 200,
        };
    });
    req.bas.sendStatusCodeJsonResponse(response.statusCode, response.body);
}

export { getRtcAuthToken };
