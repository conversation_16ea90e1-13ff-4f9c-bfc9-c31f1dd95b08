/** @prettier */
import sqlite3 from 'sqlite3';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { inspect } from 'util';
import type { BASLegacyCallback } from './calling-style.ts';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const localDepotPath = __dirname + '/archives/';

// NOTE - Stefano 5 Feb 2020
// -------------------------
//
// sqlite3.verbose();
//
// DO NOT UNCOMMENT THIS LINE!
//
// Activating verbosity in the sqlite3 library means augmenting all APIs
// with extra debugging code that performs a util.inspect call on every
// argument. Starting from Node 12.5.0 on Linux, under certain conditions
// that we weren't able to entirely reproduce in isolation, inspect becomes
// very slow, impacting production performance very badly (2x to 10x the time
// needed to generate a BMPR).

type SQLiteCallback = BASLegacyCallback<{ buffer?: Buffer; rows?: any[]; row?: any; affectedRows?: number }>;

function getArchivePath(archivesPath: string, archiveID: string): string {
    return path.join(archivesPath, archiveID + '.bmpr');
}

class SQLiteAdapter {
    archivesPath: string;
    db!: sqlite3.Database;

    constructor(options: { archivesPath?: string }) {
        this.archivesPath = options.archivesPath || localDepotPath;

        if (!fs.existsSync(this.archivesPath)) {
            fs.mkdirSync(this.archivesPath);
        }
    }

    createFromBuffer(id: string, buffer: Buffer, callback: SQLiteCallback): void {
        const path = getArchivePath(this.archivesPath, id);
        fs.writeFile(path, buffer, (err) => {
            if (err) {
                console.error(err);
                console.error(inspect(err));
                callback({ error: err.message });
            } else {
                this.open(id, 'readWriteOrCreate', callback);
            }
        });
    }

    getBuffer(id: string, callback: SQLiteCallback): void {
        const path = getArchivePath(this.archivesPath, id);
        fs.readFile(path, (err, buffer) => {
            if (err) {
                callback({ error: err.message });
            } else {
                callback({ buffer: buffer });
            }
        });
    }

    destroy(id: string, callback: SQLiteCallback): void {
        const path = getArchivePath(this.archivesPath, id);
        fs.unlink(path, (err) => {
            if (err) {
                callback({ error: err.message });
            } else {
                callback({});
            }
        });
    }

    open(
        id: string,
        openParams: 'readWriteOrCreate' | 'readWrite' | 'read' /* These are defined in @balsamiq/bmpr */,
        callback: SQLiteCallback
    ): void {
        const path = getArchivePath(this.archivesPath, id);
        let mode = 0;
        if (openParams === 'readWriteOrCreate') {
            mode = sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE;
        } else if (openParams === 'readWrite') {
            mode = sqlite3.OPEN_READWRITE;
        } else if (openParams === 'read') {
            mode = sqlite3.OPEN_READONLY;
        }
        this.db = new sqlite3.Database(path, mode, (err) => {
            if (err) {
                console.error(err);
                console.error(inspect(err));
                callback({ error: err.message });
            } else {
                callback({});
            }
        });
    }

    serialize(id: string, callback: SQLiteCallback): void {
        this.db.serialize(() => {
            callback({});
        });
    }

    run(id: string, query: string, bind: any[], callback: SQLiteCallback): void {
        this.db.run(query, bind, (err) => {
            if (err) {
                callback({ error: err.message });
            } else {
                this.db.all('SELECT CHANGES();', [], (err, rows) => {
                    //@ts-ignore
                    callback({ affectedRows: rows[0]['CHANGES()'] });
                });
            }
        });
    }

    all(id: string, query: string, bind: any[], callback: SQLiteCallback): void {
        this.db.all(query, bind, (err, rows) => {
            if (err) {
                callback({ error: err.message });
            } else {
                callback({ rows: rows });
            }
        });
    }

    get(id: string, query: string, bind: any[], callback: SQLiteCallback): void {
        this.db.get(query, bind, (err, row) => {
            if (err) {
                callback({ error: err.message });
            } else {
                callback({ row: row });
            }
        });
    }

    close(id: string, callback: SQLiteCallback): void {
        this.db.close((err) => {
            if (err) {
                callback({ error: err.message });
            } else {
                callback({});
            }
        });
    }
}

export { SQLiteAdapter };
