import { ArchiveCommentAttributes, ArchiveCommentData, ArchiveUserAttributes } from "./BalsamiqArchiveDataFormat";

export type ErrorRes = {
    error?: string;
}

type ArchiveRevisionRes = {
    archiveRevision: number;
}

type SetResourceAttributesRes = ArchiveRevisionRes;

type GetResourceDataRes = {
    data: string;
    archiveRevision: string;
}

type SetResourceDataRes = ArchiveRevisionRes;

type DeleteResourcesRes = {
    heuristicArchiveSize: number;
    archiveRevision: number;
}

type GetResourcesDataRes = {
    data: { [resourceID_PIPE_branchID: string]: string };
    archiveRevision: number;
}

type CreateResourceRes = {
    ID: string;
    archiveRevision: number;
    heuristicArchiveSize: number;
}

type CreateCommentRes = {
    commentID: string;
    resourceID: string;
    branchID: string;
    userID: string;
    attributes: ArchiveCommentAttributes;
    archiveRevision: number;
}

type UpdateCommentsAttributesRes = {
    attributes: { 
        commentID: string;
        attributes: ArchiveCommentAttributes;
    }[];
    archiveRevision: number;
}

type SetCommentDataRes = {
    attributes: ArchiveCommentAttributes;
    data: string;
    archiveRevision: number;
}

type CreateBranchRes = ArchiveRevisionRes

type SetBranchAttributesRes = ArchiveRevisionRes

type DeleteBranchesRes = ArchiveRevisionRes;

type DeleteCommentsRes = ArchiveRevisionRes;

type CreateThumbnailRes = {
    ID: string;
    archiveRevision: number;
}

type UpdateMyUserRes = {
    affectedRows: number;
    archiveRevision: number;
}

export interface BalsamiqArchiveBase {
    isLocal(): boolean;
    
    getResourceData(resourceID: string, branchID: string, callback: (res: GetResourceDataRes | ErrorRes) => void): void;
    getResourcesData(resourceID: string[], branchID: string, callback: (res: GetResourcesDataRes | ErrorRes) => void): void;
    setResourceAttributes(resourceID: string, branchID: string, attributes: unknown, callback: (res: SetResourceAttributesRes) => void): void;
    createResource(resourceID: string, branchID: string, attributes: unknown, data: unknown, callback: (res: CreateResourceRes) => void): void;
    deleteResources(resourceIDs: string[], branchID: string, callback: (res: DeleteResourcesRes | ErrorRes) => void): void;
    setResourceData(resourceID: string, branchID: string, data: string, callback: (res: SetResourceDataRes) => void): void;
    setResourceBranchID(resourceID: string, oldBranchID: string, newBranchID: string, callback: (res: ErrorRes | {}) => void): void;

    getCommentsData(commentIDs: string[], callback: (res: ErrorRes | { data?: Record<string, unknown>, comments: ArchiveCommentData[], archiveRevision: number }) => void): void;
    createComment(commentID: string, resourceID: string, branchID: string, parentID: string, data: unknown, callback: (res: CreateCommentRes | ErrorRes) => void): void;
    importComment(resourceID: string, branchID: string, comment: Record<string, unknown>, callback: (res: ErrorRes | { commentID: string, attributes: ArchiveCommentAttributes }) => void): void;
    updateCommentsAttributes(commentIDs: string[], attributes: unknown[], callback: (res: UpdateCommentsAttributesRes) => void): void;
    setCommentData(commentID: string, data: unknown, callback: (res: SetCommentDataRes) => void): void;
    deleteComments(commentIDs: string[], callback: (res: DeleteCommentsRes) => void): void;
    
    createBranch(branchID: string, attributes: unknown, callback: (res: CreateBranchRes) => void): void;
    setBranchAttributes(branchID: string, attributes: unknown, callback: (res: SetBranchAttributesRes) => void): void;
    deleteBranches(branchIDs: string[], callback: (res: DeleteBranchesRes) => void): void;
    
    getThumbnail(thumbnailID: string, callback: (res: {error?: Error, ATTRIBUTES: unknown }) => void): void;
    setThumbnail(thumbnailID: string, attributes: unknown , callback: (res: ErrorRes | {}) => void): void;
    createThumbnail(thumbnailID: string, attributes: unknown, callback: (res: CreateThumbnailRes) => void): void;
    deleteThumbnails(thumbnailIDs: string[], callback: (res: ErrorRes | {}) => void): void;
    
    setArchiveAttributes(attributes: unknown, callback: (res: ErrorRes | {}) => void): void;
    getArchiveRevision(callback: (res: ErrorRes | ArchiveRevisionRes) => void): void;
    
    createMyUser(archiveUserInfo: Record<string, unknown>, callback: (res: ErrorRes | { userID: string}) => void): void;
    updateMyUser(archiveUserInfo: Record<string, unknown>, callback: (res: UpdateMyUserRes) => void): void;
    getArchiveUsersList(callback: (res: ErrorRes | { users: { ID: string, ATTRIBUTES: ArchiveUserAttributes }[]}) => void): void;
    
    download(callback: (res: ErrorRes | ArrayBuffer) => void, progressCallback?: (isComplete: boolean, loaded: number, total: number) => void): void;
}
