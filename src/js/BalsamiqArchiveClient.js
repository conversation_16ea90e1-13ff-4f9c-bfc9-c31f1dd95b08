/*global Uint8Array, */
//Documentation: https://balsamiq.atlassian.net/wiki/display/intranet/Balsamiq+Archive+Master+API+list

var Consts = require('./BalsamiqArchiveConstants');

var urlEncodeObject = function(obj) {
    var str = [];
    for(var p in obj)
        if (obj.hasOwnProperty(p)) {
            str.push(encodeURIComponent(p) + "=" + encodeURIComponent(obj[p]));
    }
    return str.join("&");
};

var parseHeaders = function(headers) {
    var arr = headers.trim().split(/[\r\n]+/);

    var headerMap = {};
    arr.forEach(function (line) {
      var parts = line.split(': ');
      var header = parts.shift();
      var value = parts.join(': ');
      headerMap[header] = value;
    });

    return headerMap;
}

var defaultNetworkApi = function(method, url, data, callback, timeouts, settings, requestCreatedCallback) {
    var xhr, parseError, responseTimeoutTimer;

    if (method === 'GET_SYNC') {
        method = 'GET';  // Async request are deprecated (and blocked by some browsers
    }

    xhr = new XMLHttpRequest();

    if (settings && settings.binary) {
        xhr.responseType = 'arraybuffer';
    }

    xhr.onreadystatechange = function() {
        var responseObj;

        if (xhr.readyState >= 2 && responseTimeoutTimer) {
            clearTimeout(responseTimeoutTimer);
        }

        if (xhr.readyState !== XMLHttpRequest.DONE) {
            return; // not interested in other events
        }

        if (settings && settings.binary) {
            // get binary data as a response
            if (settings.base64) {
                responseObj = { data : _arrayBufferToBase64(xhr.response) };
            } else {
                responseObj = { data : xhr.response };
            }
        } else {
            try {
                responseObj = JSON.parse(xhr.responseText);
            } catch (e) {
                parseError = { error: xhr.status, error_message: 'INVALID JSON RESPONSE CONTENT' };
            }
        }

        if (settings && settings.progressCallback) {
            xhr.onprogress = function (e) {
                if (e.lengthComputable) {
                    settings.progressCallback(true, e.loaded, e.total);
                } else {
                    settings.progressCallback(false);
                }
            };
        }

        var headers = parseHeaders(xhr.getAllResponseHeaders());
        if (parseError) {
            callback(parseError, headers);
        } else {
            if (xhr.status >= 200 && xhr.status < 400) {
                callback(responseObj, headers);
            } else {
                callback({
                    error: xhr.status,
                    error_message: responseObj.message || responseObj.error || "networkApi error"
                }, headers);
            }
        }
    };

    xhr.ontimeout = function timeout(/*e*/) {
        if (responseTimeoutTimer) {
            clearTimeout(responseTimeoutTimer);
        }
        callback({error_message: 'DEADLINE_TIMEOUT', timeout: true});
    };

    xhr.open(method, url);
    if (settings && settings.headers) {
        for (var header in settings.headers) {
            xhr.setRequestHeader(header, settings.headers[header])
        }
    }
    if (timeouts.deadline) xhr.timeout = timeouts.deadline;
    if (settings && settings.formData) {
        var formData = new FormData();
        for (var i in data) {
            var field = data[i];
            var name = field.name,
                value = field.value,
                type = field.type,
                mimeType = field.mimeType,
                filename = field.filename;
            if (type === 'base64') {
                var binary = atob(value);
                var array = new Uint8Array(binary.length);
                for(var j = 0; j < binary.length; j++) {
                    array[j] = binary.charCodeAt(j);
                }
                formData.append(name, new Blob([array], {type: mimeType}), filename);
            } else {
                formData.append(name, value);
            }
        }
        xhr.send(formData);
    } else {
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded; charset=UTF-8');
        xhr.setRequestHeader('Accept', 'application/json');
        xhr.send(urlEncodeObject(data));
    }
    if (timeouts.response) {
        responseTimeoutTimer = setTimeout(function responseTimeout() {
            xhr.abort();
            callback({ error_message: 'RESPONSE_TIMEOUT', timeout: true });
        }, timeouts.response);
    }
    requestCreatedCallback && requestCreatedCallback(xhr);
};

var defaultTimerApi = {};

defaultTimerApi.setTimeout = function () {
    return setTimeout.apply(null, arguments);
};

defaultTimerApi.clearTimeout = function () {
    clearTimeout.apply(null, arguments);
};

var BalsamiqArchiveClient = function(serverURL, kind, platformToken, userInfo, networkApi, timerApi, timeouts)
{
    this.serverURL = serverURL;
    this.kind = kind;
    this.platformToken = platformToken;
    this.userInfo = userInfo;
    this.networkApi = networkApi ? networkApi : defaultNetworkApi;
    this.timerApi = timerApi ? timerApi : defaultTimerApi;
    this.timeouts = timeouts || { response: null, deadline: null };

    this.token = "";
    this.userID = "";
    this.rtcChannel = "";
    this.archiveRevision = null;

    this.downloadRequest = null;
    this.downloadCallback = null;

    this.intervalKeepAlive = null;
};

function _arrayBufferToBase64(buffer) {
    var binary = '';
    var bytes = new Uint8Array(buffer);
    var len = bytes.byteLength;
    for (var i = 0; i < len; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    return window.btoa( binary );
}

BalsamiqArchiveClient.prototype.isLocal = function() {
    return false;
};

BalsamiqArchiveClient.prototype.getKind = function () {
    return this.kind;
};

BalsamiqArchiveClient.prototype.fetch = function(url, callback)
{
    this._getBinary("fetch", "url=" + encodeURI(url), Consts.BinaryDataType.BASE64, function(data) {
        if(!data.error){
            var file = {
                data: data,
                filename: url, //deprecated
                url: url,
                type: ""
            };

            callback(file);
        } else {
            callback(data);
        }
    });
};

BalsamiqArchiveClient.prototype.create = function(platformSiteID, platformArchiveName, platformInfo, callback)
{
    this.platformInfo = platformInfo;

    var postObj = {
        platformToken:this.platformToken,
        platformSiteID:platformSiteID,
        platformArchiveName:platformArchiveName,
        platformInfo:JSON.stringify(this.platformInfo),
        kind:this.kind
    };

    this.networkApi('POST', this.serverURL+"/create", postObj, function(obj) {
        callback && callback(obj);
    }, this.timeouts);
};

BalsamiqArchiveClient.prototype.createWithPlatformArchiveID = function(platformSiteID, platformArchiveID, platformArchiveName, platformInfo, archiveAttributes, callback)
{
    this.platformInfo = platformInfo;

    var postObj = {
        platformToken: this.platformToken,
        platformSiteID: platformSiteID,
        platformArchiveID: platformArchiveID,
        platformArchiveName: platformArchiveName,
        platformInfo: JSON.stringify(this.platformInfo),
        kind: this.kind
    };
    if (archiveAttributes) {
        postObj.archiveAttributes = JSON.stringify(archiveAttributes);
    }

    this.networkApi('POST', this.serverURL+"/create", postObj, function(obj) {
        callback && callback(obj);
    }, this.timeouts);
};

BalsamiqArchiveClient.prototype.startKeepAlive = function() {
    if (this.intervalKeepAlive) {
        this.timerApi.clearTimeout(this.intervalKeepAlive);
    }
    this.intervalKeepAlive = this.timerApi.setTimeout(this.keepAlive.bind(this), 10 * 60 * 1000);
};

BalsamiqArchiveClient.prototype.keepAlive = function() {
    this.intervalKeepAlive = null;
    this.refreshSession(this.platformToken, function (resp) {
        if (resp.error) {
            //no more requests until a valid response from BAS
            console.log("BalsamiqArchiveClient keepAlive", resp.error);
        } else {
            this.startKeepAlive();
        }
    }.bind(this));
};

BalsamiqArchiveClient.prototype.setPlatformToken = function(token, refreshSession, callback) {
    this.platformToken = token;
    refreshSession = typeof refreshSession !== 'undefined' ? refreshSession : true;

    if (refreshSession && this.platformToken) {
        this.refreshSession(this.platformToken, function (resp) {
            if (resp.error) {
                // clearing the timer, no more requests until a valid response from BAS
                if (this.intervalKeepAlive) {
                    this.timerApi.clearTimeout(this.intervalKeepAlive);
                    this.intervalKeepAlive = null;
                }
            }
            else {
                this.startKeepAlive();
            }

            // returning BAS response to handle errors from the caller
            callback && callback(resp);
        }.bind(this));
    }
};

BalsamiqArchiveClient.prototype.openByPublicShareID = function(publicShareID, options, callback)
{
    var branchID = (options && options.branchID) ? options.branchID : Consts.Branch.MasterBranchID;
    var skipThumbnailImage = options && options.skipThumbnailImage;

    var postObj = {
        publicShareID: publicShareID,
        branchID: branchID,
        skipThumbnailImage: skipThumbnailImage,
        userInfo:JSON.stringify(this.userInfo),
        platformInfo:JSON.stringify(this.platformInfo),
        platformToken:this.platformToken,
        kind:this.kind
    };

    this.networkApi('POST', this.serverURL+"/open", postObj, function(obj)
    {
        if (obj.error) {
            callback && callback(obj);
        } else {
            this.archiveRevision = obj.dump && obj.dump.Info && obj.dump.Info[Consts.Info.ArchiveRevision];
            this.token = obj.token;
            this.userID = obj.userID;
            this.rtcChannel = obj.rtcChannel;

            delete obj.token;
            this.startKeepAlive();
            callback && callback(obj);
        }
    }.bind(this), this.timeouts);
};

BalsamiqArchiveClient.prototype.open = function(platformSiteID, platformArchiveID, platformArchiveName, platformInfo, permissions, options, callback)
{
    var branchID = (options && options.branchID) ? options.branchID : Consts.Branch.MasterBranchID;
    var skipThumbnailImage = options && options.skipThumbnailImage;

    this.platformInfo = platformInfo;

    var postObj = {
        platformToken:this.platformToken,
        platformSiteID:platformSiteID,
        platformArchiveID:platformArchiveID,
        platformArchiveName:platformArchiveName,
        platformInfo:JSON.stringify(this.platformInfo),
        userInfo:JSON.stringify(this.userInfo),
        permissions: permissions,
        kind:this.kind,
        branchID: branchID,
        skipThumbnailImage: skipThumbnailImage
    };

    this.networkApi('POST', this.serverURL+"/open", postObj, function(obj)
    {
        if (obj.error) {
            callback && callback(obj);
        } else {
            this.archiveRevision = obj.dump && obj.dump.Info && obj.dump.Info[Consts.Info.ArchiveRevision];
            this.token = obj.token;
            this.userID = obj.userID;
            this.rtcChannel = obj.rtcChannel;
            this.basArchiveID = obj.basArchiveID;

            //Delete internal things
            // delete obj.userID; // we need the userID in order to correctly instantiate the rtc channel (e.g. presence)
            delete obj.token;
            this.startKeepAlive();
            callback && callback(obj);
        }
    }.bind(this), this.timeouts);
};

BalsamiqArchiveClient.prototype.openFromServer = function(platformSiteID, platformArchiveID, platformInfo, options, callback) {
    const branchID = (options && options.branchID) ? options.branchID : Consts.Branch.MasterBranchID;
    const skipThumbnailImage = options && options.skipThumbnailImage;
    const authorizationHeaderString = options.authorizationHeaderString; // e.g. "Basic xxx", where xxx is the base64 of the string "username: password"

    this.platformInfo = platformInfo;

    const postObj = {
        platformToken: this.platformToken,
        platformSiteID: platformSiteID,
        platformArchiveID: platformArchiveID,
        platformInfo: JSON.stringify(this.platformInfo),
        kind: this.kind,
        branchID: branchID,
        skipThumbnailImage: skipThumbnailImage
    };

    this.networkApi('POST', this.serverURL + "/openFromServer", postObj, function (obj) {
        if (obj.error) {
            callback && callback(obj);
        } else {
            this.archiveRevision = obj.dump && obj.dump.Info && obj.dump.Info[Consts.Info.ArchiveRevision];
            this.token = obj.token;
            this.userID = obj.userID;
            this.rtcChannel = obj.rtcChannel;
            this.basArchiveID = obj.basArchiveID;

            // Delete internal things
            // delete obj.userID; // we need the userID in order to correctly instantiate the rtc channel (e.g. presence)
            delete obj.token;
            this.startKeepAlive();
            callback && callback(obj);
        }
    }.bind(this),
    this.timeouts,
    // settings
    {
        // authentication header
        headers: {
            'Authorization': authorizationHeaderString
        }
    });
};

BalsamiqArchiveClient.prototype.createSnapshot = function(platformSiteID, platformArchiveID, options, callback) {
    if (!options.authorizationHeaderString) {
        callback({error: "you must provide an authorizationHeaderString"})
    }

    this.networkApi('POST', this.serverURL + "/createSnapshot", {
            platformSiteID: platformSiteID,
            platformArchiveID: platformArchiveID,
            platformInfo: options.platformInfo,
            platformKind: this.kind,
            branchID: options.branchID ? options.branchID : "",
            resourceID: options.resourceID ? options.resourceID : ""
        }, function (obj) {
            callback && callback(obj);
        },
        this.timeouts,
        // settings
        {
            // authentication header
            headers: {
                'Authorization': options.authorizationHeaderString // e.g. "Basic xxx", where xxx is the base64 of the string "username: password"
            }
        });
};

BalsamiqArchiveClient.prototype.unloadArchive = function(platformSiteID, platformArchiveID, permissions, callback)
{
    var postObj = {
        platformToken: this.platformToken,
        platformSiteID: platformSiteID,
        platformArchiveID: platformArchiveID,
        userInfo: JSON.stringify(this.userInfo),
        permissions: permissions,
        kind: this.kind
    };

    this.networkApi('POST', this.serverURL+"/unloadArchive", postObj, function(obj) {
        callback && callback(obj);
    }, this.timeouts);
};

BalsamiqArchiveClient.prototype.unloadArchiveIfNotSync = function(platformSiteID, platformArchiveID, permissions, modifiedTimestamp, callback)
{
    var postObj = {
        platformToken: this.platformToken,
        platformSiteID: platformSiteID,
        platformArchiveID: platformArchiveID,
        userInfo: JSON.stringify(this.userInfo),
        permissions: permissions,
        modifiedTimestamp: modifiedTimestamp,
        kind: this.kind
    };

    this.networkApi('POST', this.serverURL+"/unloadArchiveIfNotSync", postObj, function(obj) {
        callback && callback(obj);
    }, this.timeouts);
};

BalsamiqArchiveClient.prototype.close = function(callback)
{
    if (this.intervalKeepAlive) {
        this.timerApi.clearTimeout(this.intervalKeepAlive);
        this.intervalKeepAlive = null;
    }

    if (this.token) { // empty string, which is the default value, means the archive didn't open
        this._get("close", null, callback);
    } else {
        callback({});
    }
};

BalsamiqArchiveClient.prototype.closeAsyncNoFlush = function(callback)
{
    this.timerApi.clearTimeout(this.intervalKeepAlive);
    this._get("close", "noflush=true", callback);
};

BalsamiqArchiveClient.prototype.closeSync = function(callback)
{
    if (this.intervalKeepAlive) {
        this.timerApi.clearTimeout(this.intervalKeepAlive);
        this.intervalKeepAlive = null;
    }
    this._getSync("close", null, callback);
};

BalsamiqArchiveClient.prototype.closeWithBeacon = function()
{
    // This API should be used only when the editor is running in the browser
    // and a "beforeunload" event occurs.
    //
    // It uses the "sendBeacon" api to send the close event to BAS, without
    // blocking the browser with a synchronous xhr (that is deprecated).
    //
    // more at https://developer.mozilla.org/en-US/docs/Web/API/Navigator/sendBeacon
    //
    // If current browser doesn't support the sendBeacon API (like IE), "false" is returned.
    // The caller should always check for the return value and eventually call
    // the deprecated closeSync method when closeWithBeacon returns false.

    var url = this.serverURL+"/close?token="+this.token;
    var requestQueued = !!(
        typeof window !== 'undefined' &&
        window.navigator &&
        window.navigator.sendBeacon &&
        window.navigator.sendBeacon(url, null)
    );

    return requestQueued;
};

BalsamiqArchiveClient.prototype.closeArchiveWithoutFlushing = function(callback)
{
    if (this.intervalKeepAlive) {
        this.timerApi.clearTimeout(this.intervalKeepAlive);
        this.intervalKeepAlive = null;
    }
    this._getSync("close", "noflush=true", callback);
};

BalsamiqArchiveClient.prototype.flush = function(platformArchiveName, callback)
{
    var params = platformArchiveName ? "platformArchiveName=" + platformArchiveName : null;
    this._get("flush", params, callback);
};

BalsamiqArchiveClient.prototype.flushExt = function(opts, callback)
{
    var params = "", i;
    var keys = Object.keys(opts), key;

    for (i=0; i<keys.length; i++) {
        key = keys[i];
        params += (i ? "&" : "") + (key + "=" + opts[key]);
    }
    this._get("flush", params, callback);
};

BalsamiqArchiveClient.prototype.deleteArchive = function(platformSiteID, platformArchiveID, callback)
{
    var postObj = {
        platformToken:this.platformToken,
        platformSiteID:platformSiteID,
        platformArchiveID:platformArchiveID,
        kind:this.kind
    };

    this.networkApi('POST', this.serverURL+"/delete", postObj, function(obj) {
        callback && callback(obj);
    }, this.timeouts);
};

BalsamiqArchiveClient.prototype.deleteArchiveSync = function(platformSiteID, platformArchiveID, callback)
{
    var postObj = {
        platformToken:this.platformToken,
        platformSiteID:platformSiteID,
        platformArchiveID:platformArchiveID,
        kind:this.kind
    };

    this.networkApi('POST', this.serverURL+"/delete?token="+this.token, postObj, function (result) {
        if (result.error) {
            var message = result.error_message ? ("POST sync call failed " + result.error_message) : "Unknown error";
            callback && callback({error: message});
        } else {
            callback && callback(result);
        }
    }, this.timeouts);
};

BalsamiqArchiveClient.prototype.getRTCAuthToken = function(callback) {
    this.networkApi(
        'GET',
        this.serverURL+"/getRTCAuthToken",
        null,
        function (result) {
            callback(result);
        },
        this.timeouts,
        {
            headers: {
                Authorization: 'Bearer ' + this.token,
            },
        }
    );
};

BalsamiqArchiveClient.prototype.download = function (callback, progressCallback) {
    if (!this.downloadRequest) {
        this.downloadCallback = callback;
        this._getBinary("download", null, Consts.BinaryDataType.ARRAYBUFFER, function (blob) {
            this.downloadRequest = null;
            this.downloadCallback = null;
            callback(blob);
        }.bind(this), progressCallback, function(request) {
            this.downloadRequest = request;
        }.bind(this));
    }
};

BalsamiqArchiveClient.prototype.cancelDownload = function () {
    if (this.downloadRequest) {
        this.downloadRequest.abort(); // this will invoke normal callback with error
    }
};

BalsamiqArchiveClient.prototype.getArchiveFormat = function (callback)
{
    this._get("getArchiveFormat", null, callback);
};

BalsamiqArchiveClient.prototype.setArchiveFormat = function (format, callback)
{
    var postObj = {
        format:format,
        archiveRevision:this.archiveRevision
    };
    this._post("setArchiveFormat", postObj, callback);
};

BalsamiqArchiveClient.prototype.getArchiveRevision = function (callback)
{
    this._get("getArchiveRevision", null, callback);
};

BalsamiqArchiveClient.prototype.getArchiveAttributes = function (callback)
{
    this._get("getArchiveAttributes", null, callback);
};

BalsamiqArchiveClient.prototype.setArchiveAttributes = function (attributes, callback)
{
    var postObj = {
        attributes:JSON.stringify(attributes),
        archiveRevision:this.archiveRevision
    };
    this._post("setArchiveAttributes", postObj, callback);
};

BalsamiqArchiveClient.prototype.getBranches = function(callback)
{
    this._get("getBranches", null, callback);
};

BalsamiqArchiveClient.prototype.getBranchAttributes = function (branchID, callback)
{
    this._get("getBranchAttributes", "branchID="+branchID, callback);
};

BalsamiqArchiveClient.prototype.setBranchAttributes = function (branchID, attributes, callback)
{
    var postObj = {
        branchID:branchID,
        attributes: attributes ? JSON.stringify(attributes) : null,
        archiveRevision:this.archiveRevision
    };
    this._post("setBranchAttributes", postObj, callback);
};

BalsamiqArchiveClient.prototype.createResource = function(resourceID, branchID, attributes, data, callback)
{
    var postObj = {
        resourceID: resourceID,
        branchID:branchID,
        attributes:attributes ? JSON.stringify(attributes) : null,
        data: (typeof data === 'string' ? data : JSON.stringify(data)),
        archiveRevision:this.archiveRevision
    };
    this._post("createResource", postObj, callback);
};

BalsamiqArchiveClient.prototype.createBranch = function(branchID, attributes, callback)
{
    var postObj = {
        branchID: branchID,
        attributes: attributes ? JSON.stringify(attributes) : null,
        archiveRevision: this.archiveRevision
    };

    this._post("createBranch", postObj, callback);
};

BalsamiqArchiveClient.prototype.deleteResources = function(resourceIDs, branchID, callback)
{
    var postObj = {
        resourceIDs: JSON.stringify(resourceIDs),
        branchID:branchID,
        archiveRevision:this.archiveRevision
    };
    this._post("deleteResources", postObj, callback);
};

BalsamiqArchiveClient.prototype.deleteBranches = function(branchIDs, callback)
{
    var postObj = {
        branchIDs: JSON.stringify(branchIDs),
        archiveRevision: this.archiveRevision
    };
    this._post("deleteBranches", postObj, callback);
};

BalsamiqArchiveClient.prototype.getResourceAttributes = function(resourceID, branchID, callback)
{
    this._get("getResourceAttributes", "resourceID="+resourceID+"&branchID="+branchID, callback);
};

BalsamiqArchiveClient.prototype.setResourceAttributes = function (resourceID, branchID, attributes, callback)
{
    var postObj = {
        resourceID:resourceID,
        branchID:branchID,
        attributes:JSON.stringify(attributes),
        archiveRevision:this.archiveRevision
    };

    this._post("setResourceAttributes", postObj, callback);
};

BalsamiqArchiveClient.prototype.getTOC = function(branchID, callback)
{
    this._get("getTOC", "branchID="+branchID, callback);
};

BalsamiqArchiveClient.prototype.getResourceData = function(resourceID, branchID, callback)
{
    this.getResourceDataWithOptions(resourceID, branchID, {nocache: false}, callback);
};

BalsamiqArchiveClient.prototype.getResourceDataWithOptions = function(resourceID, branchID, options, callback)
{
    if (options.nocache || !this.basArchiveID) {
        this._get("getResourceData", "resourceID="+resourceID+"&branchID="+branchID, callback);
    } else {
        this.networkApi(
            'GET',
            this.serverURL+"/getResourceData?"+"basArchiveID="+this.basArchiveID+"&resourceID="+resourceID+"&branchID="+branchID,
            null,
            function (result, headers) {
                var revisionHeader = headers && (headers['x-balsamiq-archive-revision'] || headers['X-Balsamiq-Archive-Revision']);
                if (revisionHeader) {
                    result.archiveRevision = revisionHeader;
                }
                this._saveArchiveRevisionAndReturn(result, callback);
            }.bind(this),
            this.timeouts,
            {
                headers: {
                    Authorization: 'Bearer ' + this.token
                }
            }
        );
    }
};

BalsamiqArchiveClient.prototype.getResourcesData = function (resourceIDs, branchID, callback)
{
    var postObj = {
        resourceIDs:JSON.stringify(resourceIDs),
        branchID:branchID,
        archiveRevision:this.archiveRevision
    };

    this._post("getResourcesData", postObj, callback);
};

BalsamiqArchiveClient.prototype.setResourceData = function (resourceID, branchID, data, callback)
{
    var postObj = {
        resourceID:resourceID,
        branchID:branchID,
        data:data,
        archiveRevision:this.archiveRevision
    };
    this._post("setResourceData", postObj, callback);
};

BalsamiqArchiveClient.prototype.setResourceBranchID = function(resourceID, oldBranchID, newBranchID, callback)
{
    var postObj = {
        resourceID: resourceID,
        oldBranchID: oldBranchID,
        newBranchID:  newBranchID,
        archiveRevision: this.archiveRevision
    };

    this._post("setResourceBranchID", postObj, callback);
};

BalsamiqArchiveClient.prototype.touchResource = function (attributes, callback) {
    var postObj = {
        attributes: JSON.stringify(attributes),
        archiveRevision:this.archiveRevision
    };

    this._post("touchResource", postObj, callback);
};

BalsamiqArchiveClient.prototype.createThumbnail = function(thumbnailID, attributes, callback) {
    var postObj = {
        thumbnailID:     thumbnailID,
        attributes:      JSON.stringify(attributes),
        archiveRevision: this.archiveRevision
    };
    this._post("createThumbnail", postObj, callback);
};

BalsamiqArchiveClient.prototype.getThumbnail = function (thumbnailID, callback) {
    this._get("getThumbnail", "thumbnailID="+thumbnailID, callback);
};

BalsamiqArchiveClient.prototype.setThumbnail = function (thumbnailID, attributes, callback) {
    var postObj = {
        thumbnailID: thumbnailID,
        attributes: JSON.stringify(attributes),
        archiveRevision:this.archiveRevision
    };

    this._post("setThumbnail", postObj, callback);
};

BalsamiqArchiveClient.prototype.deleteThumbnails = function(thumbnailIDs, callback) {

    var postObj = {
        thumbnailIDs: JSON.stringify(thumbnailIDs),
        archiveRevision: this.archiveRevision
    };

    this._post("deleteThumbnails", postObj, callback);
};

BalsamiqArchiveClient.prototype.createComment = function(commentID, resourceID, branchID, commentParentID, data, callback)
{
    var postObj = {
        commentID: commentID,
        resourceID: resourceID,
        branchID:branchID,
        commentParentID: commentParentID,
        data: (typeof data === 'string' ? data : JSON.stringify(data)), // data should be a string
        archiveRevision:this.archiveRevision
    };
    this._post("createComment", postObj, callback);
};

BalsamiqArchiveClient.prototype.importComment = function (resourceID, branchID, comment, callback) {
    var postObj = {
        resourceID: resourceID,
        branchID: branchID,
        comment: JSON.stringify(comment),
        archiveRevision: this.archiveRevision,
    };
    this._post("importComment", postObj, callback);
};

BalsamiqArchiveClient.prototype.getCommentsData = function(commentIDs, callback)
{
    var postObj = {
        commentIDs:JSON.stringify(commentIDs),
        archiveRevision:this.archiveRevision
    };

    this._post("getCommentsData", postObj, callback);
};

BalsamiqArchiveClient.prototype.setCommentData = function (commentID, data, callback)
{
    var postObj = {
        commentID:commentID,
        data: data,
        archiveRevision:this.archiveRevision
    };
    this._post("setCommentData", postObj, callback);
};

BalsamiqArchiveClient.prototype.updateCommentsAttributes = function(commentIDs, attributes, callback) {

    var postObj = {
        commentIDs:JSON.stringify(commentIDs),
        attributes:JSON.stringify(attributes),
        archiveRevision:this.archiveRevision
    };

    this._post("updateCommentsAttributes", postObj, callback);
};

BalsamiqArchiveClient.prototype.deleteComments = function(commentIDs, callback)
{
    var postObj = {
        commentIDs: JSON.stringify(commentIDs),
        archiveRevision:this.archiveRevision
    };
    this._post("deleteComments", postObj, callback);
};

BalsamiqArchiveClient.prototype.getArchiveUsersList = function(callback)
{
    this._get("getArchiveUsersList", null, callback);
};

BalsamiqArchiveClient.prototype.refreshSession = function (platformToken, callback)
{
    var postObj = {
        platformToken: platformToken
    };
    this._post("refreshSession", postObj, callback);
};

BalsamiqArchiveClient.prototype.getUsersList = function(option, callback)
{
    var query = option ? "option=" + option : "";
    this._get("getUsersList", query, callback);
};

BalsamiqArchiveClient.prototype.broadcastMessage = function (obj, callback)
{
    var postObj = {
        message: JSON.stringify(obj)
    };
    this._post("broadcastMessage", postObj, callback);
};

BalsamiqArchiveClient.prototype.getUserInfo = function(internalID, callback)
{
    this._get("getUserInfo", "internalID="+internalID, callback);
};

BalsamiqArchiveClient.prototype.updateUserInfo = function(userInfo, callback) {
    this._post("updateUserInfo", userInfo, callback);
};

BalsamiqArchiveClient.prototype.createMyUser = function(archiveUserInfo, callback) {

    var postObj = {
        archiveRevision:this.archiveRevision
    };

    if (archiveUserInfo)
        postObj.updatedUserInfo = JSON.stringify(archiveUserInfo);

    this._post("createMyUser", postObj, callback);
};

BalsamiqArchiveClient.prototype.updateMyUser = function(archiveUserInfo, callback) {
    var postObj = {
        archiveRevision:this.archiveRevision
    };

    if (archiveUserInfo) {
        postObj.updatedUserInfo = JSON.stringify(archiveUserInfo);
    }

    this._post("updateMyUser", postObj, callback);
};

BalsamiqArchiveClient.prototype.uploadTempFile = function(data, callback)
{
    this._post("uploadTempFile", data, callback);
};

BalsamiqArchiveClient.prototype.downloadTempFile = function(key, callback)
{
    var url = this.serverURL+"/downloadTempFile?token="+this.token + "&key=" + key;
    callback(url);
};

BalsamiqArchiveClient.prototype.downloadBmprlLink = function(filename, callback)
{
    var url = this.serverURL+"/download?token="+this.token + "&filename=" + filename;
    callback(url);
};

BalsamiqArchiveClient.prototype.logUserEvent = function (userEvent, callback)
{
    var postObj = {
        userEvent: JSON.stringify(userEvent)
    };
    this._post("logUserEvent", postObj, callback);
};

BalsamiqArchiveClient.prototype.getProjectMembers = function (data, callback)
{
    var postObj = {
        data: JSON.stringify(data)
    };
    this._post("getProjectMembers", postObj, callback);
};

// permalinkInfo.permalinkID
// permalinkInfo.permalinkKind
BalsamiqArchiveClient.prototype.setPermalinkData = function (resourceID, branchID, permalinkInfo, callback)
{
    var permalinkKind = permalinkInfo.permalinkKind || Consts.PermalinkKind.image;
    delete permalinkInfo.permalinkKind;
    var postObj = {
        resourceID: resourceID,
        branchID: branchID,
        permalinkKind: permalinkKind,
        permalinkInfo: JSON.stringify(permalinkInfo)
    };
    // this._post("permalink", postObj, callback);
    this._post("setPermalinkData", postObj, callback);
};

BalsamiqArchiveClient.prototype.getPermalinkData = function (resourceID, branchID, callback) {
    //this function is used only for the permalink kind image case, in future could be needed a refactoring to make it general purpose
    this._get("getPermalinkData", "resourceID=" + resourceID + "&branchID=" + branchID + "&permalinkKind=" + Consts.PermalinkKind.image, callback);
};

BalsamiqArchiveClient.prototype.getPermalinkDataByPermalinkID = function (permalinkID, callback) {
    this._get("getPermalinkData", "permalinkID=" + permalinkID, callback);
};

BalsamiqArchiveClient.prototype.getPermalinksData = function (callback) {
    this._get("getPermalinksData", null, callback);
};

BalsamiqArchiveClient.prototype.deletePermalinkData = function (resourceID, branchID, callback) {
    //this function is used only for the permalink kind image case, in future could be needed a refactoring to make it general purpose
    var postObj = {
        resourceID: resourceID,
        branchID: branchID,
        permalinkKind: Consts.PermalinkKind.image
    };
    this._post("deletePermalinkData", postObj, callback);
};

BalsamiqArchiveClient.prototype.deletePermalinkDataByPermalinkID = function (permalinkID, callback)
{
    var postObj = {
        permalinkID: permalinkID
    };
    this._post("deletePermalinkData", postObj, callback);
};

BalsamiqArchiveClient.prototype.createOrUpdateImageLink = function (resourceID, branchID, permalinkInfo, base64data, mimeType, callback)
{
    var params = urlEncodeObject({resourceID: resourceID, branchID: branchID});
    // CAVEAT: Order of fields matters in multipart/formdata encoding. Do not shuffle!
    var fields = [{
        name: 'branchID',
        value: branchID,
    },{
        name: 'resourceID',
        value: resourceID,
    }];
    if (permalinkInfo) {
        fields.push({
            name: 'permalinkInfo',
            value: JSON.stringify(permalinkInfo || {}),
        })
    }
    if (base64data) {
        fields.push({
            name: 'image',
            value: base64data,
            type: 'base64',
            mimeType: mimeType
        })
    }
    this._postFormData("createOrUpdateImageLink", fields, params, callback);
};

// if permalink exists return the connected links
// {
//     "image": "https://bas.ngrok.io/p/gntnmnLZUQA4ubtQfnzCTh.png",
//     "edit": "https://balsamiq-ngrok.atlassian.net/browse/TEST-23"
// }
BalsamiqArchiveClient.prototype.checkPermalink = function (resourceID, branchID, callback) {
    var postObj = {
        resourceID: resourceID,
        branchID: branchID,
        // check if the permalink already exists
        check: true
    };
    this._post("permalink", postObj, callback);
};

// TODO DEPRECATED: use setPermalinkData instead
BalsamiqArchiveClient.prototype.setPermalink = function (resourceID, branchID, permalinkInfo, callback) {
    this.setPermalinkData(resourceID, branchID, permalinkInfo, callback);
};

// TODO: DEPRECATED: use checkPermalink instead
BalsamiqArchiveClient.prototype.getPermalink = function (resourceID, branchID, callback) {
    this.checkPermalink(resourceID, branchID, callback);
};

BalsamiqArchiveClient.prototype.getUserAuthToken = function(callback)
{
    // return {
    //     userAuthToken: userAuthToken
    // }
    this._post("getUserAuthToken", null, callback);
};

BalsamiqArchiveClient.prototype._get = function(apiURL, params, callback)
{
    this.networkApi('GET', this.serverURL+"/"+apiURL+"?token="+this.token+(params ? "&"+params : ""), null, function (result) {
        this._saveArchiveRevisionAndReturn(result, callback);
    }.bind(this), this.timeouts);
};

BalsamiqArchiveClient.prototype._getBinary = function(apiURL, params, responseType, callback, progressCallback, requestCreatedCallback)
{
    this.networkApi(
        'GET',
        this.serverURL + "/" + apiURL + "?token=" + this.token + (params ? "&" + params : ""),
        null,
        function (result) {
            if (!result.error) {
                callback(result.data);
            } else {
                callback({ error: "error retrieving the binary: " + result.error, aborted: result.aborted });
            }
        }.bind(this),
        this.timeouts,
        {
            binary : true,
            base64 : responseType == Consts.BinaryDataType.BASE64,
            progressCallback : progressCallback
        },
        function (request) {
            if (typeof(request.abort) !== 'function') {
                throw new Error("Error in getBinary: networkApi returned a request with no abort() function");
            }
            if (requestCreatedCallback) {
                requestCreatedCallback(request);
            }
        });
};

BalsamiqArchiveClient.prototype._getSync = function(apiURL, params, callback)
{
    var url = this.serverURL+"/"+apiURL+"?token="+this.token+(params ? "&"+params : "");
    this.networkApi('GET_SYNC', url, null, function (result) {
        if (result.error) {
            this._saveArchiveRevisionAndReturn(result, callback);
        } else {
            this._saveArchiveRevisionAndReturn({}, callback);
        }
    }.bind(this), this.timeouts);
};

BalsamiqArchiveClient.prototype._post = function(apiName, postObj, callback)
{
    this.networkApi('POST', this.serverURL+"/"+apiName+"?token="+this.token, postObj, function(obj) {
        this._saveArchiveRevisionAndReturn(obj, callback);
    }.bind(this), this.timeouts);
};

BalsamiqArchiveClient.prototype._postFormData = function(apiName, postObj, params, callback)
{
    this.networkApi('POST', this.serverURL+"/"+apiName+"?token="+this.token+(params ? "&"+params : ""), postObj, function(obj) {
        this._saveArchiveRevisionAndReturn(obj, callback);
    }.bind(this), this.timeouts, {formData: true});
};

BalsamiqArchiveClient.prototype._saveArchiveRevisionAndReturn = function(obj, callback)
{
    if (obj.error) {
        callback(obj);
    } else {
        // update the revision if necessary
        if (obj.archiveRevision)
        {
            this.archiveRevision = obj.archiveRevision;
        }
        if (callback) {
            callback(obj);
        }
    }
};


exports.BalsamiqArchiveClient = BalsamiqArchiveClient;
