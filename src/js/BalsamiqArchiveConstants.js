//
exports.Branch = {
    MasterBranchID: 'Master',
    AllBranches: 'All',
    NoBranch: 'None'
};

// Keys in INFO
exports.Info = {
    SchemaVersion: 'SchemaVersion', // integer
    ArchiveFormat: 'ArchiveFormat', // string "bmpr"
    ArchiveRevision: 'ArchiveRevision', // integer
    ArchiveAttributes: 'ArchiveAttributes', // json string
    ArchiveRevisionUUID: 'ArchiveRevisionUUID'
};

exports.ErrorCodes = {
    ArchiveNotOpen: 'ArchiveNotOpen',
    UserUnknown: 'UserUnknown',
    OutOfDate: 'OutOfDate',
    CreateFailed: 'CreateFail',
    SchemaVersionNotSupported: 'SchemaVersionNotSupported',
    NeedToMigrate: 'NeedToMigrate',
    ArchiveNotLoaded: 'ArchiveNotLoaded',
    MaintenanceMode: 'MaintenanceMode'
};

exports.Role = {
    ROLE_NO_ACCESS: 0,
    ROLE_VIEWER: 1,
    ROLE_COMMENTER: 2,
    ROLE_EDITOR: 3,
    ROLE_ADMIN: 4
};

exports.PermalinkKind = {
    image: "image",
    public_share: "public_share",
    image_unfurling: "image_unfurling",
    url_unfurling: "url_unfurling",
}

exports.LikeStatus = {
    like: "like",
    unlike: "unlike"
};

exports.defaultUserInfo = {
    name: "default",
    avatarURL: ""
};

exports.CommentAttributes = {
    ParentID: 'parentID',               // String UUID
    ReadBy: 'readBy',                   // Array of userIDs
    Timestamp: 'timestamp',             // Integer UNIX Time in ms
    Trashed: 'trashed',                 // Boolean
    TrashedBy: "trashedBy",             // userID
    LikedBy: "likedBy",                 // Array of userIDs
    Timestamps: 'timestamps',           // Object in the form { label: timestamp }
    Imported: "imported",               // Boolean
    OriginalAuthor: "originalAuthor"  ,  // string
    Resolved: "resolved",               // Boolean || undefined
    ResolvedBy: "resolvedBy"            // userID || undefined
};

exports.CommentAttributesSetters = {
    setReadStatus: "setReadStatus",                       // Boolean
    setLikedStatus: "setLikedStatus",                     // Boolean
    setTrashedStatus: "setTrashedStatus",                 // Boolean
    setResolvedStatus: "setResolvedStatus",               // Boolean
    setTimestamps: "setTimestamps",                       // array of strings
    deleteTimestamps: "deleteTimestamps",                 // DEPRECATED array of strings
    setParentID: "setParentID",                           // DEPRECATED String
    setTimestamp: "setTimestamp",                         // DEPRECATED Integer UNIX Time in ms
    setImported: "imported",                              // DEPRECATED Boolean
    setOriginalAuthor: "originalAuthor"                   // DEPRECATED string
};

exports.CommentData = {
    Text: "text",
    Callouts: "callouts"
};

// used when importing comments
exports.IMPORTED_USER_ID = "IMPORTED_USER_ID";

exports.BinaryDataType = {
    ARRAYBUFFER: "arraybuffer",
    BASE64: "base64"
};

exports.SchemaVersions = {
    OneDotTwo: "1.2",
    Two: "2.0"
};

exports.CurrentSchemaVersion = exports.SchemaVersions.Two;
