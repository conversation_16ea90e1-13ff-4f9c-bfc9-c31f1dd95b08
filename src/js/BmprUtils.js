var Uri = require('jsuri');

var Consts = require('./BalsamiqArchiveConstants');
var BalsamiqArchiveModule = require('./BalsamiqArchive');

var getMockupsOrderFromDump = function (dump) {
    var mockupsOrder, temp = [], i, l, resource, branchIndex;

    for (i=0; i<dump.Branch.length; i++) {
        if (dump.Branch[i].branchID == Consts.Branch.MasterBranchID) {
            branchIndex = i;
            break;
        }
    }

    if (dump.Branch[branchIndex].ATTRIBUTES.mockupsOrder) {
        mockupsOrder = dump.Branch[branchIndex].ATTRIBUTES.mockupsOrder;
    } else {
        // extract the mockup order from the resources
        l = dump.Resources.length;
        for (i=0; i<l; i++) {
            resource = dump.Resources[i];
            if (resource && resource.BRANCHID == Consts.Branch.MasterBranchID && resource.ATTRIBUTES && !resource.ATTRIBUTES.trashed && resource.ATTRIBUTES.kind === "mockup") {
                temp.push({ID: resource.ID, order: resource.ATTRIBUTES.order});
            }
        }
        temp.sort(function (a, b) {
            var aOrder = a.order ? a.order : 0;
            var bOrder = b.order ? b.order : 0;
            if (aOrder === bOrder) {
                if (a.ID < b.ID) {
                    return -1;
                } else {
                    return 1;
                }
            } else {
                return aOrder - bOrder;
            }
        });

        l = temp.length;
        mockupsOrder = [];
        for (i = 0; i < l; i++) {
            mockupsOrder.push(temp[i].ID);
        }
    }
    return mockupsOrder;
};


var getResourceAttributes = function(dump) {
    var i, attributes = {}, resource;
    for (i=0; i<dump.Resources.length; i++) {
        resource = dump.Resources[i];
        if (resource && resource.BRANCHID == Consts.Branch.MasterBranchID) {
            attributes[resource.ID] = resource.ATTRIBUTES;
        }
    }
    return attributes;
};

var getMockupsOrderFromDumpExt = function (dump) {
    var mockupsOrder, temp = [], i, resource, name;
    var attribute, attributes;

    // extract the mockup order from the resources
    attributes = getResourceAttributes(dump);

    for (i=0; i<dump.Resources.length; i++) {
        resource = dump.Resources[i];
        attribute = attributes[resource.ID];
        if (resource && attribute && !attribute.trashed && attribute.kind === "mockup") {
            name = attribute.name;
            if (resource.BRANCHID != Consts.Branch.MasterBranchID) {
                // TODO: we should use branchName
                name += ' (' + resource.BRANCHID + ')';
            }
            temp.push({ID: resource.ID, order: attribute.order, BRANCHID: resource.BRANCHID, name: name});
        }
    }
    temp.sort(function (a, b) {
        var aOrder = a.order ? a.order : 0;
        var bOrder = b.order ? b.order : 0;
        if (a.ID == b.ID) {
            // alternates
            if (a.name < b.name) {
                // we put first the resource in master and then the associated alternates
                return -1
            } else {
                return 1;
            }
        } else {
            if (aOrder == bOrder) {
                if (a.ID < b.ID) {
                    return -1;
                } else {
                    return 1;
                }
            } else {
                return aOrder - bOrder;
            }
        }
    });

    mockupsOrder = [];
    for (i = 0; i < temp.length; i++) {
        mockupsOrder.push(temp[i].ID + "|" + temp[i].BRANCHID);
    }

    return mockupsOrder;
};


function getResourceAndBranchIDfromInitialResourceAndBranchID(initialResourceAndBranchID) {
    var re = /(.*)\|(.*)/;
    var found = initialResourceAndBranchID.match(re);
    var resourceID, branchID;

    if (found.length == 3) {
        resourceID = found[1];
        branchID = found[2];
    }
    return {resourceID: resourceID, branchID: branchID};
}


var getFirstMockupThumbnail = function(dump) {
    var i, thumb, mockupsOrder, resourceID;

    mockupsOrder = getMockupsOrderFromDump(dump);
    if (mockupsOrder.length) {
        for (i=0; i<dump.Resources.length; i++) {
            if (dump.Resources[i].ID == mockupsOrder[0] && dump.Resources[i].BRANCHID == Consts.Branch.MasterBranchID) {
                resourceID = dump.Resources[i].ID;
                break;
            }
        }

        for (i=0; i<dump.Thumbnails.length; i++) {
            if (dump.Thumbnails[i].ATTRIBUTES.resourceID == resourceID) {
                thumb = dump.Thumbnails[i].ATTRIBUTES.image;
                break;
            }
        }
    }
    return thumb;
};

var getResourceIDfromName = function(name, dump, branchID) {
    // search if the dump contain a mockup with the specified name
    var i, resourceID = null, resource;
    var attributes, attribute;

    attributes = getResourceAttributes(dump);

    branchID = branchID || Consts.Branch.MasterBranchID;
    for (i=0; i<dump.Resources.length; i++) {
        resource = dump.Resources[i];
        attribute = attributes[resource.ID];
        if (resource.BRANCHID ==  branchID && attribute.name == name)
        {
            resourceID = resource.ID;
            break;
        }
    }

    return resourceID;
};

function getDumpFromURL(id, url, sqliteDriver, callback) {
    sqliteDriver.createFromURL(id, url, function(obj) {
        var bar;
        if (obj.error) {
            callback({error: "Unable to open BMPR from URL. " + obj.error, code: obj.error});
        } else {
            bar = new BalsamiqArchiveModule.BalsamiqArchive(sqliteDriver);
            bar.open(id, function(obj) {
                if (obj.error) {
                    callback({error: "Unable to open the archive. " + obj.error, code: obj.error});
                } else {
                    bar.dump(Consts.Branch.AllBranches, function(obj) {
                        if (obj.error) {
                            callback({error: "Unable to retrieve archive dump. " + obj.error, code: obj.error});
                        } else {
                            callback(obj);
                        }
                    });
                }
            });
        }
    });
}


var permalinkPaths = {
    'default': 'p',
    'cloud': 'c',
    'confluence': 'a',
    'gd': 'g',
    'jira': 'j'
};

function composeImagePermalinkUrl(shareURL, permalinkUUID, permalinkKind, format='png') {
    permalinkKind = permalinkKind || 'default';
    var path = permalinkPaths[permalinkKind] || permalinkPaths['default'];
    return shareURL + "/" + path + "/" + permalinkUUID + `.${format}`;
}

function isPermalinkUrlValid(shareURL, permalinkUrl, format='png') {
    var url = new Uri(permalinkUrl);
    var path, ret = false;
    var pattern = new RegExp(`[p,c,j,a,g]/\\w+\\.${format}`, 'i');
    if ((url.protocol() + "://" + url.host()) === shareURL && url.path()) {
        path = url.path();
        ret = !!path.match(pattern);
    }
    return ret;
}

// UUID validation (not the correct one, but the one used in the editor)
var UUIDRegEx = /^[A-Z0-9]{8}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{12}/;
function isValidUUID(uuid) {
    return UUIDRegEx.test(uuid);
}

function isValidBranchID(branchID) {
    return isValidUUID(branchID) || Object.values(Consts.Branch).includes(branchID)
}

exports.isValidUUID = isValidUUID;
exports.isValidBranchID = isValidBranchID;
exports.getMockupsOrderFromDump = getMockupsOrderFromDump;
exports.getMockupsOrderFromDumpExt = getMockupsOrderFromDumpExt;
exports.getResourceAndBranchIDfromInitialResourceAndBranchID = getResourceAndBranchIDfromInitialResourceAndBranchID;
exports.getFirstMockupsThumbnail = getFirstMockupThumbnail;
exports.getResourceIDfromName = getResourceIDfromName;
exports.getDumpFromURL = getDumpFromURL;
exports.getResourceAttributes = getResourceAttributes;
exports.maxRevisionNumber = Number.MAX_SAFE_INTEGER - 100;

exports.composeImagePermalinkUrl = composeImagePermalinkUrl;
exports.isPermalinkUrlValid = isPermalinkUrlValid;
