'use strict';

var Consts = require('./BalsamiqArchiveConstants');
var BmprUtils = require("./BmprUtils");

var CommentPermissionError = "User has no permission to modify or delete this comment";

// BalsamiqArchive class
// ---------------------

//Documentation: https://balsamiq.atlassian.net/wiki/display/intranet/Balsamiq+Archive+Master+API+list

var BalsamiqArchive = function (p_dbDriverInstance, userInfo, log) {
    this.dbDriver = p_dbDriverInstance;
    if (log) {
        this.log = log;
    } else {
        // dummy
        this.log = {
            info: function() {}
        }
    }

    // userinfo.name is the actual user ID
    if (userInfo && userInfo.name) {
        this.userInfo = userInfo;
    }
    else {
        this.userInfo = Consts.defaultUserInfo
    }

    this.userRole = Consts.Role.ROLE_EDITOR;

    this.busy = false;      // it is true when a query is running. trying to run a second query will throw an Error.
};

BalsamiqArchive.prototype.setUserRole = function (role) {
    this.userRole = role;
};

BalsamiqArchive.prototype.setUserInfoName = function (name) {
    this.userInfo.name = name
};

BalsamiqArchive.prototype.getTablesName = function () {
    return ["INFO", "BRANCHES", "RESOURCES", "THUMBNAILS", "USERS", "COMMENTS"] ;
};

BalsamiqArchive.prototype.dump = function (branchID, callback) {
    this._enterCall();

    var dump = {};
    dump.Info = {};
    this._internalGetArchiveData(Consts.Info.SchemaVersion, Consts.Info.SchemaVersion, false, function (obj) {
        if (obj.error) {
            this._exitCall();
            callback(obj);
        } else {
            dump.Info[Consts.Info.SchemaVersion] = obj[Consts.Info.SchemaVersion];
            this._internalGetArchiveData(Consts.Info.ArchiveFormat, "format", false, function (obj) {
                if (obj.error) {
                    this._exitCall();
                    callback(obj);
                } else {
                    dump.Info[Consts.Info.ArchiveFormat] = obj.format;
                    this._getArchiveRevision( function(revision) {
                        dump.Info[Consts.Info.ArchiveRevision] = revision;
                        dump.Info[Consts.Info.ArchiveRevisionUUID] = "";
                        this._internalGetArchiveData(Consts.Info.ArchiveAttributes, "attributes", true, function (obj) {

                            if (obj.error) {
                                this._exitCall();
                                callback(obj);
                            } else {
                                dump.Info[Consts.Info.ArchiveAttributes] = obj.attributes;

                                if (branchID == Consts.Branch.NoBranch) {
                                    this._exitCall();
                                    callback(obj);
                                } else {
                                    this._internalGetBranches(function (obj) {
                                        if (obj.error) {
                                            this._exitCall();
                                            callback(obj);
                                        } else {
                                            dump.Branch = obj.branches;

                                            this._internalGetAllResourcesAttributesForBranch(branchID, function (obj) {
                                                var resourcesIDS = [], i;

                                                if (obj.error) {
                                                    this._exitCall();
                                                    callback(obj);
                                                } else {
                                                    dump.Resources = obj.resources;

                                                    for (i = 0; i < dump.Resources.length; i++) {
                                                        resourcesIDS.push(dump.Resources[i].ID);
                                                    }

                                                    this._internalGetResourcesData(resourcesIDS, branchID, function (obj) {
                                                        if (obj.error) {
                                                            this._exitCall();
                                                            callback(obj);
                                                        } else {

                                                            for (i = 0; i < dump.Resources.length; i++) {
                                                                dump.Resources[i].DATA = obj.data[dump.Resources[i].ID + "|" + dump.Resources[i].BRANCHID];
                                                            }

                                                            this._internalGetThumbnails(function (obj) {
                                                                if (obj.error) {
                                                                    this._exitCall();
                                                                    callback(obj);
                                                                } else {
                                                                    dump.Thumbnails = obj.thumbnails;

                                                                    // more: get users and comments
                                                                    this._internalGetArchiveUsersList(function (obj) {
                                                                        if (obj.error) {
                                                                            this._exitCall();
                                                                            callback(obj);
                                                                        } else {
                                                                            dump.Users = obj.users;

                                                                            this._internalGetCommentsToc(function (obj) {

                                                                                var commentIDs = [], i, j;

                                                                                if (obj.error) {
                                                                                    this._exitCall();
                                                                                    callback(obj);
                                                                                } else {
                                                                                    dump.Comments = obj.comments;

                                                                                    for (i = 0; i < dump.Comments.length; i++) {
                                                                                        commentIDs.push(dump.Comments[i].ID);
                                                                                    }

                                                                                    this._internalGetCommentsData(commentIDs, false, function (obj) {
                                                                                        for (i = 0; i < dump.Comments.length; i++) {
                                                                                            // important: the _internalGetCommentsData DO NOT return data in the same order commentIDs are sent as input!!
                                                                                            for (j=0; j<obj.comments.length; j++) {
                                                                                                if (obj.comments[j].ID === dump.Comments[i].ID) {
                                                                                                    dump.Comments[i].DATA = obj.comments[j].DATA;
                                                                                                    break;
                                                                                                }
                                                                                            }
                                                                                        }

                                                                                        this._exitCall();
                                                                                        callback({dump: dump});
                                                                                    }.bind(this))
                                                                                }
                                                                            }.bind(this));
                                                                        }
                                                                    }.bind(this));
                                                                }
                                                            }.bind(this));
                                                        }
                                                    }.bind(this));
                                                }
                                            }.bind(this));
                                        }
                                    }.bind(this));
                                }
                            }
                        }.bind(this));
                    }.bind(this));
                }
            }.bind(this));
        }
    }.bind(this));
};

BalsamiqArchive.prototype.getToc = function (branchID, callback) {
    this._enterCall();
    this._internalGetToc(branchID, function (obj) {
        this._exitCall();
        callback && callback(obj);
    }.bind(this));
};

BalsamiqArchive.prototype._internalGetToc = function (branchID, callback) {

    if (!this.checkInternalCall(callback)) {
        return;
    }

    var dump = {};
    dump.Info = {};
    dump.userInfo = this.userInfo;
    // internally, we are using name but clients just want an ID
    dump.userInfo.ID = dump.userInfo.name;

    this._internalGetArchiveData(Consts.Info.SchemaVersion, Consts.Info.SchemaVersion, false, function (obj) {
        if (obj.error) {
            callback(obj);
        } else {
            dump.Info[Consts.Info.SchemaVersion] = obj[Consts.Info.SchemaVersion];

            this._internalGetArchiveData(Consts.Info.ArchiveFormat, "format", false, function (obj) {

                if (obj.error) {
                    callback(obj);
                } else {
                    dump.Info[Consts.Info.ArchiveFormat] = obj.format;

                    this._getArchiveRevision( function(archiveRevision) {

                        dump.Info[Consts.Info.ArchiveRevision] = archiveRevision;

                        this._internalGetArchiveData(Consts.Info.ArchiveRevisionUUID, "ArchiveRevisionUUID", false, function(obj) {
                            dump.Info[Consts.Info.ArchiveRevisionUUID] = obj.ArchiveRevisionUUID;
                            this._internalGetArchiveData(Consts.Info.ArchiveAttributes, "attributes", true, function (obj) {

                                if (obj.error) {
                                    callback(obj);
                                } else {
                                    dump.Info[Consts.Info.ArchiveAttributes] = obj.attributes;

                                    if (branchID == Consts.Branch.NoBranch) {
                                        callback({dump: dump});
                                    } else {
                                        this._internalGetBranches(function (obj) {
                                            if (obj.error) {
                                                callback(obj);
                                            } else {
                                                dump.Branch = obj.branches;

                                                this._internalGetAllResourcesAttributesForBranch(branchID, function(obj) {

                                                    if (obj.error) {
                                                        callback(obj);
                                                    } else {
                                                        dump.Resources = obj.resources;

                                                        this._internalGetThumbnails( function(obj) {
                                                            if (obj.error) {
                                                                callback(obj);
                                                            } else {

                                                                dump.Thumbnails = obj.thumbnails;
                                                                this._internalGetArchiveUsersList( function(obj) {
                                                                    if (obj.error) {
                                                                        callback(obj);
                                                                    } else {

                                                                        dump.Users = obj.users;
                                                                        this._internalGetCommentsToc( function(obj) {

                                                                            if (obj.error) {
                                                                                callback(obj);
                                                                            } else {

                                                                                dump.Comments = obj.comments;
                                                                                callback({dump: dump});
                                                                            }
                                                                        }.bind(this));
                                                                    }
                                                                }.bind(this));
                                                            }
                                                        }.bind(this));
                                                    }
                                                }.bind(this));
                                            }
                                        }.bind(this));
                                    }
                                }
                            }.bind(this));
                        }.bind(this));
                    }.bind(this));
                }
            }.bind(this));
        }
    }.bind(this));
};


BalsamiqArchive.prototype.createFromDump = function (id, dump, callback) {
    this.archiveID = id;
    // var logObj = {action: "create"};
    // var resourcesNumber = dump.Resources ? dump.Resources.length : 0;
    // this.log.info(logObj, "HUNT: BEGIN create for archive " + id + " with resources " + resourcesNumber);

    // TODO: check if dump is well formed try catch is not obvious with all this callbacks
    // TODO: delete the zombie temporary sqlite archive in case of error (they are located in the "src/archive" folder

    this.create(this.archiveID, dump.Info[Consts.Info.ArchiveFormat], function (obj) {
        if (obj.error) {
            callback(obj);
            return;
        }
        // this.log.info(logObj, "HUNT: after create db " + id);
        this.setArchiveAttributes(dump.Info[Consts.Info.ArchiveAttributes], function (obj) {
            if (obj.error) {
                callback(obj);
                return;
            }
            // delete the default Master branch eventually created in the previous create call
            // this.log.info(logObj, "HUNT: after setting attributes " + id);
            this.deleteBranches([Consts.Branch.MasterBranchID], function (obj) {
                if (obj.error) {
                    callback(obj);
                    return;
                }

                // this.log.info(logObj, "HUNT: after deleting branches " + id);
                var branchesToCreate = dump.Branch;
                var createNextBranch = function(index, localThis) {
                    var toCreate;
                    if (index < branchesToCreate.length) {
                        toCreate = branchesToCreate[index];
                        localThis.createBranch(toCreate.branchID, toCreate.ATTRIBUTES, function (obj) {
                            if (obj.error) {
                                callback(obj);
                                return;
                            }
                            index++;
                            createNextBranch(index, localThis);
                        });
                    } else {
                        // localThis.log.info(logObj, "HUNT: after creating branches " + id);
                        var resourcesToCreate = dump.Resources;

                        var createNextResource = function(index, localThis) {
                            var toCreate;
                            if (index < resourcesToCreate.length) {
                                toCreate = resourcesToCreate[index];
                                localThis.createResource(toCreate.ID, toCreate.BRANCHID, toCreate.ATTRIBUTES, toCreate.DATA, function (obj) {
                                    if (obj.error) {
                                        callback(obj);
                                        return;
                                    }
                                    index++;
                                    createNextResource(index, localThis);
                                });
                            } else {
                                // localThis.log.info(logObj, "HUNT: after creating resources " + id);
                                var thumbnailsToCreate = dump.Thumbnails;
                                var createNextThumbnail = function(index, localThis) {
                                    var toCreate;
                                    if (index < thumbnailsToCreate.length) {
                                        toCreate = thumbnailsToCreate[index];
                                        localThis.createThumbnail(toCreate.ID, toCreate.ATTRIBUTES, function (obj) {
                                            if (obj.error) {
                                                callback(obj);
                                                return;
                                            }
                                            index++;
                                            createNextThumbnail(index, localThis);
                                        });
                                    } else {
                                        // localThis.log.info(logObj, "HUNT: after creating thumbnails " + id);
                                        var usersToCreate = dump.Users;
                                        var createNextUser = function(index, localThis) {
                                            var toCreate;
                                            if (index < usersToCreate.length) {
                                                toCreate = usersToCreate[index];
                                                localThis.reCreateUser(toCreate.ID, toCreate.ATTRIBUTES, function (obj) {
                                                    if (obj.error) {
                                                        callback(obj);
                                                        return;
                                                    }
                                                    index++;
                                                    createNextUser(index, localThis);
                                                });
                                            } else {
                                                // localThis.log.info(logObj, "HUNT: after creating users " + id);
                                                var commentsToCreate = dump.Comments;
                                                var createNextComment = function (index, localThis) {
                                                    var toCreate;
                                                    if (index < commentsToCreate.length) {
                                                        toCreate = commentsToCreate[index];
                                                        // commentID, resourceID, branchID, commentParentID
                                                        localThis.reCreateComment(toCreate.ID, toCreate.RESOURCEID, toCreate.BRANCHID, toCreate.USERID, toCreate.ATTRIBUTES, toCreate.DATA, function (obj) {
                                                            if (obj.error) {
                                                                callback(obj);
                                                                return;
                                                            }
                                                            index++;
                                                            createNextComment(index, localThis);
                                                        });
                                                    } else {
                                                        // localThis.log.info(logObj, "HUNT: after creating comments " + id);
                                                        localThis.setArchiveRevision(dump.Info[Consts.Info.ArchiveRevision], function (obj) {
                                                            // localThis.log.info(logObj, "HUNT: END calling callback " + id);
                                                            if (obj.error) {
                                                                callback(obj);
                                                            }
                                                            else {
                                                                callback(obj);
                                                            }
                                                        });
                                                    }
                                                };
                                                createNextComment(0, localThis);
                                            }
                                        };
                                        createNextUser(0, localThis);
                                    }

                                };
                                createNextThumbnail(0, localThis);
                            }
                        };
                        createNextResource(0, localThis);
                    }
                };

                createNextBranch(0, this);

            }.bind(this));
        }.bind(this));
    }.bind(this));
};

BalsamiqArchive.prototype.createFromBuffer = function (id, buffer, callback) {
    this.archiveID = id;
    this._enterCall();
    this.dbDriver.createFromBuffer(this.archiveID, buffer, function(obj) {

        if (obj.error) {
            this._exitCall();
            this._finalCallbackWithError(callback, obj);
        } else {
            this._internalGetArchiveData(Consts.Info.SchemaVersion, Consts.Info.SchemaVersion, false, function (obj) {

                if (obj.error) {
                    this._exitCall();
                    this._finalCallbackWithError(callback, obj);
                } else {
                    // TODO: how to compare?
                    if (obj[Consts.Info.SchemaVersion] !== Consts.CurrentSchemaVersion) {
                        this._migrate1To2(function(obj) {
                            if (obj.error)
                            {
                                this._exitCall();
                                this._finalCallbackWithError(callback, obj);
                            } else {
                                this._getArchiveRevision(function (p_pv) {
                                    this._exitCall();
                                    this._finalCallback(callback, p_pv, obj);
                                }.bind(this));
                            }
                        }.bind(this));
                    } else {
                        this._getArchiveRevision(function (p_pv) {
                            this._exitCall();
                            this._finalCallback(callback, p_pv, obj);
                        }.bind(this));
                    }
                }
             }.bind(this));
        }

    }.bind(this));
};

BalsamiqArchive.prototype.getBuffer = function (id, callback) {
    this._enterCall();
    this.archiveID = id;
    this.dbDriver.getBuffer(this.archiveID, function (obj) {
        this._exitCall();
        callback && callback(obj);
    }.bind(this));
};

// create a new DBDriver file
BalsamiqArchive.prototype.create = function (id, p_format, callback) {
    this.archiveID = id;
    this._enterCall();
    this.dbDriver.open(this.archiveID, "readWriteOrCreate", function (obj) {
        var query;
        if (obj.error) {
            this._exitCall();
            this._finalCallbackWithError(callback, obj);
        } else {
            this.dbDriver.serialize(this.archiveID, function (obj) {
                if (obj.error) {
                    this._exitCall();
                    this._finalCallbackWithError(callback, obj);
                } else {
                    // create tables
                    if (this.dbDriver.type == "mysql") {
                        query = "CREATE TABLE INFO (NAME VARCHAR(255) PRIMARY KEY, VALUE TEXT CHARACTER SET 'utf8mb4' COLLATE 'utf8mb4_unicode_ci');";
                    } else {
                        query = "CREATE TABLE INFO (NAME VARCHAR(255) PRIMARY KEY, VALUE TEXT);";
                    }
                    this.dbDriver.run(this.archiveID, query, {}, function (obj) {
                        if (obj.error) {
                            this._exitCall();
                            this._finalCallbackWithError(callback, obj);
                        } else {
                            if (this.dbDriver.type == "mysql") {
                                query = "CREATE TABLE BRANCHES (ID VARCHAR(255) PRIMARY KEY, ATTRIBUTES TEXT CHARACTER SET 'utf8mb4' COLLATE 'utf8mb4_unicode_ci');";
                            } else {
                                query = "CREATE TABLE BRANCHES (ID VARCHAR(255) PRIMARY KEY, ATTRIBUTES TEXT);";
                            }
                            this.dbDriver.run(this.archiveID, query, {}, function (obj) {
                                if (obj.error) {
                                    this._exitCall();
                                    this._finalCallbackWithError(callback, obj);
                                } else {
                                    if (this.dbDriver.type == "mysql") {
                                        query = "CREATE TABLE RESOURCES (ID VARCHAR(255), BRANCHID VARCHAR(255), ATTRIBUTES TEXT CHARACTER SET 'utf8mb4' COLLATE 'utf8mb4_unicode_ci', DATA LONGTEXT CHARACTER SET 'utf8mb4' COLLATE 'utf8mb4_unicode_ci', PRIMARY KEY (ID, BRANCHID), FOREIGN KEY (BRANCHID) REFERENCES BRANCHES(ID));";
                                    } else {
                                        query = "CREATE TABLE RESOURCES (ID VARCHAR(255), BRANCHID VARCHAR(255), ATTRIBUTES TEXT, DATA LONGTEXT, PRIMARY KEY (ID, BRANCHID), FOREIGN KEY (BRANCHID) REFERENCES BRANCHES(ID));";
                                    }
                                    this.dbDriver.run(this.archiveID, query, {}, function (obj) {
                                        if (obj.error) {
                                            this._exitCall();
                                            this._finalCallbackWithError(callback, obj);
                                        } else {
                                            this.dbDriver.run(this.archiveID, "CREATE TABLE THUMBNAILS (ID VARCHAR(255) PRIMARY KEY, ATTRIBUTES MEDIUMTEXT);", {}, function (obj) {
                                                if (obj.error) {
                                                    this._exitCall();
                                                    this._finalCallbackWithError(callback, obj);
                                                } else {
                                                    if (this.dbDriver.type == "mysql") {
                                                        query = "CREATE TABLE USERS (ID VARCHAR(255) PRIMARY KEY, ATTRIBUTES TEXT CHARACTER SET 'utf8mb4' COLLATE 'utf8mb4_unicode_ci');";
                                                    } else {
                                                        query = "CREATE TABLE USERS (ID VARCHAR(255) PRIMARY KEY, ATTRIBUTES TEXT);";
                                                    }
                                                    this.dbDriver.run(this.archiveID, query, {}, function (obj) {
                                                        if (obj.error) {
                                                            this._exitCall();
                                                            this._finalCallbackWithError(callback, obj);
                                                        } else {
                                                            if (this.dbDriver.type == "mysql") {
                                                                query = "CREATE TABLE COMMENTS (ID VARCHAR(255) PRIMARY KEY, BRANCHID VARCHAR(255), RESOURCEID VARCHAR(255), DATA LONGTEXT CHARACTER SET 'utf8mb4' COLLATE 'utf8mb4_unicode_ci', USERID VARCHAR(255), ATTRIBUTES TEXT CHARACTER SET 'utf8mb4' COLLATE 'utf8mb4_unicode_ci', FOREIGN KEY (USERID) REFERENCES USERS(ID), FOREIGN KEY (RESOURCEID, BRANCHID) REFERENCES RESOURCES(ID, BRANCHID));";
                                                            } else {
                                                                query = "CREATE TABLE COMMENTS (ID VARCHAR(255) PRIMARY KEY, BRANCHID VARCHAR(255), RESOURCEID VARCHAR(255), DATA LONGTEXT, USERID VARCHAR(255), ATTRIBUTES TEXT, FOREIGN KEY (USERID) REFERENCES USERS(ID), FOREIGN KEY (RESOURCEID, BRANCHID) REFERENCES RESOURCES(ID, BRANCHID));";
                                                            }
                                                            this.dbDriver.run(this.archiveID, query, {}, function (obj) {
                                                                if (obj.error) {
                                                                    this._exitCall();
                                                                    this._finalCallbackWithError(callback, obj);
                                                                } else {
                                                                    // create master branch
                                                                    this.dbDriver.run(this.archiveID, "INSERT INTO BRANCHES (ID, ATTRIBUTES) VALUES (?, ?);", [Consts.Branch.MasterBranchID, JSON.stringify({})], function (obj) {
                                                                        if (obj.error) {
                                                                            this._exitCall();
                                                                            this._finalCallbackWithError(callback, obj);
                                                                        } else {
                                                                            // set archive revision
                                                                            var values = [
                                                                                Consts.Info.SchemaVersion,
                                                                                Consts.CurrentSchemaVersion,
                                                                                Consts.Info.ArchiveRevision,
                                                                                0,
                                                                                Consts.Info.ArchiveFormat,
                                                                                p_format,
                                                                                Consts.Info.ArchiveRevisionUUID,
                                                                                ""
                                                                            ];
                                                                            this.dbDriver.run(this.archiveID, "INSERT INTO INFO (NAME, VALUE) VALUES (?, ?), (?, ?), (?, ?), (?, ?);", values, function (obj) {
                                                                                this._exitCall();
                                                                                if (obj.error) {
                                                                                    this._finalCallbackWithError(callback, obj)
                                                                                } else {
                                                                                    this._getArchiveRevision(function (p_pv) {
                                                                                        this._finalCallback(callback, p_pv);
                                                                                    }.bind(this));
                                                                                }
                                                                            }.bind(this));
                                                                        }
                                                                    }.bind(this));
                                                                }
                                                            }.bind(this));
                                                        }
                                                    }.bind(this));
                                                }
                                            }.bind(this));
                                        }
                                    }.bind(this));
                                }
                            }.bind(this));
                        }
                    }.bind(this));
                }
            }.bind(this));
        }
    }.bind(this));
};

BalsamiqArchive.prototype.validateBAR = function (id, callback) {
    // callback({ schemaVersion: null || SchemaVersions, archiveFormat:String?, archiveRevision:Number? || error })

    this.archiveID = id;
    this._enterCall();
    this.dbDriver.open(id, "read", function (obj) {
        if (obj.error) {
            this._exitCall();
            this._finalCallback(callback, { schemaVersion: null });
        } else {
            this.dbDriver.checkIntegrity(this.archiveID, function(obj) {
                if (obj.error) {
                    this.dbDriver.close(id, function () {
                        this._exitCall();
                        this._finalCallbackWithError(callback, obj);
                    }.bind(this));
                } else {
                    // check if upgrade is necessary
                    this._internalGetArchiveData(Consts.Info.SchemaVersion, Consts.Info.SchemaVersion, false, function (obj) {
                        if (obj.error) {
                            this.dbDriver.close(id, function () {
                                this._exitCall();
                                this._finalCallback(callback, { schemaVersion: null });
                            }.bind(this));
                        } else {
                            this._getArchiveRevision(function (archiveRevision) {
                                this.dbDriver.close(id, function () {
                                    this._exitCall();
                                    this._finalCallback(callback, archiveRevision, {
                                        schemaVersion: obj[Consts.Info.SchemaVersion],
                                        archiveFormat: "bmpr"        // TODO one more call to _internalGetArchiveData
                                    });
                                }.bind(this));
                            }.bind(this));
                        }
                    }.bind(this));
                }
            }.bind(this));
        }
    }.bind(this));
};

BalsamiqArchive.prototype.open = function (id, callback) {
    this.archiveID = id;
    this._enterCall();
    this.dbDriver.open(this.archiveID, "readWrite", function (obj) {
        if (obj.error) {
            this._exitCall();
            this._finalCallbackWithError(callback, obj);
        } else {
            // check if upgrade is necessary
            this._internalGetArchiveData(Consts.Info.SchemaVersion, Consts.Info.SchemaVersion, false, function (obj) {
                if (obj.error) {
                    this._exitCall();
                    this._finalCallbackWithError(callback, obj);
                } else {
                    // TODO: how to compare?
                    if (obj[Consts.Info.SchemaVersion] !== Consts.CurrentSchemaVersion) { // TODO fix this check!!!
                        this._migrate1To2(function (obj) {
                            if (obj.error) {
                                this._exitCall();
                                this._finalCallbackWithError(callback, obj);
                            } else {
                                this._internalGetToc(Consts.Branch.AllBranches, function (obj) {
                                    if (obj.error) {
                                        this._exitCall();
                                        this._finalCallbackWithError(callback, obj);
                                    } else {
                                        this._getArchiveRevision(function (p_pv) {
                                            this._exitCall();
                                            this._finalCallback(callback, p_pv, obj);
                                        }.bind(this));
                                    }
                                }.bind(this));
                            }
                        }.bind(this));
                    } else {
                        this._internalGetToc(Consts.Branch.AllBranches, function (obj) {
                            if (obj.error) {
                                this._exitCall();
                                this._finalCallbackWithError(callback, obj);
                            } else {
                                this._getArchiveRevision(
                                    function (p_pv) {
                                        this._exitCall();

                                        this._finalCallback(callback, p_pv, obj);
                                    }.bind(this)
                                );
                            }
                        }.bind(this));
                    }
                }
            }.bind(this));
        }
    }.bind(this));
};

BalsamiqArchive.prototype._internalUpdateUser = function(attributes, callback) {

    if (!this.checkInternalCall(callback)) {
        return;
    }

    this._internalGetArchiveUsersList( function(obj) {

        if (obj.error) {
            callback(obj);
            return;
        }

        var user = null;
        if (obj.users) {
            user = obj.users.find(function (element/*, index, array*/) {
                return element.ID === this.userInfo.name;
            }.bind(this));
        }

        if (user === null) {
            callback({error: "User to be updated does not exists: " + this.userInfo.name});
            return;
        }

        this.dbDriver.run(this.archiveID, "UPDATE USERS SET ATTRIBUTES=? WHERE ID=?;", [JSON.stringify(attributes), this.userInfo.name], function (obj) {
            callback(obj);
        }.bind(this));
    }.bind(this));
};

BalsamiqArchive.prototype._internalInsertUserIfNeeded = function(attributes, callback) {

    if (!this.checkInternalCall(callback)) {
        return;
    }

    this._internalGetArchiveUsersList( function(obj) {

        if (obj.error) {
            callback(obj);
            return;
        }

        var user = null;
        if (obj.users) {
            user = obj.users.find(function (element/*, index, array*/) {
                return element.ID === this.userInfo.name;
            }.bind(this));
        }

        if (user && !attributes) {
            callback({});
        }
        else if (user && attributes) {
            this._internalUpdateUser(attributes, callback);
        }
        else {

            this.dbDriver.run(this.archiveID, "INSERT INTO USERS (ID, ATTRIBUTES) VALUES (?, ?);", [ this.userInfo.name, JSON.stringify(attributes)], function (obj) {
                if (obj.error) {
                    callback(obj);
                }
                else {
                    callback({});
                }
            }.bind(this));
        }
    }.bind(this));
};

BalsamiqArchive.prototype._purgeComments = function (userID, callback) {
    this._internalGetComments(function(obj) {
        if (obj.error) {
            callback(obj);
        } else {
            var commentIDsToPurge = [];
            for (var i = 0; i < obj.comments.length; i++) {
                var comment = obj.comments[i];
                if (comment.ATTRIBUTES[Consts.CommentAttributes.Trashed] === true && comment.ATTRIBUTES[Consts.CommentAttributes.TrashedBy] === userID && comment.DATA !== "{}") {
                    commentIDsToPurge.push(comment.ID);
                }
            }

            makeQueryInBatches(this.dbDriver, 'run', this.archiveID, "UPDATE COMMENTS SET DATA = '{}' WHERE ID in ([!!__VALUES__!!])", [], commentIDsToPurge, function (obj) {
                if (obj.error) {
                    callback(obj);
                } else {
                    callback({ purgedCommentIDs: commentIDsToPurge })
                }
            }.bind(this));
        }
    }.bind(this));
};

BalsamiqArchive.prototype.purgeComments = function (userID, callback) {
    this._enterCall();
    this._purgeComments(userID, function (obj) {
        if (obj.error) {
            this._exitCall();
            this._finalCallbackWithError(callback, obj);
        } else {
            this._getArchiveRevision(function (p_ar) {
                this._exitCall();
                this._finalCallback(callback, p_ar, obj);
            }.bind(this));
        }
    }.bind(this));
};

BalsamiqArchive.prototype._purgeCommentsAndUsers = function (callback) {
    this._internalGetComments(
        function (obj) {
            if (obj.error) {
                callback(obj);
            } else {
                const commentIdsMap = new Map();
                obj.comments.forEach((c) => {
                    commentIdsMap.set(c.ID, {
                        comment: c,
                        keep: !c.ATTRIBUTES[Consts.CommentAttributes.Trashed],
                        processed: false,
                        ancestors: undefined,
                    });
                });


                const computeAncestors = (elem, idsMap) => {
                    if (elem.ancestors) {
                        return;
                    }
                    let parentID = elem.comment.ATTRIBUTES[Consts.CommentAttributes.ParentID];
                    if (!parentID) {
                        elem.ancestors = [];
                        return;
                    }
                    const parentElem = idsMap.get(parentID);
                    computeAncestors(parentElem, idsMap);
                    elem.ancestors = [parentID, ...parentElem.ancestors];
                };
                commentIdsMap.forEach((value) => {
                    computeAncestors(value, commentIdsMap);
                });

                obj.comments.forEach((c) => {
                    const obj = commentIdsMap.get(c.ID);
                    if (obj.processed) return;
                    if (obj.keep) {
                        obj.processed = true;
                        const ancestors = obj.ancestors;
                        for (let i = 0; i < ancestors.length; i++) {
                            commentIdsMap.get(ancestors[i]).keep = true;
                        }                        
                    }
                });

                const commentIDsToDelete = [];
                const usersToKeep = new Set();
                commentIdsMap.forEach((value, id) => {
                    if (!value.keep) {
                        commentIDsToDelete.push(id);
                    } else {
                        const likedByUsers = value.comment.ATTRIBUTES[Consts.CommentAttributes.LikedBy];
                        //const readByUsers = value.comment.ATTRIBUTES[Consts.CommentAttributes.ReadBy];
                        const users = [...likedByUsers, value.comment.USERID];
                        users.forEach((u) => usersToKeep.add(u));
                    }
                });

                makeQueryInBatches(
                    this.dbDriver,
                    "run",
                    this.archiveID,
                    "DELETE FROM COMMENTS WHERE ID in ([!!__VALUES__!!])",
                    [],
                    commentIDsToDelete,
                    function (obj) {
                        if (obj.error) {
                            callback(obj);
                        } else {
                            this._internalGetArchiveUsersList(
                                function (obj) {
                                    if (obj.error) {
                                        callback(obj);
                                    } else {
                                        const usersToDelete = [];
                                        obj.users.forEach((userData) => {
                                            if (!usersToKeep.has(userData.ID)) {
                                                usersToDelete.push(userData.ID);
                                            }
                                        });

                                        makeQueryInBatches(
                                            this.dbDriver,
                                            "run",
                                            this.archiveID,
                                            "DELETE FROM USERS WHERE ID in ([!!__VALUES__!!])",
                                            [],
                                            usersToDelete,
                                            function (obj2) {
                                                if (obj2.error) {
                                                    callback(obj2);
                                                } else {                                                    
                                                    callback({purgedComments: commentIDsToDelete, purgedUsers: usersToDelete});                                                    
                                                }
                                            }.bind(this)
                                        );
                                    }
                                }.bind(this)
                            );
                        }
                    }.bind(this)
                );

                // Mark and delete the USERS!!!
            }
        }.bind(this)
    );
};

BalsamiqArchive.prototype.purgeCommentsAndUsers = function (callback) {
    this._enterCall();
    this._purgeCommentsAndUsers(
        function (obj) {
            if (obj.error) {
                this._exitCall();
                this._finalCallbackWithError(callback, obj);
            } else {
                const changedRows = obj.purgedComments.length + obj.purgedUsers.length;   

                if (changedRows > 0) {
                    this._incrementArchiveRevision((p_ar) => {
                        this._exitCall();
                        this._finalCallback(callback, p_ar, obj);

                    });
                } else {
                    this._getArchiveRevision((p_ar) => {
                        this._exitCall();
                        this._finalCallback(callback, p_ar, obj);
                    });
                }
            }
        }.bind(this)
    );
};

// close the DBDriver db
BalsamiqArchive.prototype.close = function (id, callback) {
    this.archiveID = id;
    this._enterCall();
    this._purgeCommentsAndUsers((res) => {  

        let changedRows = 0;
        if (res.purgedComments) {
            changedRows += res.purgedComments.length;
        }
        if (res.purgedUsers) {
            changedRows += res.purgedUsers.length;
        }       
        // ignore purge errors. we want to continue with the close anyway.
        const myClose = () =>
            this._getArchiveRevision(
                (p_ar) => {
                    this.dbDriver.close(
                        this.archiveID,
                        (obj) => {
                            this._exitCall();
                            if (obj.error) {
                                this._finalCallbackWithError(callback, obj);
                            } else {
                                this._finalCallback(callback, p_ar);
                            }
                            this.dbDriver = null;
                        }
                    );
                }
            );

        if (changedRows > 0) {
            this._incrementArchiveRevision(
                (p_pv) => {
                    myClose(p_pv);
                }
            );
        } else {
            myClose();
        }       
    });
};

BalsamiqArchive.prototype.destroy = function (id, callback) {
    this.archiveID = id;
    this.dbDriver.destroy(this.archiveID, callback);
};

BalsamiqArchive.prototype.destroyExt = function (id, force, callback) {
    this.archiveID = id;
    if (this.dbDriver.destroyExt) {
        this.dbDriver.destroyExt(this.archiveID, force, callback);
    } else {
        // TODO
        // N/A
        this.dbDriver.destroy(this.archiveID, callback);
    }
};

// read and return the archive Revision
BalsamiqArchive.prototype.getArchiveRevision = function (callback) {
    this._getArchiveRevision(function (p_pv) {
        this._finalCallback(callback, p_pv);
    }.bind(this));
};

//Only used for restoring from a previous dump
BalsamiqArchive.prototype.setArchiveRevision = function (p_revision, callback) {
    this._enterCall();
    this.dbDriver.run(this.archiveID, "REPLACE INTO INFO (NAME, VALUE) VALUES (?, ?);", [Consts.Info.ArchiveRevision, p_revision], function (obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {
            this._finalCallback(callback, p_revision);
        }
    }.bind(this));
};

// read attributes for the whole archive
BalsamiqArchive.prototype.getArchiveAttributes = function (callback) {
    this._getArchiveData(Consts.Info.ArchiveAttributes, callback, "attributes", true);
};

// set attributes for the whole archive
BalsamiqArchive.prototype.setArchiveAttributes = function (p_attrs, callback) {
    this._setArchiveData(Consts.Info.ArchiveAttributes, JSON.stringify(p_attrs), callback);
};

BalsamiqArchive.prototype.compact = function (callback) {
    this._enterCall();
    this.dbDriver.compact(this.archiveID, function(obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {
            this._getArchiveRevision(function (p_pv) {
                this._finalCallback(callback, p_pv, obj);
            }.bind(this));
        }
    }.bind(this));
};

BalsamiqArchive.prototype.download = function (callback/*, progressCallback*/) {
    this.dbDriver.download(this.archiveID, function(uint8Array)
    {
        callback(uint8Array);
    }.bind(this));
};

BalsamiqArchive.prototype.isLocal = function() {
    return true;
};

// read archive format
BalsamiqArchive.prototype.getArchiveFormat = function (callback) {
    this._getArchiveData(Consts.Info.ArchiveFormat, callback, "format");
};

BalsamiqArchive.prototype.getArchiveSchemaVersion = function (callback) {
    this._getArchiveData(Consts.Info.SchemaVersion, callback, Consts.Info.SchemaVersion);
};

// write archive format
BalsamiqArchive.prototype.setArchiveFormat = function (p_format, callback) {
    this._setArchiveData(Consts.Info.ArchiveFormat, p_format, callback);
};

// read the branch list
BalsamiqArchive.prototype.getBranches = function (callback) {
    this._enterCall();

    this._internalGetBranches(function (obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {
            this._getArchiveRevision(function (p_pv) {
                this._finalCallback(callback, p_pv, obj);
            }.bind(this));
        }
    }.bind(this));
};

BalsamiqArchive.prototype._internalGetBranches = function (callback) {
    var archiveID;

    if (!this.checkInternalCall(callback)) {
        return;
    }

    archiveID = this.archiveID;
    this.dbDriver.all(archiveID, "SELECT ID, ATTRIBUTES FROM BRANCHES;", {}, function (obj) {
        var res, i, p_record;

        if (obj.error) {
            callback(obj);
        } else {
            res = { branches: [] };
            try {
                for (i=0; i<obj.rows.length; i++) {
                    p_record = obj.rows[i];
                    res.branches.push({
                        branchID: p_record.ID,
                        ATTRIBUTES: JSON.parse(p_record.ATTRIBUTES)
                    });
                }
                callback(res);
            } catch (e) {
                callback({error: "Balsamiq Archive error: Unexpected database response while getting attributes, id from branch: " + archiveID + " " + JSON.stringify(obj) + '. Error: ' + e.message, stack: e.stack});
            }
        }
    }.bind(this));
};

// read the attributes of the given branch
BalsamiqArchive.prototype.getBranchAttributes = function (branchID, callback) {
    this._enterCall();

    this.dbDriver.get(this.archiveID, "SELECT ATTRIBUTES FROM BRANCHES WHERE ID = ?;", [branchID], function (obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {
            try {
                if (obj.row) {
                    var res = { attributes: JSON.parse(obj.row.ATTRIBUTES) };
                    this._getArchiveRevision(function (p_pv) {
                        this._finalCallback(callback, p_pv, res);
                    }.bind(this));
                } else {
                    this._finalCallbackWithError(callback, {error: "Balsamiq Archive error: Unexpected db response while getting branch attribute: no attribute for selected branch: " + branchID});
                }
            } catch (e) {
                this._finalCallbackWithError(callback, {error: "Balsamiq Archive error: Unexpected database response while getting branch attribute: " + JSON.stringify(obj)});
            }
        }
    }.bind(this));
};

// write the attributes of a branch
BalsamiqArchive.prototype.setBranchAttributes = function (branchID, attributes, callback) {
    this._enterCall();

    if (!attributes) {
        attributes = {};
    }

    this.dbDriver.run(this.archiveID, "UPDATE BRANCHES SET ATTRIBUTES = ? WHERE ID = ?;", [JSON.stringify(attributes), branchID], function (obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {
            this._finalCallbackWithAffectedRowsCheck(callback, obj);
        }
    }.bind(this));
};

// read the resources belonging to the given branch
BalsamiqArchive.prototype.getAllResourcesAttributesForBranch = function (branchID, callback) {
    this._enterCall();
    this._internalGetAllResourcesAttributesForBranch(branchID, function (obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj.error);
        } else {
            this._getArchiveRevision(function (p_pv) {
                this._finalCallback(callback, p_pv, obj);
            }.bind(this));
        }
    }.bind(this));
};

BalsamiqArchive.prototype._internalGetAllResourcesAttributesForBranch = function (branchID, callback) {

    if (!this.checkInternalCall(callback)) {
        return;
    }

    var query, values;
    var archiveID = this.archiveID;

    if (branchID == Consts.Branch.AllBranches || branchID == null) {
        query =  "SELECT ID, BRANCHID, ATTRIBUTES FROM RESOURCES";
        values = [];
    } else {
        query =  "SELECT ID, BRANCHID, ATTRIBUTES FROM RESOURCES WHERE BRANCHID = ?";
        values = [branchID];
    }

    this.dbDriver.all(this.archiveID, query, values, function (obj) {
        var res, i, p_record, attrs;
        if (obj.error) {
            callback(obj);
        }
        else {
            try {
                res = { resources: [] };
                for (i=0; i<obj.rows.length; i++) {
                    p_record = obj.rows[i];
                    attrs = JSON.parse(p_record.ATTRIBUTES);
                    res.resources.push({
                        ID: p_record.ID,
                        BRANCHID: p_record.BRANCHID,
                        ATTRIBUTES: attrs
                    });
                }
                callback(res);
            } catch (e) {
                callback({
                    error: "Balsamiq Archive error: Unexpected database response while getting resources (" + p_record.ID + ", " + branchID + ")",
                    archiveID: archiveID,
                    attributes: p_record.ATTRIBUTES,
                    errorMessage: e.message,
                    stack: e.stack,
                    bmprObj: obj
                });
            }
        }
    }.bind(this));
};

// create a new resource
BalsamiqArchive.prototype.createResource = function (p_resourceID, branchID, attributes, p_data, callback) {
    this._enterCall();

    if (p_data === undefined) {
        p_data = "";
    }
    if (attributes === null || attributes === undefined) {
        attributes = {};
    }
    var values = [
        p_resourceID,
        branchID,
        p_data,
        JSON.stringify(attributes)
    ];
    this.dbDriver.run(this.archiveID, "INSERT INTO RESOURCES (ID, BRANCHID, DATA, ATTRIBUTES) VALUES (?, ?, ?, ?);", values, function (obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {
            this._finalCallbackWithAffectedRowsCheck(callback, obj, {ID: p_resourceID});
        }
    }.bind(this));
};

BalsamiqArchive.prototype.deleteResources = function (p_resourceIDs, branchID, callback) {
    var query, fixedValues;

    this._enterCall();

    this._internalGetCommentsToc(function (obj) {
        var i, j;
        if (obj.error) {
            this._exitCall();
            this._finalCallbackWithError(callback, obj);
        } else {

            // check if there are comments pointing to these resources and issue an error
            for (i = 0; i < obj.comments.length; i++) {
                var comment = obj.comments[i];

                if (branchID === comment.BRANCHID) {
                    for (j=0; j<p_resourceIDs.length; j++) {
                        if (p_resourceIDs[j] == comment.RESOURCEID) {
                            this._exitCall();
                            this._finalCallbackWithError(callback, {error: "Balsamiq Archive error: Could not delete a resource with comments"});
                            return;
                        }
                    }
                }
            }

            // no comments? go!

            if (branchID == Consts.Branch.AllBranches || branchID == null) {
                query = "DELETE FROM RESOURCES WHERE ID in ([!!__VALUES__!!])";
                fixedValues = [];
            } else {
                query = "DELETE FROM RESOURCES WHERE BRANCHID = ? AND ID in ([!!__VALUES__!!])";
                fixedValues = [branchID];
            }

            makeQueryInBatches(this.dbDriver, 'run', this.archiveID, query, fixedValues, p_resourceIDs, function (obj) {
                this._exitCall();
                if (obj.error) {
                    this._finalCallbackWithError(callback, obj);
                } else {
                    this._finalCallbackWithAffectedRowsCheck(callback, obj);
                }
            }.bind(this));
        }
    }.bind(this));
};

// read the attributes of the selected resources
BalsamiqArchive.prototype.getResourceAttributes = function (p_resourceID, p_branchID, callback) {
    var query, values, allBranch;

    this._enterCall();

    if (p_branchID == Consts.Branch.AllBranches || p_branchID == null) {
        query = "SELECT ID, BRANCHID, ATTRIBUTES FROM RESOURCES WHERE ID = ?;";
        values = [p_resourceID];
        allBranch = true;
    } else {
        query = "SELECT ATTRIBUTES FROM RESOURCES WHERE ID = ? AND BRANCHID = ?;";
        values = [p_resourceID, p_branchID];
        allBranch = false;
    }


    this.dbDriver.all(this.archiveID, query, values, function (obj) {
        var res;
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {
            try {
                res = {attributes: {}};
                if (allBranch) {
                    for (var i=0; i<obj.rows.length; i++)
                    {
                        var row = obj.rows[i];
                        res.attributes[row.BRANCHID] = JSON.parse(row.ATTRIBUTES);
                    }
                } else {
                    if (obj.rows && obj.rows[0]) {
                        row = obj.rows[0];
                        res.attributes = JSON.parse(row.ATTRIBUTES);
                    }
                }
                this._getArchiveRevision(function (p_pv) {
                    this._finalCallback(callback, p_pv, res);
                }.bind(this));
            } catch (e) {
                this._finalCallbackWithError(callback, {error: "Balsamiq Archive error: Unexpected database response while getting resource attributes: " + JSON.stringify(obj)});
            }
        }
    }.bind(this));
};

// set the attributes of the given resource
BalsamiqArchive.prototype.setResourceAttributes = function (p_resourceID, p_branchID, p_attrs, callback) {
    this._enterCall();

    this.dbDriver.run(this.archiveID, "UPDATE RESOURCES SET ATTRIBUTES = ? WHERE ID = ? AND BRANCHID = ?;", [JSON.stringify(p_attrs), p_resourceID, p_branchID], function (obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {
            this._finalCallbackWithAffectedRowsCheck(callback, obj)
        }
    }.bind(this));
};

// set the branch id of the given resource
BalsamiqArchive.prototype.setResourceBranchID = function (resourceID, oldBranchID, newBranchID, callback) {
    this._enterCall();

    this.dbDriver.get(this.archiveID, "SELECT ID, ATTRIBUTES, DATA FROM RESOURCES WHERE ID = ? AND BRANCHID = ?;", [resourceID, oldBranchID], function (obj) {
        var res;
        if (obj.error) {
            this._exitCall();
            this._finalCallbackWithError(callback, obj);
            return;
        }

        if (!obj.row) {
            this._exitCall();
            this._finalCallbackWithError(callback, {error: "Balsamiq Archive error: no resource found trying to set the branch ID"});
            return;
        }

        res = obj.row;

        this.dbDriver.run(this.archiveID, "INSERT INTO RESOURCES (ID, ATTRIBUTES, DATA, BRANCHID) VALUES (?, ?, ?, ?);", [res.ID, res.ATTRIBUTES, res.DATA, newBranchID], function (obj) {
            if (obj.error) {
                this._exitCall();
                this._finalCallbackWithError(callback, obj);
                return;
            }

            this.dbDriver.run(this.archiveID, "UPDATE COMMENTS SET BRANCHID = ? WHERE RESOURCEID = ? AND BRANCHID = ?;", [newBranchID, resourceID, oldBranchID], function (obj) {
                if (obj.error) {
                    this._exitCall();
                    this._finalCallbackWithError(callback, obj);
                    return;
                }

                this.dbDriver.run(this.archiveID, "DELETE FROM RESOURCES WHERE ID = ? AND BRANCHID = ?;", [resourceID, oldBranchID], function (obj) {
                    this._exitCall();
                    if (obj.error) {
                        this._finalCallbackWithError(callback, obj);
                    } else {
                        this._finalCallbackWithAffectedRowsCheck(callback, obj);
                    }
                }.bind(this));
            }.bind(this));
        }.bind(this));
    }.bind(this));
};

// read the data of the given resource
BalsamiqArchive.prototype.getResourceData = function (p_resourceID, branchID, callback) {
    this.getResourceDataWithOptions(p_resourceID, branchID, {}, callback);
};

BalsamiqArchive.prototype.getResourceDataWithOptions = function (p_resourceID, branchID, options, callback) {
    this._enterCall();

    var computeSHA1 = !!options.computeSHA1;
    var computeSHA1Query = computeSHA1 ? "sha1(DATA) as SHA1, " : "";

    this.dbDriver.get(this.archiveID, "SELECT " + computeSHA1Query + "DATA FROM RESOURCES WHERE ID = ? AND BRANCHID = ?;", [p_resourceID, branchID], function (obj) {
        var res;
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {
            try {
                if (obj.row && typeof obj.row.DATA === "string") {
                    res = {
                        data: obj.row.DATA
                    };
                    if (computeSHA1) {
                        res.sha1 = obj.row.SHA1;
                    }
                    this._getArchiveRevision(function (p_pv) {
                        this._finalCallback(callback, p_pv, res);
                    }.bind(this));
                } else {
                    this._finalCallbackWithError(callback, {error: "Balsamiq Archive error: Unexpected database response while getting resource data: " + JSON.stringify(obj)});
                }
            }
            catch (e) {
                this._finalCallbackWithError(callback, {error: "Balsamiq Archive error: Unexpected database response while getting resource data: " + JSON.stringify(obj)});
            }
        }
    }.bind(this));
};

BalsamiqArchive.prototype.getResourcesData = function (p_resourceIDs, branchID, callback)
{
    // var query, values, tmp = [], id;

    this._enterCall();

    this._internalGetResourcesData(p_resourceIDs, branchID, function (obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {
            this._getArchiveRevision(function (p_pv) {
                this._finalCallback(callback, p_pv, obj);
            }.bind(this));
        }
    }.bind(this));
};

BalsamiqArchive.prototype._internalGetResourcesData = function (p_resourceIDs, branchID, callback)
{
    var query, fixedValues;

    if (p_resourceIDs && p_resourceIDs.length) {
        if (branchID == Consts.Branch.AllBranches || branchID == null) {
            query = "SELECT ID, BRANCHID, DATA FROM RESOURCES WHERE ID in ([!!__VALUES__!!])";
            fixedValues = [];
        } else {
            query = "SELECT ID, BRANCHID, DATA FROM RESOURCES WHERE BRANCHID = ? AND ID in ([!!__VALUES__!!])";
            fixedValues = [branchID];
        }

        makeQueryInBatches(this.dbDriver, 'all', this.archiveID, query, fixedValues, p_resourceIDs, function (obj) {
            if (obj.error) {
                callback(obj);
            } else {
                var res = {data: {}};
                try {
                    for (var i = 0; i < obj.rows.length; i++) {
                        var s = obj.rows[i];
                        res.data[s.ID + "|" + s.BRANCHID] = s.DATA;
                    }
                    callback(res);
                }
                catch (e) {
                    callback({error: "Balsamiq Archive error: Unexpected database response while getting resources data: " + e.stack});
                }
            }
        }.bind(this));
    } else {
        callback({error: "Balsamiq Archive error: Unexpected parameter while getting resources data: p_resourceIDs is null or is not an valid"});
    }
};

// set the data of the given resource
BalsamiqArchive.prototype.setResourceData = function (p_resourceID, branchID, p_data, callback) {
    if (typeof p_data !== 'string') {
        this._finalCallbackWithError(callback, {error: "Balsamiq Archive error: p_data must be a String!"});
    } else {
        this._enterCall();

        this.dbDriver.run(this.archiveID, "UPDATE RESOURCES SET DATA = ? WHERE ID = ? AND BRANCHID = ?;", [p_data, p_resourceID, branchID], function (obj) {
            this._exitCall();
            if (obj.error) {
                this._finalCallbackWithError(callback, obj);
            } else {
                this._finalCallbackWithAffectedRowsCheck(callback, obj);
            }
        }.bind(this));
    }
};

BalsamiqArchive.prototype.getThumbnails = function (callback) {
    this._enterCall();
    this._internalGetThumbnails(function (obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj.error);
        }
        else {
            this._getArchiveRevision(function (p_pv) {
                this._finalCallback(callback, p_pv, obj);
            }.bind(this));
        }
    }.bind(this));
};

BalsamiqArchive.prototype._internalGetThumbnails = function (callback) {

    if (!this.checkInternalCall(callback)) {
        return;
    }
    this.dbDriver.all(this.archiveID, "SELECT ID, ATTRIBUTES FROM THUMBNAILS;", [], function (obj) {
        var attrs, i, p_record, res;
        if (obj.error) {
            callback(obj);
        } else {
            try {
                res = { thumbnails: [] };
                for (i=0; i<obj.rows.length; i++) {
                    p_record = obj.rows[i];
                    attrs = JSON.parse(p_record.ATTRIBUTES);
                    res.thumbnails.push({
                        ID: p_record.ID,
                        ATTRIBUTES: attrs
                    });
                }
                callback(res);
            } catch (e) {
                callback({error: "Balsamiq Archive error: Unexpected database response while getting thumbnails: " + JSON.stringify(obj)});
            }
        }
    }.bind(this));
};

BalsamiqArchive.prototype.createThumbnail = function (thumbnailID, attributes, callback) {
    this._enterCall();

    if (attributes === null || attributes === undefined) {
        attributes = {};
    }
    var values = [
        thumbnailID,
        JSON.stringify(attributes)
    ];
    this.dbDriver.run(this.archiveID, "INSERT INTO THUMBNAILS (ID, ATTRIBUTES) VALUES (?, ?);", values, function (obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {
            this._finalCallbackWithAffectedRowsCheck(callback, obj, {ID: thumbnailID});
        }
    }.bind(this));
};

BalsamiqArchive.prototype.getThumbnail = function (thumbnailID, callback) {
    this._enterCall();
    this.dbDriver.get(this.archiveID, "SELECT ID, ATTRIBUTES FROM THUMBNAILS WHERE ID = ?;", [thumbnailID], function (obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {
            try {
                if (obj.row) {
                    var res = {
                        ID: obj.row.ID,
                        ATTRIBUTES: JSON.parse(obj.row.ATTRIBUTES),
                    };
                    this._getArchiveRevision(function (p_pv) {
                        this._finalCallback(callback, p_pv, res);
                    }.bind(this));
                } else {
                    this._finalCallbackWithError(callback, {error: "Balsamiq Archive error: Unexpected database response while getting thumbnail: " + JSON.stringify(obj)});
                }
            } catch (e) {
                this._finalCallbackWithError(callback, {error: "Balsamiq Archive error: Unexpected database response while getting thumbnail: " + JSON.stringify(obj)});
            }
        }
    }.bind(this));
};

// TODO: to evaluate performance
BalsamiqArchive.prototype.getThumbnailFromResourceID = function (resourceID, branchID, callback) {
    var queryResourceID = "%" + resourceID + "%";
    var queryBranchID = "%" + branchID + "%";
    var resourceQuery, thumbnailAttribute;
    var res;

    this._enterCall();

    // getting image
    this.dbDriver.all(this.archiveID, "SELECT * FROM THUMBNAILS WHERE ATTRIBUTES LIKE ? AND ATTRIBUTES LIKE ?", [queryResourceID, queryBranchID], function (thumnbnailObj) {
        if (thumnbnailObj.error) {
            this._exitCall();
            this._finalCallbackWithError(callback, thumnbnailObj);
        } else {
            res = {
                id: "", image: "", resourceName: "", branchName: "", projectName: ""
            };
            if (thumnbnailObj.rows && thumnbnailObj.rows.length) {
                res.id = thumnbnailObj.rows[0].ID;
                try {
                    thumbnailAttribute = JSON.parse(thumnbnailObj.rows[0].ATTRIBUTES);
                } catch (e) {
                    this._exitCall();
                    this._finalCallbackWithError(callback, {error: "Balsamiq Archive error: Unexpected database response while getting thumbnail: " + thumnbnailObj.rows[0].ATTRIBUTES});
                    return;
                }
                res.image = thumbnailAttribute.image;

                // getting project name
                this._internalGetArchiveData(Consts.Info.ArchiveAttributes, "attributes", true, function (archiveObj) {
                    if (archiveObj.error) {
                        this._exitCall();
                        this._finalCallbackWithError(callback, archiveObj);
                    } else {
                        res.projectName = archiveObj.attributes.name;

                        // getting resource name (it is only in master branch)
                        resourceQuery = "SELECT ATTRIBUTES FROM RESOURCES WHERE ID = ? AND BRANCHID = ?;";
                        this.dbDriver.all(this.archiveID, resourceQuery, [resourceID, Consts.Branch.MasterBranchID], function (resourceObj) {
                            var resourceAttributes, row;

                            if (resourceObj.error) {
                                this._exitCall();
                                this._finalCallbackWithError(callback, resourceObj);
                            } else {
                                if (resourceObj.rows && resourceObj.rows.length) {
                                    row = resourceObj.rows[0];
                                    try {
                                        resourceAttributes = JSON.parse(row.ATTRIBUTES);
                                    } catch (e) {
                                        this._exitCall();
                                        this._finalCallbackWithError(callback, {error: "Balsamiq Archive error: Unexpected database response while getting thumbnail: " + row.ATTRIBUTES});
                                        return;
                                    }

                                    res.resourceName = resourceAttributes.name;

                                    // getting branch name (it is only in master branch)
                                    if (branchID !== Consts.Branch.MasterBranchID) {
                                        resourceQuery = "SELECT ATTRIBUTES FROM BRANCHES WHERE ID = ?;";
                                        this.dbDriver.all(this.archiveID, resourceQuery, [branchID], function (resourceObj) {
                                            var resourceAttributes, row;

                                            if (resourceObj.error) {
                                                this._exitCall();
                                                this._finalCallbackWithError(callback, resourceObj);
                                            } else {
                                                if (resourceObj.rows && resourceObj.rows[0]) {
                                                    row = resourceObj.rows[0];
                                                    try {
                                                        resourceAttributes = JSON.parse(row.ATTRIBUTES);
                                                    } catch (e) {
                                                        this._exitCall();
                                                        this._finalCallbackWithError(callback, {error: "Balsamiq Archive error: Unexpected database response while getting thumbnail: " + row.ATTRIBUTES});
                                                        return;
                                                    }
                                                    res.branchName = resourceAttributes.branchName;
                                                }
                                                this._exitCall();
                                                this._getArchiveRevision(function (p_pv) {
                                                    this._finalCallback(callback, p_pv, res);
                                                }.bind(this));
                                            }
                                        }.bind(this));
                                    } else {
                                        this._exitCall();
                                        this._getArchiveRevision(function (p_pv) {
                                            this._finalCallback(callback, p_pv, res);
                                        }.bind(this));
                                    }
                                } else {
                                    // resource does not exit
                                    this._exitCall();
                                    this._finalCallbackWithError(callback, {error: "Balsamiq Archive error: Unexpected database response while getting thumbnail: resource does not existing"});
                                }
                            }
                        }.bind(this));
                    }
                }.bind(this));
            } else {
                // resource does not exit
                this._exitCall();
                this._finalCallbackWithError(callback, {error: "Balsamiq Archive error: Unexpected database response while getting thumbnail: resource does not existing"});
            }
        }
    }.bind(this));
};

BalsamiqArchive.prototype.setThumbnail = function (thumbnailID, attributes, callback) {
    this._enterCall();

    this._internalSetThumbnail(thumbnailID, attributes, function (obj) {
            this._exitCall();
            if (obj.error) {
                this._finalCallbackWithError(callback, obj);
            } else {
                this._finalCallbackWithAffectedRowsCheck(callback, obj);
            }
        }.bind(this));
};

BalsamiqArchive.prototype._internalSetThumbnail = function (thumbnailID, attributes, callback) {
    if (!this.checkInternalCall(callback)) {
        return;
    }
    this.dbDriver.run(this.archiveID, "UPDATE THUMBNAILS SET ATTRIBUTES = ? WHERE ID = ?;", [JSON.stringify(attributes), thumbnailID], function (obj) {        
        callback(obj);
    }.bind(this));
};

BalsamiqArchive.prototype.deleteThumbnails = function (thumbnailIDs, callback) {
    this._enterCall();

    makeQueryInBatches(this.dbDriver, 'run', this.archiveID, "DELETE FROM THUMBNAILS WHERE ID in ([!!__VALUES__!!])", [], thumbnailIDs, function (obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {
            this._finalCallbackWithAffectedRowsCheck(callback, obj);
        }
    }.bind(this));
};

BalsamiqArchive.prototype.deleteThumbnailImages = function (callback) {
    this._enterCall();

    let thumbnails;
    let deletedImageIndexes = []; // indexes of deleted thumbnails.

    this._internalGetThumbnails(
        function (obj) {
            if (obj.error) {
                this._exitCall();
                this._finalCallbackWithError(callback, obj);
            } else {
                thumbnails = obj.thumbnails;
                for (let i = 0; i < thumbnails.length; i++) {
                    if (thumbnails[i].ATTRIBUTES.image) {
                        delete thumbnails[i].ATTRIBUTES.image;
                        deletedImageIndexes.push(i);
                    }
                }

                if (deletedImageIndexes.length > 0) {
                    const setThumbnailAttributes = (i) => {
                        const thumbnailIndex = deletedImageIndexes[i];

                        this._internalSetThumbnail(
                            thumbnails[thumbnailIndex].ID,
                            thumbnails[thumbnailIndex].ATTRIBUTES,
                            function (obj) {
                                if (i === deletedImageIndexes.length - 1) {
                                    this._exitCall();
                                    if (obj.error) {
                                        this._finalCallbackWithError(callback, obj);
                                    } else {
                                        this._finalCallbackWithAffectedRowsCheck(callback, obj);
                                    }
                                    return;
                                } else {
                                    if (obj.error) {
                                        this._exitCall();
                                        this._finalCallbackWithError(callback, obj);
                                    } else {
                                        setThumbnailAttributes(i + 1);
                                    }
                                }
                            }.bind(this)
                        );
                    };
                    setThumbnailAttributes(0);
                } else {
                    this._exitCall();
                    this._finalCallbackWithError(callback, {error: "Command affected no rows"});
                }
            }
        }.bind(this)
    );
}

BalsamiqArchive.prototype.deleteBranches = function (branchIDs, callback) {
    this._enterCall();

    makeQueryInBatches(this.dbDriver, 'run', this.archiveID, "DELETE FROM BRANCHES WHERE ID in ([!!__VALUES__!!])", [], branchIDs, function (obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {
            this._finalCallbackWithAffectedRowsCheck(callback, obj);
        }
    }.bind(this));
};

BalsamiqArchive.prototype.createBranch = function(p_branchID, p_attributes, callback) {
    var attr = JSON.stringify(p_attributes);
    this._enterCall();
    this.dbDriver.run(this.archiveID, "INSERT INTO BRANCHES (ID, ATTRIBUTES) VALUES (?, ?);", [p_branchID, attr], function (obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {
            this._finalCallbackWithAffectedRowsCheck(callback, obj);
        }
    }.bind(this));
};

// obj.comments array of {id, data}
BalsamiqArchive.prototype.getCommentsData = function(commentIDs, callback) {
    this._enterCall();

    this._internalGetCommentsData(commentIDs, false, function (obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {
            this._getArchiveRevision(function (p_pv) {
                this._finalCallback(callback, p_pv, obj);
            }.bind(this));
        }

    }.bind(this));
};

BalsamiqArchive.prototype._internalGetCommentsData = function(commentIDs, includeTrashedData, callback) {

    if (!this.checkInternalCall(callback)) {
        return;
    }

    var res = { comments: [] };

    if (commentIDs && commentIDs.length) {
        makeQueryInBatches(this.dbDriver, 'all', this.archiveID, "SELECT ID, BRANCHID, RESOURCEID, DATA, USERID, ATTRIBUTES FROM COMMENTS WHERE ID in ([!!__VALUES__!!])", [], commentIDs, function (obj) {
            var i, row;

            if (obj.error) {
                callback(obj);
            } else {
                try {
                    for (i=0; i<obj.rows.length; i++) {
                        row = obj.rows[i];
                        var attributes = JSON.parse(row.ATTRIBUTES);
                        res.comments.push({
                            ID: row.ID,
                            BRANCHID: row.BRANCHID,
                            RESOURCEID: row.RESOURCEID,
                            USERID: row.USERID,
                            ATTRIBUTES: attributes,
                            DATA: !includeTrashedData && attributes[Consts.CommentAttributes.Trashed] ? "{}" : row.DATA          // this is a string! as for input value
                        });
                    }
                    callback(res);
                } catch (e) {
                    this._finalCallbackWithError(callback, {error: "Balsamiq Archive error: Unexpected database response while getting comments data: " + JSON.stringify(obj)});
                }
            }
        }.bind(this));
    } else {
        callback(res);
    }
};

BalsamiqArchive.prototype.reCreateComment = function(commentID, resourceID, branchID, userID, attributes, data, callback) {

    this._enterCall();

    this.dbDriver.run(this.archiveID, "INSERT INTO COMMENTS (ID, RESOURCEID, BRANCHID, DATA, USERID, ATTRIBUTES) VALUES (?, ?, ?, ?, ?, ?);", [ commentID, resourceID, branchID, data, userID, JSON.stringify(attributes)], function(obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {

            var res = {
                commentID: commentID,
                resourceID: resourceID,
                branchID: branchID,
                userID: this.userInfo.name,
                attributes: attributes
            };

            this._finalCallbackWithAffectedRowsCheck(callback, obj, res);
        }
    }.bind(this));
};

// return id and attributes of newly created comment
// user id is not passed as a param because it is already known
BalsamiqArchive.prototype.createComment = function(commentID, resourceID, branchID, commentParentID, data, callback) {
    if (!BmprUtils.isValidUUID(commentID)) {
        this._finalCallbackWithError(callback, {error: "Invalid commentID."});
        return;
    }

    if (commentParentID && !BmprUtils.isValidUUID(commentParentID)) {
        this._finalCallbackWithError(callback, {error: "Invalid commentParentID."});
        return;
    }

    if (!BmprUtils.isValidBranchID(branchID)) {
        this._finalCallbackWithError(callback, {error: "Invalid branchID."});
        return;
    }

    this._enterCall();
    var dateTime = Date.now();

    var attributes = {};

    attributes[Consts.CommentAttributes.ParentID] =  commentParentID;
    attributes[Consts.CommentAttributes.ReadBy] =  [this.userInfo.name];
    attributes[Consts.CommentAttributes.Timestamp] =  dateTime;
    attributes[Consts.CommentAttributes.Trashed] =  false;
    attributes[Consts.CommentAttributes.TrashedBy] =  "";
    attributes[Consts.CommentAttributes.LikedBy] =  [];
    attributes[Consts.CommentAttributes.Timestamps] =  {};

    this.dbDriver.run(this.archiveID, "INSERT INTO COMMENTS (ID, BRANCHID, RESOURCEID, DATA, USERID, ATTRIBUTES) VALUES (?, ?, ?, ?, ?, ?);", [ commentID, branchID, resourceID, data, this.userInfo.name, JSON.stringify(attributes)], function(obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {

            var res = {
                commentID: commentID,
                resourceID: resourceID,
                branchID: branchID,
                userID: this.userInfo.name,
                attributes: attributes
            };

            this._finalCallbackWithAffectedRowsCheck(callback, obj, res);
        }
    }.bind(this));
};

/** @typedef {{ imported: boolean; likedBy: string[]; originalAuthor?: string; parentID: string; readBy: string[]; timestamp: number; timestamps: unknown; trashed: boolean; trashedBy: string; }} Attributes */

/** @typedef {{ color: number; id: string; label: string; labelColor: number; tip: "left" | "top" | "right" | "bottom"; x: number; y: number; }} Callout */

/** @typedef {{ id: string; data: { text: string, callouts: Callout[] }; attributes: Attributes; originalAuthorDisplayName: string; }} ApiComment */

/**
 *
 * @param {string} resourceID
 * @param {string} branchID
 * @param {ApiComment} comment
 * @param {() => void} callback
 */
BalsamiqArchive.prototype.importComment = function (resourceID, branchID, comment, callback) {
    this._enterCall();

    var attributes = {};

    attributes[Consts.CommentAttributes.ParentID] = comment.attributes.parentID;
    attributes[Consts.CommentAttributes.ReadBy] = [this.userInfo.name];
    attributes[Consts.CommentAttributes.Timestamp] = comment.attributes.timestamp;
    attributes[Consts.CommentAttributes.Trashed] = comment.attributes.trashed;
    attributes[Consts.CommentAttributes.TrashedBy] = comment.attributes.trashedBy === "" ? comment.attributes.trashedBy : Consts.IMPORTED_USER_ID;
    if (comment.attributes.resolved !== undefined) {
        attributes[Consts.CommentAttributes.Resolved] = comment.attributes.resolved;
        attributes[Consts.CommentAttributes.ResolvedBy] = comment.attributes.ResolvedBy === "" ? comment.attributes.ResolvedBy : Consts.IMPORTED_USER_ID;
    }
    attributes[Consts.CommentAttributes.LikedBy] = comment.attributes.likedBy.map(function () {
        return Consts.IMPORTED_USER_ID;
    });
    attributes[Consts.CommentAttributes.Timestamps] = comment.attributes.timestamps;
    attributes[Consts.CommentAttributes.Imported] = true;
    attributes[Consts.CommentAttributes.OriginalAuthor] = comment.originalAuthorDisplayName;

    this.dbDriver.run(
        this.archiveID,
        "INSERT INTO COMMENTS (ID, BRANCHID, RESOURCEID, DATA, USERID, ATTRIBUTES) VALUES (?, ?, ?, ?, ?, ?);",
        [comment.id, branchID, resourceID, JSON.stringify(comment.data), this.userInfo.name, JSON.stringify(attributes)],
        function (obj) {
            this._exitCall();
            if (obj.error) {
                this._finalCallbackWithError(callback, obj);
            } else {
                var res = {
                    commentID: comment.id,
                    attributes: attributes, 
                };
                this._finalCallbackWithAffectedRowsCheck(callback, obj, res);
            }
        }.bind(this)
    );
};

// user id is not passed as a param because it is already known
// returns an array of { commentID: xxx, attributes: {} }
BalsamiqArchive.prototype.updateCommentsAttributes = function(commentIDs, attributesArray, callback) {
    this._enterCall();
    this._internalUpdateCommentsAttributes(commentIDs, attributesArray, function(obj, res) {
        this._exitCall();

        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {
            this._finalCallbackWithAffectedRowsCheck(callback, obj, { attributes: res });
        }
    }.bind(this));
};

// user id is not passed as a param because it is already known
BalsamiqArchive.prototype._internalUpdateCommentsAttributes = function(commentIDs, attributes, callback) {

    if (!this.checkInternalCall(callback)) {
        return;
    }

    if (commentIDs === null || commentIDs === undefined) {
        callback({ error: "Unexpected error: commentIDs is null" });
        return;
    }

    if (commentIDs.length === 0) {
        callback({ affectedRows: 0 });
        return;
    }

    this._internalGetCommentsData(commentIDs, true, function(obj) {
        var tagList, index, tag, j;

        if (obj.error) {
            callback(obj);
        } else {

            if (obj.comments.length !== commentIDs.length) {
                callback({ affectedRows: 0 });
                return;
            }

            var newAttributesArr = [];
            for (var commentIndex = 0; commentIndex < commentIDs.length; ++commentIndex) {

                // important: the _internalGetCommentsData DO NOT return data in the same order commentIDs are sent as input!!
                var queryResultIndex = obj.comments.findIndex(function (comment) {
                    return comment.ID === commentIDs[commentIndex]
                }.bind(this));

                var oldAttributes = obj.comments[queryResultIndex].ATTRIBUTES;
                var userID = obj.comments[queryResultIndex].USERID;
                var commentID = obj.comments[queryResultIndex].ID;
                var commentData = obj.comments[queryResultIndex].DATA;
                var changed = false;
                var realAttribute;
                var userIndex;

                var newAttributes = {};
                var data;
                for (var attribute in oldAttributes) {
                    newAttributes[attribute] = oldAttributes[attribute]
                }

                if (!canUserUpdateCommentAttributes(this.userInfo.name, this.userRole, attributes[commentIndex], obj.comments[queryResultIndex].USERID)) {
                    callback({ error: CommentPermissionError });
                    return;
                }

                // smart attributes management
                for (var attributeSetter in attributes[commentIndex]) {

                    var attributeValue =  attributes[commentIndex][attributeSetter];

                    if (attributeSetter === Consts.CommentAttributesSetters.setReadStatus) {
                        realAttribute = newAttributes[Consts.CommentAttributes.ReadBy];
                        userIndex = -1;
                        for (j=0; j<realAttribute.length; j++) {
                            if (realAttribute[j] == this.userInfo.name) {
                                userIndex = j;
                                break;
                            }
                        }

                        if (attributeValue === true && userIndex === -1) {
                            realAttribute.push(this.userInfo.name);
                            changed = true;
                        }
                        else if (attributeValue === false && userIndex !== -1) {
                            realAttribute.splice(userIndex, 1);
                            changed = true;
                        }
                        break;
                    }
                    else if (attributeSetter === Consts.CommentAttributesSetters.setLikedStatus) {
                        realAttribute = newAttributes[Consts.CommentAttributes.LikedBy];

                        userIndex = -1;
                        for (j=0; j<realAttribute.length; j++) {
                            if (realAttribute[j] == this.userInfo.name) {
                                userIndex = j;
                                break;
                            }
                        }

                        if (attributeValue === true && userIndex === -1) {
                            realAttribute.push(this.userInfo.name);
                            changed = true;
                        } else if (attributeValue === false && userIndex !== -1) {
                            realAttribute.splice(userIndex, 1);
                            changed = true;
                        }

                        // remove user from readBy list to notify it that someone liked its comment
                        if (changed && userID !== this.userInfo.name) {
                            realAttribute = newAttributes[Consts.CommentAttributes.ReadBy];

                            for (j=0; j<realAttribute.length; j++) {
                                if (realAttribute[j] == userID) {
                                    realAttribute.splice(j, 1);
                                    break;
                                }
                            }
                        }

                        break;
                    }
                    else if (attributeSetter === Consts.CommentAttributesSetters.setTrashedStatus) {
                        if (oldAttributes[Consts.CommentAttributes.Trashed] === false && attributeValue === true) {

                            newAttributes[Consts.CommentAttributes.Trashed] = attributeValue;
                            newAttributes[Consts.CommentAttributes.TrashedBy] = this.userInfo.name;
                            changed = true;
                        }
                        else if (oldAttributes[Consts.CommentAttributes.Trashed] === true && attributeValue === false) {
                            newAttributes[Consts.CommentAttributes.Trashed] = attributeValue;
                            newAttributes[Consts.CommentAttributes.TrashedBy] = "";
                            data = commentData;
                            changed = true;
                        }
                    }
                    else if (attributeSetter === Consts.CommentAttributesSetters.setResolvedStatus) {
                        const oldResolvedAttribute = oldAttributes[Consts.CommentAttributes.Resolved];
                        if (oldResolvedAttribute === undefined || oldResolvedAttribute === false && attributeValue === true) {
                            newAttributes[Consts.CommentAttributes.Resolved] = attributeValue;
                            newAttributes[Consts.CommentAttributes.ResolvedBy] = this.userInfo.name;
                            changed = true;
                        }
                        else if (oldResolvedAttribute === true && attributeValue === false) {
                            newAttributes[Consts.CommentAttributes.Resolved] = attributeValue;
                            newAttributes[Consts.CommentAttributes.ResolvedBy] = "";
                            changed = true;
                        }
                    }
                    else if (attributeSetter === Consts.CommentAttributesSetters.setTimestamps) {
                        if (!newAttributes[Consts.CommentAttributes.Timestamps]) {
                            newAttributes[Consts.CommentAttributes.Timestamps] = {};
                        }

                        tagList = attributeValue;
                        for (index = 0; index < tagList.length; ++index) {
                            tag = tagList[index];
                            newAttributes[Consts.CommentAttributes.Timestamps][tag] = Date.now();
                        }
                        changed = true;
                    }
                }

                if (changed) {
                    newAttributesArr.push( {
                        commentID: commentID,
                        attributes: newAttributes,
                        attributesChangedData: data
                    })
                }
            }

            if (newAttributesArr.length > 0) {
                    this._recurseSetCommentsAttributes(newAttributesArr, 0, function (obj) {
                        if (obj.error) {
                            callback(obj);
                        }
                        else if (obj.affectedRows !== newAttributesArr.length) {
                            callback({ error: "not all changes affected rows", affectedRows: 0});
                        }
                        else {
                            callback({affectedRows: obj.affectedRows}, newAttributesArr);
                        }
                    }.bind(this));
            }
            else {
                callback({affectedRows: commentIDs.length}, newAttributesArr);
            }

        }
    }.bind(this));
};

BalsamiqArchive.prototype._recurseSetCommentsAttributes = function(attributes, affectedRows, callback) {

    this._internalSetCommentAttributes(attributes[0].commentID, attributes[0].attributes, function (obj) {
        if (obj.error || obj.affectedRows == 0) {
            callback(obj);
            return;
        }

        var totalAffectedRows = obj.affectedRows + affectedRows;

        var remainingAttributesToSet = attributes.slice(1);
        if (remainingAttributesToSet.length == 0) {
            callback( { affectedRows: totalAffectedRows });
        } else {
            this._recurseSetCommentsAttributes(remainingAttributesToSet, obj.affectedRows + affectedRows, callback);
        }
    }.bind(this));
};

// returns JSON of attributes
BalsamiqArchive.prototype._internalGetCommentAttributes = function(commentID, callback) {

    if (!this.checkInternalCall(callback)) {
        return;
    }

    this.dbDriver.get(this.archiveID, "SELECT ATTRIBUTES FROM COMMENTS WHERE ID = ?", [commentID], function (obj) {
        if (obj.error) {
            callback(obj);
        }
        else {
            try {
                callback({ attributes: JSON.parse(obj.row.ATTRIBUTES)});

            } catch (e) {
                callback({error: "Balsamiq Archive error: Unexpected database response while getting comment attributes: " + JSON.stringify(obj)});
            }
        }
    });
};

// newAttributes: object to be stringyfied
BalsamiqArchive.prototype._internalSetCommentAttributes = function(commentID, newAttributes, callback) {

    if (!this.checkInternalCall(callback)) {
        return;
    }

    this.dbDriver.run(this.archiveID, "UPDATE COMMENTS SET ATTRIBUTES = ? WHERE ID = ?", [JSON.stringify(newAttributes), commentID], function (obj) {
        callback(obj);
    });
};

BalsamiqArchive.prototype.setCommentData = function(commentID, data, callback) {

    this._enterCall();

    this._internalGetCommentsData([commentID], false, function(obj) {

        if (obj.error) {
            this._exitCall();
            this._finalCallbackWithError(callback, obj);
        } else {
            if (!canUserSetCommentData(this.userInfo.name, this.userRole, obj.comments[0].USERID)) {
                this._exitCall();
                this._getArchiveRevision(function (archiveRevision) {
                    this._finalCallback(callback, archiveRevision, {});
                }.bind(this));
                return;
            }
            var oldAttributes = obj.comments[0].ATTRIBUTES;
            // var userID = obj.comments[0].USERID;

            var newAttributes = {};
            for (var attribute in oldAttributes) {
                newAttributes[attribute] = oldAttributes[attribute]
            }

            // clear readby attributes with the exception of current user which edited it
            newAttributes[Consts.CommentAttributes.ReadBy] = [this.userInfo.name];

            this.dbDriver.run(this.archiveID, "UPDATE COMMENTS SET DATA = ?, ATTRIBUTES = ? WHERE ID = ?;", [data, JSON.stringify(newAttributes), commentID], function (obj) {
                this._exitCall();
                if (obj.error) {
                    this._finalCallbackWithError(callback, obj);
                }
                else {
                    this._finalCallbackWithAffectedRowsCheck(callback, obj, {
                        attributes: newAttributes,
                        data: data
                    });
                }
            }.bind(this));
        }
    }.bind(this));
};

BalsamiqArchive.prototype.deleteComments = function(commentIDs, callback) {

    this._enterCall();

    this._internalGetCommentsData(commentIDs, false, function (obj) {
        if (obj.error) {
            this._exitCall();
            this._finalCallbackWithError(callback, obj);
        } else {
            for (var i = 0; i < obj.comments.length; i++) {
                if (!canUserDeleteComment(this.userInfo.name, this.userRole, obj.comments[i].USERID)) {
                    this._exitCall();
                    this._finalCallbackWithError(callback, { error: CommentPermissionError })
                    return;
                }
            }
            makeQueryInBatches(this.dbDriver, 'run', this.archiveID, "DELETE FROM COMMENTS WHERE ID in ([!!__VALUES__!!])", [], commentIDs, function (obj) {
                this._exitCall();
                if (obj.error) {
                    this._finalCallbackWithError(callback, obj);
                } else {
                    this._finalCallbackWithAffectedRowsCheck(callback, obj);
                }
            }.bind(this));
        }
    }.bind(this));
};


// get array of users {id, attributes}
BalsamiqArchive.prototype.getArchiveUsersList = function(callback) {
    this._enterCall();
    // var res = { users: [] };
    this._internalGetArchiveUsersList(function (obj) {

        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        } else {
            this._getArchiveRevision(function (p_pv) {
                this._finalCallback(callback, p_pv, obj);
            }.bind(this));
        }
    }.bind(this));
};

// internal usage only
BalsamiqArchive.prototype.reCreateUser = function(userID, attributes, callback) {

    this._enterCall();
    this.dbDriver.run(this.archiveID, "INSERT INTO USERS (ID, ATTRIBUTES) VALUES (?, ?);", [ userID, JSON.stringify(attributes)], function (obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        }
        else {
            this._getArchiveRevision(function (p_pv) {
                this._finalCallback(callback, p_pv, obj);
            }.bind(this));
        }
    }.bind(this));
};

BalsamiqArchive.prototype.createMyUser = function(updateAttributes, callback) {

    this._enterCall();

    this._internalInsertUserIfNeeded(updateAttributes, function(obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        }
        else {
            this._getArchiveRevision(function (p_pv) {
                this._finalCallback(callback, p_pv, obj);
            }.bind(this));
        }
    }.bind(this));
};

BalsamiqArchive.prototype.updateMyUser = function(updateAttributes, callback) {

    this._enterCall();

    this._internalUpdateUser(updateAttributes, function(obj) {
        this._exitCall();
        if (obj.error) {
            this._finalCallbackWithError(callback, obj);
        }
        else {
            this._getArchiveRevision(function (p_pv) {
                this._finalCallback(callback, p_pv, obj);
            }.bind(this));
        }
    }.bind(this));
};

BalsamiqArchive.prototype.getHeuristicArchiveSize = function(callback) {
    this._enterCall();

    this.dbDriver.all(this.archiveID, "SELECT SUM(LENGTH(DATA)) AS sizeInBytes FROM RESOURCES", [], function (obj) {
        var size;
        if (obj.error) {
            this._exitCall();
            this._finalCallbackWithError(callback, obj);
        } else {
            if (obj.rows) {
                size = obj.rows[0]['sizeInBytes'];
                this.dbDriver.all(this.archiveID, "SELECT SUM(LENGTH(ATTRIBUTES)) AS sizeInBytes FROM THUMBNAILS", [], function (obj) {
                    this._exitCall();
                    if (obj.rows) {
                        size += obj.rows[0]['sizeInBytes'];
                        this._getArchiveRevision(function (p_pv) {
                            this._finalCallback(callback, p_pv, {heuristicArchiveSize: size});
                        }.bind(this));
                    } else {
                        this._finalCallbackWithError(callback, {error: "unexpected result from query"});
                    }
                }.bind(this));
            } else {
                this._exitCall();
                this._finalCallbackWithError(callback, {error: "unexpected result from query"});
            }
        }
    }.bind(this));
};

BalsamiqArchive.prototype._internalGetArchiveUsersList = function(callback) {

    if (!this.checkInternalCall(callback)) {
        return;
    }

    var res = { users: [] };
    this.dbDriver.all(this.archiveID, "SELECT ID, ATTRIBUTES FROM USERS", [], function(obj) {
        var i, row, attrs;
        if (obj.error) {
            callback(obj);
        } else {
            try {
                for (i=0; i<obj.rows.length; i++) {
                    row = obj.rows[i];
                    attrs = JSON.parse(row.ATTRIBUTES);
                    res.users.push({
                        ID: row.ID,
                        ATTRIBUTES: attrs
                    });
                }
                callback(res);
            } catch (e) {
                callback({error: "Balsamiq Archive error: Unexpected database response while getting users list: " + JSON.stringify(obj)});
            }
        }
    }.bind(this));
};

BalsamiqArchive.prototype._internalGetCommentsToc = function(callback) {

    // TODO: no check here, debug only function
    // if (!this.checkInternalCall(callback)) {
    //     return;
    // }

    var res = { comments: [] };
    this.dbDriver.all(this.archiveID, "SELECT ID, BRANCHID, RESOURCEID, USERID, ATTRIBUTES FROM COMMENTS", [], function (obj) {
        var i, row;

        if (obj.error) {
            callback(obj);
        } else {
            try {
                for (i=0; i<obj.rows.length; i++) {
                    row = obj.rows[i];
                    // var attrs = JSON.parse(row.ATTRIBUTES);
                    res.comments.push({
                        ID: row.ID,
                        BRANCHID: row.BRANCHID,
                        RESOURCEID: row.RESOURCEID,
                        USERID: row.USERID,
                        ATTRIBUTES: JSON.parse(row.ATTRIBUTES)
                    });
                }
                callback(res);
            } catch (e) {
                callback({error: "Balsamiq Archive error: Unexpected database response while getting comments TOC: " + JSON.stringify(obj)});
            }
        }
    }.bind(this));
};

BalsamiqArchive.prototype._internalGetComments = function(callback) {

    // TODO: no check here, debug only function
    // if (!this.checkInternalCall(callback)) {
    //     return;
    // }

    var res = { comments: [] };
    this.dbDriver.all(this.archiveID, "SELECT ID, BRANCHID, RESOURCEID, USERID, ATTRIBUTES, DATA FROM COMMENTS", [], function (obj) {
        var i, row;
        if (obj.error) {
            callback(obj);
        } else {
            try {
                for (i=0; i<obj.rows.length; i++) {
                    row = obj.rows[i];
                    // var attrs = JSON.parse(row.ATTRIBUTES);
                    res.comments.push({
                        ID: row.ID,
                        BRANCHID: row.BRANCHID,
                        RESOURCEID: row.RESOURCEID,
                        USERID: row.USERID,
                        ATTRIBUTES: JSON.parse(row.ATTRIBUTES),
                        DATA: row.DATA          // this is a string! as for input value
                    });
                }
                callback(res);
            } catch (e) {
                callback({error: "Balsamiq Archive error: Unexpected database response while getting comments TOC: " + JSON.stringify(obj)});
            }
        }
    }.bind(this));
};

// call this function at the beginning of each API
BalsamiqArchive.prototype._enterCall = function () {
    // in case the busy guard is inconsistent we restart the server
    if (!this.dbDriver) {
        throw new Error("Balsamiq Archive error: no DBDriver db is open!");
    }
    if (this.busy) {
        throw new Error("Balsamiq Archive error: I am already busy, you can't call me now!");
    }
    this.busy = true;
    if (this.dbDriver.beginSequence) {
        this.dbDriver.beginSequence(this.archiveID);
    }
};

// call this function at the end of each API
BalsamiqArchive.prototype._exitCall = function () {
    // in case the busy guard is inconsistent we restart the server
    if (!this.busy) {
        throw new Error("Balsamiq Archive error: _exitCall called when this.busy is false!");
    }
    this.busy = false;
};

// invoke the user callback returning the archive Revision and an optional result
BalsamiqArchive.prototype._finalCallback = function (callback, p_archiveRevision, p_result) {
    if (this.dbDriver.endSequence) {
        this.dbDriver.endSequence(this.archiveID);
    }
    var obj;
    if (p_archiveRevision.error) {
        callback && callback(p_archiveRevision);
    } else {
        obj = p_result ? p_result : {};
        obj.archiveRevision = p_archiveRevision;
        callback && callback(obj);
    }
};

// invoke the user callback returning an error
BalsamiqArchive.prototype._finalCallbackWithError = function (callback, p_error) {
    if (this.dbDriver.endSequence) {
        this.dbDriver.endSequence(this.archiveID);
    }
    if (callback) {
        callback(p_error);
    }
};

// invoke the user callback returning an error
BalsamiqArchive.prototype._finalCallbackWithAffectedRowsCheck = function (callback, sqlCommandResult, callbackResult) {
    if (sqlCommandResult.affectedRows >= 1) {
        this._incrementArchiveRevision(function (p_pv) {
            this._finalCallback(callback, p_pv, callbackResult);
        }.bind(this));
    } else {
        this._finalCallbackWithError(callback, {error: "Command affected no rows"});
    }
};

// read and return the archive Revision
BalsamiqArchive.prototype._getArchiveRevision = function (callback) {
    // no need to check this.busy here

    // TODO no check for an internal call here

    this.dbDriver.get(this.archiveID, "SELECT VALUE FROM INFO WHERE NAME = ?;", [Consts.Info.ArchiveRevision], function (obj) {
        var resp;
        if (obj.error) {
            callback && callback(obj);
        }
        else {
            try {
                resp = parseInt(obj.row.VALUE, 10);
            }
            catch (e) {
                resp = {error: "Balsamiq Archive error: Unexpected database response while getting archive revision: " + JSON.stringify(obj)};
            }
            callback && callback(resp);
        }
    });
};

// increment and return the archive Revision
BalsamiqArchive.prototype._incrementArchiveRevision = function (callback) {

    // TODO no check for an internal call here
    // if (!this.checkInternalCall(callback)) {
    //     return;
    // }

    // no need to check this.busy here
    this.dbDriver.run(this.archiveID, "UPDATE INFO SET VALUE = VALUE + 1 WHERE NAME = ?;", [Consts.Info.ArchiveRevision], function (obj) {
        if (obj.error) {
            callback && callback(obj);
        }
        else {
            this._getArchiveRevision(callback);
        }
    }.bind(this));
};

// read the value of the given metadata key
BalsamiqArchive.prototype._getArchiveData = function (p_key, callback, p_resultKey, p_isJSON) {

    this._enterCall();
    this._internalGetArchiveData(p_key, p_resultKey, p_isJSON, function (res) {
        this._exitCall();
        if (res.error) {
            this._finalCallbackWithError(callback, res);
        } else {
            this._getArchiveRevision(function (p_pv) {
                this._finalCallback(callback, p_pv, res);
            }.bind(this));
        }
    }.bind(this));
};

// no need to check for busy here
BalsamiqArchive.prototype._internalGetArchiveData = function (p_key, p_resultKey, p_isJSON, callback) {

    if (!this.checkInternalCall(callback)) {
        return;
    }

    if (!Consts.Info[p_key]) {
        callback({error: "Balsamiq Archive error: using unknown key " + p_key + " while getting archive data"});
    } else {

        this.dbDriver.get(this.archiveID, "SELECT NAME, VALUE FROM INFO WHERE NAME = ?;", [p_key], function (obj) {

            if (obj.error) {
                callback(obj);
            } else {
                var res = {};
                if (obj.row) {
                    res[p_resultKey] = p_isJSON ? JSON.parse(obj.row.VALUE) : obj.row.VALUE;
                }
                callback(res);

            }
        }.bind(this));
    }
};

// set the value of the given metadata key
BalsamiqArchive.prototype._setArchiveData = function (p_key, p_value, callback) {
    if (!Consts.Info[p_key]) {
        this._finalCallbackWithError(callback, {error: "Balsamiq Archive error: using unknown key " + p_key + " while setting archive data"});
    } else {
        this._enterCall();

        this.dbDriver.run(this.archiveID, "REPLACE INTO INFO (NAME, VALUE) VALUES (?, ?);", [p_key, p_value], function (obj) {
            this._exitCall();
            if (obj.error) {
                this._finalCallbackWithError(callback, obj);
            } else {
                this._finalCallbackWithAffectedRowsCheck(callback, obj);
            }
        }.bind(this));
    }
};

// TODO no enterCall/existCall: internal usage only - no need to check this.busy
BalsamiqArchive.prototype._migrate1To2 = function(callback) {

    if (!this.checkInternalCall(callback)) {
        return;
    }

    if (this.dbDriver.run) {
        this._internalGetArchiveData(Consts.Info.SchemaVersion, Consts.Info.SchemaVersion, false, function(obj) {
            var query;
            if (obj.error) {
                callback(obj);
            } else {
                if (obj[Consts.Info.SchemaVersion] === Consts.CurrentSchemaVersion) {
                    callback({error: "Balsamiq Archive error: attempt to upgrade new database schema"});
                }
                else {
                    if (this.dbDriver.type == "mysql") {
                        query = "CREATE TABLE USERS (ID VARCHAR(255) PRIMARY KEY, ATTRIBUTES TEXT CHARACTER SET 'utf8mb4' COLLATE 'utf8mb4_unicode_ci');";
                    } else {
                        query = "CREATE TABLE USERS (ID VARCHAR(255) PRIMARY KEY, ATTRIBUTES TEXT);";
                    }
                    this.dbDriver.run(this.archiveID, query, {}, function(obj) {
                        if (obj.error) {
                            callback(obj);
                        } else {
                            if (this.dbDriver.type == "mysql") {
                                query = "CREATE TABLE COMMENTS (ID VARCHAR(255) PRIMARY KEY, BRANCHID VARCHAR(255), RESOURCEID VARCHAR(255), DATA LONGTEXT CHARACTER SET 'utf8mb4' COLLATE 'utf8mb4_unicode_ci', USERID VARCHAR(255), ATTRIBUTES TEXT CHARACTER SET 'utf8mb4' COLLATE 'utf8mb4_unicode_ci', FOREIGN KEY (USERID) REFERENCES USERS(ID), FOREIGN KEY (RESOURCEID, BRANCHID) REFERENCES RESOURCES(ID, BRANCHID));";
                            } else {
                                query = "CREATE TABLE COMMENTS (ID VARCHAR(255) PRIMARY KEY, BRANCHID VARCHAR(255), RESOURCEID VARCHAR(255), DATA LONGTEXT, USERID VARCHAR(255), ATTRIBUTES TEXT, FOREIGN KEY (USERID) REFERENCES USERS(ID), FOREIGN KEY (RESOURCEID, BRANCHID) REFERENCES RESOURCES(ID, BRANCHID));";
                            }
                            this.dbDriver.run(this.archiveID, query, {}, function(obj) {
                                if (obj.error) {
                                    callback(obj);
                                } else {
                                    this.dbDriver.run(this.archiveID, "UPDATE INFO SET VALUE = ? WHERE NAME = ?;", [Consts.CurrentSchemaVersion, Consts.Info.SchemaVersion], function(obj) {
                                        if (obj.error) {
                                            callback(obj);
                                        } else {
                                            // IMPORTANT: avoid to increase the revision number after the migration
                                            // After incrementing the revision number the BMPR cannot be unloaded in case it is opened by user with read only permissions (i.e. not able to save to platform)
                                            // this.dbDriver.run(this.archiveID, "UPDATE INFO SET VALUE = VALUE + 1 WHERE NAME = ?;", [Consts.Info.ArchiveRevision], function (obj) {
                                                obj.migrated = true;
                                                callback(obj);
                                            // }.bind(this))
                                        }
                                    }.bind(this));
                                }
                            }.bind(this));
                        }
                    }.bind(this));
                }
            }
        }.bind(this));
    } else {
        // we are in read only mode (e.g. db opened via createFromURL)
        callback({error: Consts.ErrorCodes.NeedToMigrate})
    }
};

BalsamiqArchive.prototype.checkInternalCall = function(callback) {
    if (!this.busy) {
        callback({ error: "internal call require previous enterCall" });
        return false;
    }

    return true;
};

var getThumbnailFromDump = function (resourceID, branchID, dump, callback) {
    var i, attributes, ret = {};
    ret.projectName = dump.Info.ArchiveAttributes.name;
    ret.archiveRevision = dump.Info.ArchiveRevision;
    for (i=0; i<dump.Resources.length; i++) {
        attributes = dump.Resources[i].ATTRIBUTES;
        if (dump.Resources[i].ID === resourceID && dump.Resources[i].BRANCHID === branchID) {
            ret.resourceName = attributes.name;
            break;
        }
    }

    for (i=0; i<dump.Thumbnails.length; i++) {
        attributes = dump.Thumbnails[i].ATTRIBUTES;
        if (attributes.resourceID === resourceID && attributes.branchID === branchID) {
            ret.image = attributes.image;
            break;
        }
    }
    callback && callback(ret);
};

function makeSqlPlaceholdersForArray(arr) {
    return arr.map(function () { return '?'; }).join(', ');
}


var QUERY_MAX_NUM_VALUES = 500;

function makeQueryInBatches(dbDriver, methodName, dbName, queryTemplate, fixedValues, values, callback) {
    var result = {
        rows: [],
        affectedRows: 0
    };
    if (values.length === 0) {
        callback(result);
        return;
    }
    var driverMethod = dbDriver[methodName].bind(dbDriver);
    var batches = splitArrayInChunks(values, QUERY_MAX_NUM_VALUES);

    var processNextBatch = function () {
        var values = batches[0];
        batches = batches.slice(1);
        var query = queryTemplate.replace('[!!__VALUES__!!]', makeSqlPlaceholdersForArray(values));
        driverMethod(dbName, query, fixedValues.concat(values), function (obj) {
            if (obj.error) {
                callback(obj);
            } else {
                result.affectedRows += obj.affectedRows;
                result.rows = result.rows.concat(obj.rows);
                if (batches.length > 0) {
                    processNextBatch();
                } else {
                    callback(result);
                }
            }
        });
    };
    processNextBatch();
}

function splitArrayInChunks(arr, chunkSize) {
    var chunks = [];
    for (var i=0; i<arr.length; i += chunkSize) {
        chunks.push(arr.slice(i, i+chunkSize));
    }
    return chunks;
}

function canUserUpdateCommentAttributes(userID, userRole, attributesToUpdate, commentUserID) {
    if (userRole < Consts.Role.ROLE_COMMENTER) {
        return false;
    }

    var isCommentAuthor = userID === commentUserID;
    var isCommenter = userRole === Consts.Role.ROLE_COMMENTER;
    for (var attributeSetter in attributesToUpdate) {
        switch (attributeSetter) {
            case Consts.CommentAttributesSetters.setReadStatus:
            case Consts.CommentAttributesSetters.setLikedStatus:
                break;
            case Consts.CommentAttributesSetters.setResolvedStatus:
            case Consts.CommentAttributesSetters.setTrashedStatus:
            case Consts.CommentAttributesSetters.setTimestamps:
                if (isCommenter && !isCommentAuthor) {
                    return false;
                }
                break;
            default:
                return false;
        }
    }

    return true;
}

function canUserSetCommentData(userID, userRole, commentUserID) {
    if (userRole > Consts.Role.ROLE_COMMENTER) {
        return true;
    }
    if (userRole === Consts.Role.ROLE_COMMENTER) {
        return userID === commentUserID
    }
    return false;
}

function canUserDeleteComment(userID, userRole, commentUserID) {
    if (userRole < Consts.Role.ROLE_COMMENTER) {
        return false;
    }

    if (userRole == Consts.Role.ROLE_COMMENTER) {
        return userID === commentUserID;
    }

    // EDITOR/ADMIN can delete all comments
    return true;
}

exports.MasterBranchID = Consts.Branch.MasterBranchID;
exports.getThumbnailFromDump = getThumbnailFromDump;
exports.BalsamiqArchive = BalsamiqArchive;
exports.canUserUpdateCommentAttributes = canUserUpdateCommentAttributes;
exports.canUserSetCommentData = canUserSetCommentData;
exports.canUserDeleteComment = canUserDeleteComment;
