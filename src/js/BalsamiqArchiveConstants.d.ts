export enum Branch {
    MasterBranchID = "Master",
    AllBranches = "All",
    NoBranch = "None",
}

// Keys in INFO
export enum Info {
    SchemaVersion = "SchemaVersion", // integer
    ArchiveFormat = "ArchiveFormat", // string "bmpr"
    ArchiveRevision = "ArchiveRevision", // integer
    ArchiveAttributes = "ArchiveAttributes", // json string
    ArchiveRevisionUUID = "ArchiveRevisionUUID",
}

export enum ErrorCodes {
    ArchiveNotOpen = "ArchiveNotOpen",
    UserUnknown = "UserUnknown",
    OutOfDate = "OutOfDate",
    CreateFailed = "CreateFail",
    SchemaVersionNotSupported = "SchemaVersionNotSupported",
    NeedToMigrate = "NeedToMigrate",
    ArchiveNotLoaded = "ArchiveNotLoaded",
    MaintenanceMode = "MaintenanceMode",
}

export enum Role {
    ROLE_NO_ACCESS = 0,
    ROLE_VIEWER = 1,
    ROLE_COMMENTER = 2,
    ROLE_EDITOR = 3,
    ROLE_ADMIN = 4,
}

export enum LikeStatus {
    like = "like",
    unlike = "unlike",
}

export enum defaultUserInfo {
    name = "default",
    avatarURL = "",
}

export enum CommentAttributes {
    ParentID = "parentID", // String UUID
    ReadBy = "readBy", // Array of userIDs
    Timestamp = "timestamp", // Integer UNIX Time in ms
    Trashed = "trashed", // Boolean
    TrashedBy = "trashedBy", // userID
    LikedBy = "likedBy", // Array of userIDs
    Timestamps = "timestamps", // Object in the form { label: timestamp }
    Imported = "imported", // Boolean
    OriginalAuthor = "originalAuthor", // string
    Resolved = "resolved", // Boolean || undefined
    ResolvedBy = "resolvedBy", // userID || undefined
}

export enum CommentAttributesSetters {
    setReadStatus = "setReadStatus", // Boolean
    setLikedStatus = "setLikedStatus", // Boolean
    setTrashedStatus = "setTrashedStatus", // Boolean
    setTimestamps = "setTimestamps", // array of strings
    setResolvedStatus = "setResolvedStatus", // Boolean
    deleteTimestamps = "deleteTimestamps", // DEPRECATED array of strings
    setParentID = "setParentID", // DEPRECATED String
    setTimestamp = "setTimestamp", // DEPRECATED Integer UNIX Time in ms
    setImported = "imported", // DEPRECATED Boolean
    setOriginalAuthor = "originalAuthor", // DEPRECATED string
}

export enum CommentData {
    Text = "text",
    Callouts = "callouts",
}

// used when importing comments
export const IMPORTED_USER_ID = "IMPORTED_USER_ID";

export enum BinaryDataType {
    ARRAYBUFFER = "arraybuffer",
    BASE64 = "base64",
}

export enum SchemaVersions {
    OneDotTwo = "1.2",
    Two = "2.0",
}

export const CurrentSchemaVersion = SchemaVersions.Two;

export declare const PermalinkKind: {
    readonly image: "image";
    readonly public_share: "public_share";
    readonly image_unfurling: "image_unfurling";
    readonly url_unfurling: "url_unfurling";
};

export type PermalinkKind = typeof PermalinkKind[keyof typeof PermalinkKind];
  