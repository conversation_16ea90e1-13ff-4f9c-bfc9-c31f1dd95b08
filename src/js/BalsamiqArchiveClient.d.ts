import { BalsamiqArchiveBase, ErrorRes } from "./BalsamiqArchiveBase";
import { ArchiveClientOpenResult } from "./BalsamiqArchiveDataFormat";
import * as BARConstants from "./BalsamiqArchiveConstants";

type PermalinkInfo = {
    resourceID: string; 
    branchID: string;
    permalinkID: string;
    permalinkKind: BARConstants.PermalinkKind;
    platformKind: string;
    permalinkInfo: {
        image: {
            format: string;
        };
        dataResidency: string | null;
    };
    dirty: boolean;
    timestamp: number;
    image: string;
    edit: string;
}

type GetPermalinkDataRes = PermalinkInfo

export type ArchiveUserInfo = {
    id: number;
    fullName: string;
    email: string;
    userName: string;
    avatarUrl: string;
}

type GetProjectMembersRes = {
    users: ArchiveUserInfo[];
}

type GetPermalinksDataRes = {
    permalinksData: PermalinkInfo[];
}

type BroadcastMessageRes = {
    id: string;
    user: string;
    type: string;
    data: Record<string, unknown>; // resourceID, branchID, selectedControlIDs: number[][], actualRole: number
    ignore: boolean;
}

type DeletePermalinkDataRes = {
    message: string;
}

type CreateOrUpdateImageLinkRes = PermalinkInfo

export interface BalsamiqArchiveClient extends BalsamiqArchiveBase {
    fetch(url: string, callback: (data: unknown) => void): void;
    createWithPlatformArchiveID(platformSiteID: string, platformArchiveID: string, platformArchiveName: string, platformInfo: unknown, archiveAttributes: Record<string, unknown> | null | undefined, callback: (res: ErrorRes | {}) => void): BalsamiqArchiveClient;
    setPlatformToken(token: string, refreshSession: () => void, callback: () => void): void;
    create(platformSiteID: string, platformArchiveName: string, platformInfo: Record<string, unknown> | null | undefined, callback: (res: ErrorRes | {}) => void): void;
    setPlatformToken(token: string, refreshSession?: boolean, callback?: () => void): void;
    openByPublicShareID(publicShareID: string, options: Record<string, unknown>, callback: (res: ArchiveClientOpenResult) => void): void;
    openFromServer(platformSiteID: string, platformArchiveID: string, platformInfo: Record<string, unknown>, options: Record<string, unknown>, callback: (res: ArchiveClientOpenResult) => void): void;
    open(platformSiteID: string, platformArchiveID: string, platformArchiveName: string, platformInfo: Record<string, unknown>, permissions: unknown, options: Record<string, unknown>, callback: (res: ArchiveClientOpenResult) => void): void;
    get timeouts(): { [key: string]: number| null };
    set timeouts(value: { [key: string]: number| null });
    logUserEvent(event: Record<string, unknown>, callback: (res: ErrorRes | { data: Record<string, unknown>}) => void): void;
    getUserAuthToken(callback: (res: ErrorRes | { token: string}) => void): void;
    downloadBmprlLink(link: string, callback: (res: ErrorRes | { data: string }) => void): void;
    downloadTempFile(key: string, callback: (res: ErrorRes | string ) => void): void;
    uploadTempFile(data: unknown, callback: (res: ErrorRes | {}) => void): void;
    getProjectMembers(data:{
        projectId: any;
        searchString: string | undefined;
        mentionedUserIds: string[] | undefined;
    }, callback: (res: GetProjectMembersRes | ErrorRes) => void): void;
    
    getPermalinksData(callback: (res: GetPermalinksDataRes | ErrorRes) => void): void;
    getPermalinkData(resourceID: string, branchID: string, callback: (res: GetPermalinkDataRes | ErrorRes) => void): void;
    createOrUpdateImageLink(resourceID: string, branchID: string, permalinkInfo: unknown, base64data: string | null, mimeType: string | null, callback: (res: CreateOrUpdateImageLinkRes | ErrorRes) => void): void;
    closeArchiveWithoutFlushing(callback: (res:ErrorRes | {}) => void): void;
    close(callback: (res: ErrorRes | {}) => void): void;
    flushExt(options: Record<string, unknown>, callback: (res: ErrorRes | {}) => void): void;
    broadcastMessage(message: unknown, callback: (res: BroadcastMessageRes | ErrorRes) => void): void;
    deletePermalinkData(resourceID: string, branchID: string, callback: (res: DeletePermalinkDataRes) => void): void;
    cancelDownload(): void;
    get userID(): string;
    closeWithBeacon(): boolean;
    closeSync(): void;
}
