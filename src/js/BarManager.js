'use strict';

var BalsamiqArchiveClientModule = require('./BalsamiqArchiveClient');
var BmprUtilsUtilsModule = require('./BmprUtils');
var BalsamiqArchiveConstantsModule = require('./BalsamiqArchiveConstants');
var BmprConstantsModule = require('./BmprConstants');

var serverURL;
var rtcClient;

var barClients = {};
var rtcChannel = {};

var RTC_update = true;

function rtc_fromMe(message) {
    var i, fromMe = false, ids;
    ids = Object.keys(barClients);
    for (i=0; i<ids.length; i++) {
        var barClient = barClients[ids[i]];
        if (message.author == barClient.userID || (barClient.userInfo && message.username == barClient.userInfo.name)) {
            fromMe = true;
            break;
        }
    }
    return fromMe;
}

function finaliseOpenArchive(obj, callback, platformArchiveID, barClient, doRTCJob, userInfo, branchID) {
    if (obj.error) {
        if (obj.error == BalsamiqArchiveConstantsModule.ErrorCodes.SchemaVersionNotSupported) {
            obj.error = "Oops! The format of project archive is not supported. Please contact <NAME_EMAIL>";
        }

        callback && callback(obj);
    } else {
        var finalise = function (obj) {
            barClients[platformArchiveID] = barClient;
            if (obj.rtcChannel && doRTCJob && rtcClient) {
                rtcChannel[platformArchiveID] = rtcClient.subscribe({
                    channelName: obj.rtcChannel,
                    onMessage: function(message/*, env, channel*/){
                        var fromMe = rtc_fromMe(message);
                        // log("RTC message " + (fromMe ? "from me": "from others")/* + ": " + channel, "Message: " + JSON.stringify(message).substring(1, 255)*/);
                        if (RTC_update) {
                            doRTCJob(platformArchiveID, message, fromMe);
                        }
                    },
                    getTokenInfo: function () {
                        return new Promise(function (resolve, reject) {
                            barClient.getRTCAuthToken(function (result) {
                                if (result.error) {
                                    reject(new Error(result.error_message));
                                } else {
                                    resolve(result);
                                }
                            });
                        });
                    },
                });
                callback && callback(obj);
            } else {
                // the archive is not yet loaded on BAS (e.g. attached to the issue)
                callback && callback(obj);
            }
        };

        if (branchID == BalsamiqArchiveConstantsModule.Branch.NoBranch) {
            finalise(obj);
        } else {
            // get the data for everything except other assets and add it to "obj"
            var resourceIDs = [];
            var i, attrs, kind;
            var commentsIDs = [];

            for (i = 0; i < obj.dump.Resources.length; i++) {
                attrs = obj.dump.Resources[i].ATTRIBUTES;
                kind = attrs[BmprConstantsModule.ResourceAttributes.Kind];
                if (kind != BmprConstantsModule.ResourceKinds.OtherAsset) {
                    resourceIDs.push(obj.dump.Resources[i].ID);
                } else {
                    // TODO: [WORKAROUND] N2W crash if DATA is undefined
                    obj.dump.Resources[i].DATA = "";
                }
            }

            barClient.getResourcesData(resourceIDs, branchID, function (resourcesDataObj) {
                for (i = 0; i < obj.dump.Resources.length; i++) {
                    if (resourcesDataObj.data[obj.dump.Resources[i].ID + "|" + obj.dump.Resources[i].BRANCHID]) {
                        obj.dump.Resources[i].DATA = resourcesDataObj.data[obj.dump.Resources[i].ID + "|" + obj.dump.Resources[i].BRANCHID];
                    }
                }

                for (i = 0; i < obj.dump.Comments.length; i++) {
                    commentsIDs.push(obj.dump.Comments[i].ID);
                }

                barClient.getCommentsData(commentsIDs, function (commentsDataObj) {
                    var j, comment;
                    for (i = 0; i < obj.dump.Comments.length; i++) {
                        for (j = 0; j < commentsDataObj.comments.length; j++) {
                            comment = commentsDataObj.comments[j];
                            if (comment.ID === obj.dump.Comments[i].ID) {
                                obj.dump.Comments[i].DATA = comment.DATA;
                                break;
                            }
                        }
                    }

                    finalise(obj);
                });
            });
        }
    }
}

function doOpenArchiveByPublicShareID(authToken, userInfo, kind, publicShareID, options, doRTCJob, callback) {
    var barClient;
    var branchID;

    if (serverURL) {
        if (publicShareID) {
            barClient = new BalsamiqArchiveClientModule.BalsamiqArchiveClient(serverURL, kind, authToken, userInfo);
            branchID = options && options.branchID ? options.branchID : BalsamiqArchiveConstantsModule.Branch.MasterBranchID;
            barClient.openByPublicShareID(publicShareID, {
                branchID: branchID
            }, function(obj) {
                finaliseOpenArchive(obj, callback, publicShareID, barClient, doRTCJob, userInfo, branchID);
            });
        } else
        {
            callback && callback({error: "publicShareID is null"})
        }
    } else {
        callback && callback({error: "BarClient is not initialized, call init(serverURL) first"})
    }
}

function doOpenArchive(authToken, userInfo, kind, platformArchiveID, platformSiteID, platformArchiveName, platformInfo, permissions, options, doRTCJob, callback) {
    var barClient;
    var branchID;
    var info = platformInfo || {};

    if (serverURL) {
        if (platformArchiveID) {
            barClient = new BalsamiqArchiveClientModule.BalsamiqArchiveClient(serverURL, kind, authToken, userInfo);
            branchID = options && options.branchID ? options.branchID : BalsamiqArchiveConstantsModule.Branch.MasterBranchID;
            barClient.open(platformSiteID, platformArchiveID, platformArchiveName, info, permissions, {
                branchID: branchID
            }, function(obj) {
                finaliseOpenArchive(obj, callback, platformArchiveID, barClient, doRTCJob, userInfo, branchID);
            });
        } else
        {
            callback && callback({error: "platformArchiveID is null"})
        }
    } else {
        callback && callback({error: "BarClient is not initialized, call init(serverURL) first"})
    }
}

function doCloseArchive(id, async, callback) {
    if (barClients[id]) {
        rtcChannel[id] && rtcChannel[id].unsubscribe();
        if (async) {
            barClients[id].close(callback);
        } else {
            barClients[id].closeSync(callback);
        }
        delete barClients[id];
    } else {
        callback && callback({});
    }
}

function doCloseArchiveWithoutFlushing(id, callback) {
    if (barClients[id]) {
        rtcChannel[id] && rtcChannel[id].unsubscribe();
        barClients[id].closeArchiveWithoutFlushing(callback);
        delete barClients[id];
    } else {
        callback && callback({});
    }
}

function doRefreshSession(archiveID, platformToken, callback) {
    if (barClients[archiveID]) {
        barClients[archiveID].refreshSession(platformToken, callback);
    } else {
        callback({error: "BAR session is not opened " + archiveID})
    }
}

function doSetPlatformToken(archiveID, platformToken, refreshSession, callback) {
    if (barClients[archiveID]) {
        barClients[archiveID].setPlatformToken(platformToken, refreshSession, callback);
    } else {
        callback({error: "BAR session is not opened " + archiveID})
    }
}

// wrapper for createOrUpdateImageLink
function doCreateOrUpdateImageLink(archiveID,resourceID, branchID, permalinkInfo, base64data, mimeType, callback) {
    if (barClients[archiveID]) {
        barClients[archiveID].createOrUpdateImageLink(resourceID, branchID, permalinkInfo,base64data,mimeType, callback);
    } else {
        callback({error: "BAR session is not opened " + archiveID})
    }
}

function doSetPermalink(archiveID, resourceID, branchID, permalinkInfo, callback) {
    if (barClients[archiveID]) {
        barClients[archiveID].setPermalink(resourceID, branchID, permalinkInfo, callback);
    } else {
        callback({error: "BAR session is not opened " + archiveID})
    }
}

// check if the permalink exists
function doGetPermalink(archiveID, resourceID, branchID, callback) {
    if (barClients[archiveID]) {
        barClients[archiveID].getPermalink(resourceID, branchID, callback);
    } else {
        callback({error: "BAR session is not opened " + archiveID})
    }
}

function doGetPermalinkDataByPermalinkID(archiveID, permalinkID, callback) {
    if (barClients[archiveID]) {
        barClients[archiveID].getPermalinkDataByPermalinkID(permalinkID, callback);
    } else {
        callback({error: "BAR session is not opened " + archiveID})
    }
}

function doGetPermalinksData(archiveID, callback) {
    if (barClients[archiveID]) {
        barClients[archiveID].getPermalinksData(callback);
    } else {
        callback({error: "BAR session is not opened " + archiveID})
    }
}

function doDeletePermalinkDataByPermalinkID(archiveID, permalinkID, callback) {
    if (barClients[archiveID]) {
        barClients[archiveID].deletePermalinkDataByPermalinkID(permalinkID, callback);
    } else {
        callback({error: "BAR session is not opened " + archiveID})
    }
}

function doCloseArchivesWithoutFlushing(callback) {
    var ids, id, i, errors = [], resp = {};
    ids = Object.keys(barClients);
    var closeCallback;

    closeCallback = function(obj) {
        if (obj.error)
        {
            errors.push(obj);
        }
    };

    for (i=0; i<ids.length; i++) {
        id = ids[i];
        (function(id) {
            doCloseArchiveWithoutFlushing(id, closeCallback);
        })(id);
    }

    // TODO: we need a watchdog or we simply ignore the error!!!
    if (errors.length>0)
    {
        resp.error = "Something went wrong";
        resp.data = errors;
    }
    callback && callback(resp);
}

function doCloseArchives(async, callback) {
    var ids, id, i, errors = [], resp = {};
    ids = Object.keys(barClients);
    var closeCallback;

    closeCallback = function(obj) {
        if (obj.error)
        {
            errors.push(obj);
        }
    };

    for (i=0; i<ids.length; i++) {
        id = ids[i];
        (function(id) {
            doCloseArchive(id, async, closeCallback);
        })(id);
    }

    // TODO: we need a watchdog or we simply ignore the error!!!
    if (errors.length>0)
    {
        resp.error = "Something went wrong";
        resp.data = errors;
    }
    callback && callback(resp);
}

function doGetThumbnail(archiveID, thumbnailID, callback) {
    if (barClients[archiveID]) {
        barClients[archiveID].getThumbnail(thumbnailID, callback);
    } else {
        callback({error: "Not existent session for the selected archiveID: " + archiveID});
    }
}

function doGetResourceData(archiveID, branch, resourceID, callback) {
    if (barClients[archiveID]) {
        barClients[archiveID].getResourceData(resourceID, branch, callback);
    } else {
        callback({error: "Not existent session for the selected archiveID: " + archiveID});
    }
}

function doGetTOC(archiveID, branch, callback) {
    if (barClients[archiveID]) {
        barClients[archiveID].getTOC(branch, callback);
    } else {
        callback({error: "Not existent session for the selected archiveID: " + archiveID});
    }
}

function doGetUsersList(archiveID, option, callback) {
    if (barClients[archiveID]) {
        barClients[archiveID].getUsersList(option, callback);
    } else {
        callback({error: "Not existent session for the selected archiveID: " + archiveID});
    }
}

function doFlush(archiveID, platformArchiveName, callback) {
    if (barClients[archiveID]) {
        barClients[archiveID].flush(platformArchiveName, callback);
    } else {
        callback({error: "Not existent session for the selected archiveID: " + archiveID});
    }
}

function doFlushExt(archiveID, opts, callback) {
    if (barClients[archiveID]) {
        barClients[archiveID].flushExt(opts, callback);
    } else {
        callback({error: "Not existent session for the selected archiveID: " + archiveID});
    }
}

function doDeleteArchive(platformSiteID, archiveID, callback) {
    if (barClients[archiveID]) {
        barClients[archiveID].deleteArchiveSync(platformSiteID, archiveID, callback);
    } else {
        callback && callback({error: "Not existent session for the selected archiveID: " + archiveID});
    }
}


function doGetResourceAttributes(archiveID, branch, resourceID, callback) {
    if (barClients[archiveID]) {
        barClients[archiveID].getResourceAttributes(resourceID, branch, callback);
    } else {
        callback && callback({error: "Not existent session for the selected archiveID: " + archiveID});
    }
}

function doGetBranchAttributes(archiveID, branchID, callback) {
    if (barClients[archiveID]) {
        barClients[archiveID].getBranchAttributes(branchID, callback);
    } else {
        callback && callback({error: "Not existent session for the selected archiveID: " + archiveID});
    }
}

function doSetResourceAttributes(archiveID, branch, resourceID, attributes, callback) {
    if (barClients[archiveID]) {
        barClients[archiveID].setResourceAttributes(resourceID, branch, attributes, callback);
    } else {
        callback && callback({error: "Not existent session for the selected archiveID: " + archiveID});
    }
}

function doGetArchiveIDs() {
    return Object.keys(barClients);
}

function doSetArchiveAttributes(archiveID, attributes, callback)
{
    var barClient, message;
    barClient = barClients[archiveID];
    if (barClient) {
        barClient.setArchiveAttributes(attributes, function(obj) {
            callback && callback(obj);
        });
    } else {
        message = "Invalid archive ID";
        callback && callback({error: message});
    }
}

var doIsAtLeastOneAgentConnected = function (archiveID, options, callback) {
    var result = false, i;
    doGetUsersList(archiveID, "all", function(obj) {
        if (obj.error) {
            callback && callback(obj);
        } else {
            for (i=0; i<obj.users.length; i++) {
                if (obj.users[i].userInfo && obj.users[i].userInfo.agent == options.agentType) {
                    if (options.username) {
                        if (obj.users[i].userInfo.name == options.username) {
                            result = true;
                            break;
                        }
                    } else {
                        result = true;
                        break;
                    }

                }
            }
        }
        callback && callback({status: result});
    });
};

let doGetRTCAuthToken = function(archiveID, callback) {
    let barClient, message;
    barClient = barClients[archiveID];
    if (barClient) {
        barClient.getRTCAuthToken(callback);
    } else {
        message = "Invalid archive ID";
        callback && callback({error: message});
    }
}

var doBroadcastMessage = function(archiveID, obj, callback) {
    var barClient, message;
    barClient = barClients[archiveID];
    if (barClient) {
        barClient.broadcastMessage(obj, function(obj) {
            callback && callback(obj);
        });
    } else {
        message = "Invalid archive ID";
        callback && callback({error: message});
    }
};

var doUpdateUserInfo = function(archiveID, obj, callback) {
    var barClient, message;
    barClient = barClients[archiveID];
    if (barClient) {
        barClient.updateUserInfo(obj, function(obj) {
            callback && callback(obj);
        });
    } else {
        message = "Invalid archive ID";
        callback && callback({error: message});
    }
};

var doUnloadArchive = function(serverURL, platformToken, userInfo, kind, platformSiteID, platformArchiveID, callback) {
    // Disable unloading from BAS 1.2
    callback && callback({message: BalsamiqArchiveConstantsModule.ErrorCodes.ArchiveNotLoaded});
    // var barClient = new BalsamiqArchiveClientModule.BalsamiqArchiveClient(serverURL, kind, platformToken, userInfo);
    // barClient.unloadArchive(platformSiteID, platformArchiveID, BalsamiqArchiveConstantsModule.Role.ROLE_EDITOR, callback);
};

var doUnloadArchiveIfNotSync = function(serverURL, platformToken, userInfo, kind, platformSiteID, platformArchiveID, modifiedTimestamp, callback) {
    var barClient = new BalsamiqArchiveClientModule.BalsamiqArchiveClient(serverURL, kind, platformToken, userInfo);
    barClient.unloadArchiveIfNotSync(platformSiteID, platformArchiveID, BalsamiqArchiveConstantsModule.Role.ROLE_EDITOR, modifiedTimestamp, callback);
};

//function doSetRtcUpdate(state) {
//    RTC_update = state;
//}

//function doGetRtcReceived() {
//    return RTC_received;
//}

function doInit(p_serverURL, p_rtcClient) {
    // if p_rtcClient is defined, use RTC
    // if p_rtcClient is null, RTC is disabled (BWCS/JS)

    serverURL = p_serverURL;
    if (p_rtcClient) {
        rtcClient = p_rtcClient;
    }
}

var BarClient = (function () {
    return {
        init: doInit,
        unloadArchive: doUnloadArchive,
        unloadArchiveIfNotSync: doUnloadArchiveIfNotSync,
        openArchive: doOpenArchive,
        openArchiveByPublicShareID: doOpenArchiveByPublicShareID,
        refreshSession: doRefreshSession,
        setPlatformToken: doSetPlatformToken,
        createOrUpdateImageLink: doCreateOrUpdateImageLink,
        setPermalink: doSetPermalink,
        getPermalink: doGetPermalink,
        getPermalinksData: doGetPermalinksData,
        getPermalinkDataByPermalinkID: doGetPermalinkDataByPermalinkID,
        deletePermalinkDataByPermalinkID: doDeletePermalinkDataByPermalinkID,
        closeArchive: doCloseArchive,
        closeArchiveWithoutFlushing: doCloseArchiveWithoutFlushing,
        closeArchives: doCloseArchives,
        closeArchivesWithoutFlushing: doCloseArchivesWithoutFlushing,
        getResourceData: doGetResourceData,
        getResourceAttributes: doGetResourceAttributes,
        getBranchAttributes: doGetBranchAttributes,
        getThumbnail: doGetThumbnail,
        getUsersList: doGetUsersList,
        setResourceAttributes: doSetResourceAttributes,
        deleteArchive: doDeleteArchive,
        flush: doFlush,
        flushExt: doFlushExt,
        getArchiveIDs: doGetArchiveIDs,
        setArchiveAttributes: doSetArchiveAttributes,
        getMockupsOrderFromDump: BmprUtilsUtilsModule.getMockupsOrderFromDump,
        getMockupsOrderFromDumpExt: BmprUtilsUtilsModule.getMockupsOrderFromDumpExt,
        isAtLeastOneAgentConnected: doIsAtLeastOneAgentConnected,
        broadcastMessage: doBroadcastMessage,
        getRTCAuthToken: doGetRTCAuthToken,
        updateUserInfo: doUpdateUserInfo,
        getTOC: doGetTOC
        //setRtcUpdate: doSetRtcUpdate,
        //getRtcReceived: doGetRtcReceived,
    };
}());

exports.BarClient = BarClient;
exports.BalsamiqArchiveConstants = BalsamiqArchiveConstantsModule;
