export type Skin = "sketch" | "wireframe" | "programmatic";

export type ArchiveAttributes = {
    name: string;
    creationDate: number;
};

export type ArchiveCommentAttributes = {
    parentID: string | null;
    readBy: string[];
    resolvedBy?: string;
    resolved?: boolean;
    timestamp: number;
    trashed: boolean;
    trashedBy: string;
    likedBy: string[];
    timestamps: Record<string, number>;
};

export type ArchiveCommentData = {
    USERID: string;
    RESOURCEID: string;
    BRANCHID: string;
    ATTRIBUTES: ArchiveCommentAttributes;
    DATA: string;
    ID: string;
};

export type ArchiveBranchData = {
    branchID: string;
    ATTRIBUTES: {
        branchDescription?: string;
        branchName?: string;
        fontFace?: string;
        fontSize?: number;
        linkColor?: number;
        projectDescription?: string;
        selectionColor?: number;
        skinName?: Skin;
        symbolLibraryID?: string;
        creationDate?: number;
        modifiedBy?: string[];
    };
};

export type ArchiveResourceData = {
    ID: string;
    BRANCHID: string;
    ATTRIBUTES: ArchiveResourceAttributes;
    DATA?: string;
};

export type ArchiveThumbnailAttributes = {
    branchID: string;
    resourceID: string;
    image?: string;
};

export type ArchiveResourceAttributes = {
    name: string;
    kind: string;
    trashed?: boolean;
    importedFrom?: string;
    creationDate?: number | null;
    modifiedBy?: string | null;
    notes?: string | null;
    order?: number | null;
    parentID?: string | null;
    extension?: string | null;
    mimeType?: string | null;
    thumbnailID?: string | null;
};

export type ArchiveUserAttributes = {
    displayName: string;
    userName: string;
    email: string;
    anonymous: boolean;
    inProject?: boolean;
};

export type DumpFromArchive = {
    Info: {
        SchemaVersion: string;
        ArchiveFormat: "bmpr";
        ArchiveRevision: number;
        ArchiveRevisionUUID: string;
        ArchiveAttributes: ArchiveAttributes;
    };
    userInfo?: {
        name: string;
        avatarURL: string;
        ID: string;
    };
    Branch: ArchiveBranchData[];
    Resources: ArchiveResourceData[];
    Thumbnails: {
        ID: string;
        ATTRIBUTES: ArchiveThumbnailAttributes;
        DATA?: string;
    }[];
    Users: {
        ID: string;
        ATTRIBUTES: ArchiveUserAttributes;
    }[];
    Comments: ArchiveCommentData[];
};

export type ArchiveClientOpenResult = {
    userID: string;
    rtcChannel: string;
    timestamp: number;
    heuristicArchiveSize: number;
    resourcesPerRequest?: number;
    dump: DumpFromArchive;
    basArchiveID: string;
};
