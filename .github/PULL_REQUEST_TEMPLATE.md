## Description

---
### Pull Request Todo:
- Add a **Pull Request Title** with one of the following keywords
  ```
  feat:     A new feature (MINOR)
  fix:      A bug fix (PATCH)

  docs:     Documentation only changes
  style:    Changes that do not affect the meaning of the code (white-space, formatting, missing semi-colons, etc)
  refactor: A code change that neither fixes a bug nor adds a feature
  perf:     A code change that improves performance
  test:     Adding missing tests or correcting existing tests
  build:    Changes that affect the build system or external dependencies (example scopes: gulp, broccoli, npm)
  ci:       Changes to our CI configuration files and scripts (example scopes: Travis, Circle, BrowserStack, SauceLabs)
  chore:    Other changes that don't modify src or test files
  revert:   Reverts a previous commit
  ```
  Additional types are not mandated by the Conventional Commits specification, and have no implicit effect in semantic versioning (unless they include BREAKING CHANGE in the body). 

  example: `feat: added feature **x**`.

- Append a `!` after the type, to introduce a breaking API change (correlating with **MAJOR** in semantic versioning) 

  example: `refactor!: drop support for Node 6`.

- Add at least 2 team leads from <PERSON> (Stefano), B<PERSON> (Marco), BAS and Integrations (Sax) as reviewers.

### Squash Commit Todo:
- Write a descriptive **commit body**.

  **Why?** it will be used to generate the **release notes** and the **changelog** in the next release.
