name: PR Coverage Check

on:
  pull_request:
    types: [opened, synchronize]

jobs:
  call-workflow-passing-data:
    uses: ./.github/workflows/reusable_run_tests.yml
    secrets: inherit

  set-coverage-status:
    needs: call-workflow-passing-data
    runs-on: ubuntu-latest
    steps:
      - name: Set coverage status
        env:
          COVERAGE: ${{ needs.call-workflow-passing-data.outputs.coverage-total }}
        uses: actions/github-script@v7
        with:
          script: |
            const coverage = parseFloat(process.env.COVERAGE);
            const { owner, repo } = context.repo;
            const prNumber = context.payload.pull_request.number;
            const commitSHA = context.payload.pull_request.head.sha;

            let state = "success";
            let description = `Coverage is ${coverage}%`;

            const minimumCoverage = 0; // Disable minimum coverage check
            if (coverage < minimumCoverage) {
              state = "failure";
              description = `Coverage too low: ${coverage}% (Minimum required: ${minimumCoverage}%)`;
            }

            console.log(`Set status '${state}' on commit ${commitSHA} with description: '${description}'`);

            // Create commit status
            await github.rest.repos.createCommitStatus({
              owner,
              repo,
              sha: commitSHA,
              state: state,
              context: "Coverage Check",
              description: description,
            });