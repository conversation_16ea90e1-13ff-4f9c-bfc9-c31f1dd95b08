name: Reusable Run Tests

on:
  workflow_call:
    outputs:
      coverage-total:
        description: 'Total coverage'
        value: ${{ jobs.run-tests.outputs.coverage-total }}

jobs:

  run-tests:
    outputs:
      coverage-total: ${{ steps.set-coverage.outputs.coverage-total }}
    
    runs-on: ubuntu-latest
    services:
      redis:
        image: redis:latest
        ports:
          - 6379:6379
      mysql:
        image: mysql:8.0.36
        env:
          MYSQL_DATABASE: root
          MYSQL_ROOT_PASSWORD: root
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - name: Set up MySQL
        run: |
          mysql --host 127.0.0.1 --port 3306 -uroot -proot -e "ALTER USER 'root' IDENTIFIED WITH mysql_native_password BY 'root'; flush privileges;";

      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: ./src/.nvmrc
          cache: 'npm'
          cache-dependency-path: src/package-lock.json

      - name: Install dependencies
        env:
          PRIVATE_NPM_AUTH_TOKEN: ${{ secrets.PRIVATE_NPM_AUTH_TOKEN }}
          NODE_ENV: testing
        working-directory: ./src
        run: |
          npm ci
          [ "$UPDATE_BMPR_FOR_TESTS" = true ] && npm install --save @balsamiq/bmpr@latest || true

      - name: Run tests
        env:
          BAS_DB_HOST: '127.0.0.1'
          BAS_DB_PASSWORD: root
        working-directory: ./src
        run: |
          npm run coverage -- --forbid-only

      - name: Set coverage total in output
        id: set-coverage
        working-directory: ./src
        run: |
          npm install lcov-total
          echo "coverage-total=$(npx lcov-total ./coverage/lcov.info)" >> "$GITHUB_OUTPUT"

      - name: Upload test artifacts
        uses: actions/upload-artifact@v4
        with:
          name: test-coverage
          path: |
            ./src/coverage/
