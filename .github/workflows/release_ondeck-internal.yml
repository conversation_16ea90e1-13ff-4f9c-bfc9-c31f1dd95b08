name: Release ondeck

on:
    workflow_dispatch:
        inputs:
            approve_cdk:
                description: 'Approve CDK changes'
                required: true
                type: 'boolean'
                default: false

env:
    BAS_ENV: ondeck
    UPDATE_BMPR_FOR_TESTS: false
    AWS_ACCESS_KEY_ID: ${{ secrets.AWS_SRLINTERNAL_ACCESS_KEY_ID }}
    AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SRLINTERNAL_SECRET_ACCESS_KEY }}

concurrency:
    group: ondeck-internal_environment
    cancel-in-progress: true

jobs:
    test-ondeck:
        uses: ./.github/workflows/reusable_run_tests.yml
        secrets: inherit

    build-and-deploy-ondeck:
        needs: test-ondeck
        runs-on: ubuntu-latest
        steps:
            - name: Assert branch is not master
              if: ${{ startsWith(github.ref, 'refs/heads/master') }}
              run: |
                  echo '::error::You should not build ondeck from the master branch'
                  exit 1

            - name: Checkout
              uses: actions/checkout@v4

            - name: Set up Node.js
              uses: actions/setup-node@v4
              with:
                  node-version-file: ./infrastructure/.nvmrc
                  cache: 'npm'
                  cache-dependency-path: infrastructure/package-lock.json

            - name: Build and deploy
              env:
                  PRIVATE_NPM_AUTH_TOKEN: ${{ secrets.PRIVATE_NPM_AUTH_TOKEN }}
              working-directory: ./infrastructure
              run: |
                  APPROVE=""
                  if [[ "${{ inputs.approve_cdk }}" == "true" ]]; then
                    APPROVE="--approve"
                  fi
                  npm ci
                  bin/cli deploy $APPROVE $BAS_ENV new
                  bin/build fixS3permissions $BAS_ENV

            - name: Upload CDK assembly directory
              uses: actions/upload-artifact@v4
              with:
                  name: cdk-out
                  path: |
                      ./infrastructure/cdk.out/
