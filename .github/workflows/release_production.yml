name: Release production

on:
    workflow_dispatch:
        inputs:
            approve_cdk:
                description: 'Approve CDK changes'
                required: true
                type: 'boolean'
                default: false

env:
    BAS_ENV: production
    UPDATE_BMPR_FOR_TESTS: false
    AWS_ACCESS_KEY_ID: ${{ secrets.AWS_LLC_ACCESS_KEY_ID }}
    AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_LLC_SECRET_ACCESS_KEY }}

concurrency:
    group: production_environment
    cancel-in-progress: true

jobs:
    test-production:
        uses: ./.github/workflows/reusable_run_tests.yml
        secrets: inherit

    build-and-deploy-production:
        needs: test-production
        runs-on: ubuntu-latest
        steps:
            - name: Assert release branch
              if: ${{ !startsWith(github.ref, 'refs/heads/release') }}
              run: |
                  echo '::error::You must choose the release branch'
                  exit 1

            - name: Checkout
              uses: actions/checkout@v4

            - name: Set up Node.js
              uses: actions/setup-node@v4
              with:
                  node-version-file: ./infrastructure/.nvmrc
                  cache: 'npm'
                  cache-dependency-path: infrastructure/package-lock.json

            - name: Build and deploy
              env:
                  PRIVATE_NPM_AUTH_TOKEN: ${{ secrets.PRIVATE_NPM_AUTH_TOKEN }}
              working-directory: ./infrastructure
              run: |
                  APPROVE=""
                  if [[ "${{ inputs.approve_cdk }}" == "true" ]]; then
                    APPROVE="--approve"
                  fi
                  npm ci
                  bin/cli deploy $APPROVE $BAS_ENV new
                  bin/build fixS3permissions $BAS_ENV

            - name: Upload CDK assembly directory
              uses: actions/upload-artifact@v4
              with:
                  name: cdk-out
                  path: |
                      ./infrastructure/cdk.out/
