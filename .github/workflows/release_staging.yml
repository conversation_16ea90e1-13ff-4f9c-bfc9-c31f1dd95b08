name: Release staging

on:
    workflow_dispatch:
        inputs:
            approve_cdk:
                description: 'Approve CDK changes'
                required: true
                type: 'boolean'
                default: false

    push:
        branches: [master]

env:
    BAS_ENV: staging
    UPDATE_BMPR_FOR_TESTS: true
    AWS_ACCESS_KEY_ID: ${{ secrets.AWS_SRLINTERNAL_ACCESS_KEY_ID }}
    AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SRLINTERNAL_SECRET_ACCESS_KEY }}

concurrency:
    group: staging-internal_environment
    cancel-in-progress: true

jobs:
    test-staging:
        uses: ./.github/workflows/reusable_run_tests.yml
        secrets: inherit

    build-and-deploy-staging:
        needs: test-staging
        runs-on: ubuntu-latest
        steps:
            - name: Check if staging builds from master are disabled
              if: ${{ startsWith(github.ref, 'refs/heads/master') }}
              shell: bash
              env:
                  DISABLE_STAGING_BUILD_FROM_MASTER: ${{ vars.DISABLE_STAGING_BUILD_FROM_MASTER }}
              run: |
                  if [ "$DISABLE_STAGING_BUILD_FROM_MASTER" == "true" ] ; then echo '::error::Staging release from master temporarily disabled'; exit 1; else exit 0; fi

            - name: Checkout
              uses: actions/checkout@v4

            - name: Set up Node.js
              uses: actions/setup-node@v4
              with:
                  node-version-file: ./infrastructure/.nvmrc
                  cache: 'npm'
                  cache-dependency-path: infrastructure/package-lock.json

            - name: Install latest BMPR
              env:
                  PRIVATE_NPM_AUTH_TOKEN: ${{ secrets.PRIVATE_NPM_AUTH_TOKEN }}
              working-directory: ./src
              run: |
                  npm install --save @balsamiq/bmpr@latest

            - name: Build and deploy
              env:
                  PRIVATE_NPM_AUTH_TOKEN: ${{ secrets.PRIVATE_NPM_AUTH_TOKEN }}
              working-directory: ./infrastructure
              run: |
                  APPROVE=""
                  if [[ "${{ inputs.approve_cdk }}" == "true" ]]; then
                    APPROVE="--approve"
                  fi
                  npm ci
                  bin/cli deploy $APPROVE $BAS_ENV new
                  bin/build fixS3permissions $BAS_ENV

            - name: Upload CDK assembly directory
              uses: actions/upload-artifact@v4
              with:
                  name: cdk-out
                  path: |
                      ./infrastructure/cdk.out/
