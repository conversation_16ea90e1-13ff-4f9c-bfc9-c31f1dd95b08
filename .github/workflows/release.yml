name: Release
on:
  push:
    branches:
      - master
jobs:
  release:
    if: "!contains(github.event.head_commit.message, '[skip ci]')"
    name: Release
    runs-on: ubuntu-22.04
    env:
      PRIVATE_NPM_AUTH_TOKEN: ${{ secrets.PRIVATE_NPM_AUTH_TOKEN }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Read nvmrc
        id: nvmrc
        run: echo "node_version=$(cat .nvmrc)" >> $GITHUB_OUTPUT
        shell: bash
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: './.nvmrc'
      - name: Install dependencies
        run: npm install
      - name: Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.PRIVATE_NPM_AUTH_TOKEN }}
        run: npm run semantic-release
      - name: Get Release Version
        id: get_released_version
        run: echo "version=$(git tag --points-at HEAD | sed 's/v//')" >> $GITHUB_OUTPUT
      - name: Commit the new version
        if: "(steps.get_released_version.outputs.version != '')"
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add -u .
          git commit -m "chore: update package.json to version ${{ steps.get_released_version.outputs.version }} [skip ci]"
      - name: Push changes
        if: "(steps.get_released_version.outputs.version != '')"
        uses: ad-m/github-push-action@v0.6.0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          branch: master
      - name: Trigger B4 Workflow "Update Package JSON - BMPR Version"
        if: "(steps.get_released_version.outputs.version != '')"
        run: |
          curl -X POST ${{ secrets.WORKFLOW_TRIGGERER_API_URL }} \
          -H "Content-Type: application/json" \
          -H "x-api-key: ${{ secrets.WORKFLOW_TRIGGERER_API_KEY }}" \
          -d '{ "owner": "balsamiq", "repo": "b4", "workflow_id": "update_bmpr.yml", "ref": "master", "inputs": { "version": "${{ steps.get_released_version.outputs.version }}" } }'
      - name: Trigger Cloud Workflow "Update Package JSON - BMPR Version"
        if: "(steps.get_released_version.outputs.version != '')"
        run: |
          curl -X POST ${{ secrets.WORKFLOW_TRIGGERER_API_URL }} \
          -H "Content-Type: application/json" \
          -H "x-api-key: ${{ secrets.WORKFLOW_TRIGGERER_API_KEY }}" \
          -d '{ "owner": "balsamiq", "repo": "cloud", "workflow_id": "update_bmpr.yml", "ref": "master", "inputs": { "version": "${{ steps.get_released_version.outputs.version }}" } }'
      - name: Trigger Cloud Frontend Workflow "Update Package JSON - BMPR Version"
        if: "(steps.get_released_version.outputs.version != '')"
        run: |
          curl -X POST ${{ secrets.WORKFLOW_TRIGGERER_API_URL }} \
          -H "Content-Type: application/json" \
          -H "x-api-key: ${{ secrets.WORKFLOW_TRIGGERER_API_KEY }}" \
          -d '{ "owner": "balsamiq", "repo": "cloud-frontend", "workflow_id": "update_bmpr.yml", "ref": "main", "inputs": { "version": "${{ steps.get_released_version.outputs.version }}" } }'
      - name: Trigger BAS Workflow "Update Package JSON - BMPR Version"
        if: "(steps.get_released_version.outputs.version != '')"
        run: |
          curl -X POST ${{ secrets.WORKFLOW_TRIGGERER_API_URL }} \
          -H "Content-Type: application/json" \
          -H "x-api-key: ${{ secrets.WORKFLOW_TRIGGERER_API_KEY }}" \
          -d '{ "owner": "balsamiq", "repo": "bas", "workflow_id": "update_bmpr.yml", "ref": "master", "inputs": { "version": "${{ steps.get_released_version.outputs.version }}" } }'
      - name: Trigger wireframestogo workflow "Update BMPR package"
        if: "(steps.get_released_version.outputs.version != '')"
        run: |
          curl -X POST ${{ secrets.WORKFLOW_TRIGGERER_API_URL }} \
          -H "Content-Type: application/json" \
          -H "x-api-key: ${{ secrets.WORKFLOW_TRIGGERER_API_KEY }}" \
          -d '{ "owner": "balsamiq", "repo": "wireframestogo.com", "workflow_id": "update_bmpr.yml", "ref": "master", "inputs": { "version": "${{ steps.get_released_version.outputs.version }}" } }'
