name: Update Package JSON - BMPR Version

on:
  workflow_dispatch:
    inputs:
      version:
        description: "Version of the @balsamiq/bmpr package"
        required: true

jobs:
  update_package_json:
    runs-on: ubuntu-22.04
    env:
      UPDATE_BRANCH: update-bmpr-version-${{ github.event.inputs.version }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: ./src/.nvmrc

      - name: Cache node modules
        uses: actions/cache@v4
        env:
          cache-name: cache-node-modules
        with:
          # npm cache files are stored in `~/.npm` on Linux/macOS
          path: ~/.npm
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}

      - name: Checkout ${{ env.UPDATE_BRANCH }} branch
        run: git checkout -b ${{ env.UPDATE_BRANCH }}

      - name: Install bmpr
        run: npm install --save @balsamiq/bmpr@${{ github.event.inputs.version }}
        working-directory: src
        env:
          PRIVATE_NPM_AUTH_TOKEN: ${{ secrets.PRIVATE_NPM_AUTH_TOKEN }}
          INPUT_GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Add changes to Git tracking
        run: git add -A .

      - name: Commit changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git commit -m "Update package.json"

      - name: Push changes
        uses: ad-m/github-push-action@v0.8.0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          branch: ${{ env.UPDATE_BRANCH }}

      - name: Initialize Pull Request
        uses: repo-sync/pull-request@v2
        with:
          source_branch: ${{ env.UPDATE_BRANCH }}
          destination_branch: "master"
          pr_title: "chore: update package.json: bmpr@${{ github.event.inputs.version }}"
          pr_body: >
            Update **bmpr** to version ${{ github.event.inputs.version }}

            Release notes: https://github.com/balsamiq/bmpr/releases/tag/v${{ github.event.inputs.version }}
          pr_reviewer: "balsamiqSax"
          github_token: ${{ secrets.GITHUB_TOKEN }}
