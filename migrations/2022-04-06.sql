-- Add `image_unfurling` <PERSON>NU<PERSON> to PERMALINKS table
ALTER TABLE
    `PERMALINKS`.`PER<PERSON><PERSON><PERSON><PERSON>_INFO`
    MODIFY COLUMN
        `PERMALINK_KIND` enum(
            'image',
            'public_share',
            'image_unfurling',
            'url_unfurling'
        )
        NOT NULL DEFAULT 'image';


ALTER TABLE `PERMALINKS`.`PERMALINK_INFO` ADD UNIQUE INDEX (
                                        `PLATFORM_SITE_ID`,
                                        `PLATFORM_ARCHIVE_ID`,
                                        `RESOURCE_ID`,
                                        `BRANCH_ID`,
                                        `<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_KIND`);

ALTER TABLE `PERMALINKS`.`<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_INFO` DROP INDEX `PLATFORM_SITE_ID`;
