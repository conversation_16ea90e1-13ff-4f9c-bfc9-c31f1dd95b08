# ` --platform=linux/amd64` is required to build for amd64 on M1 Macs, otherwise
# builds on AWS will fail with `exec /usr/local/bin/docker-entrypoint.sh: exec format error`
FROM --platform=linux/amd64 node:22.15.1-alpine

ARG PRIVATE_NPM_AUTH_TOKEN

# OS dependencies
RUN apk update && apk upgrade
RUN apk add --no-cache git python3 make g++ bash curl

# Setting NODE_ENV this way will prevent node dev-dependencies from being installed
# and certain libraries to behave more "production" like
ENV NODE_ENV=production

# Install BAS node dependencies
COPY src/.npmrc /tmp/dependencies/
COPY src/package.json /tmp/dependencies/
COPY src/package-lock.json /tmp/dependencies/
RUN cd /tmp/dependencies &&\
    export PYTHON=/usr/bin/python3 &&\
    npm ci &&\
    mkdir -p /app/bas/src &&\
    cp -a /tmp/dependencies/node_modules /app/bas/src/ &&\
    rm -rf /tmp/dependencies

# Copy code. We do this after the dependencies to improve the Docker caching
# performance gain, else any time a source file changes all subsequent layers
# would be re-created
ADD src /app/bas/src

WORKDIR /app/bas/src
