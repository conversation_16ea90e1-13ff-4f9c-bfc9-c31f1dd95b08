var SQLiteLib = require('sql.js');

var MockDbDriver = {
    sqliteDBs: {},
    open: function(id, mode, callback) {
        if (!MockDbDriver.sqliteDBs[id]) {
            MockDbDriver.sqliteDBs[id] = new SQLiteLib.Database();
        }

        // verify that id is in host.SQLite
        if (callback) {
            callback({});
        }
    },
    close: function(id, callback) {
        MockDbDriver.sqliteDBs[id].close();
        delete MockDbDriver.sqliteDBs[id];
        if (callback) {
            callback({});
        }
    },
    run: function(id, query, params, callback) {

        var res = {};
        var rows = MockDbDriver._query(id, query, params);
        res.rows = rows;

        var changes = MockDbDriver._query(id, "SELECT CHANGES();", []);
        res.affectedRows = changes[0]["CHANGES()"];
        if (callback) {
            callback(res);
        }
        return res;
    },
    _query: function(id, query, parameters) {
        var db = MockDbDriver.sqliteDBs[id];
        var s = db.prepare(query);
        s.bind(parameters);
        var rows = [];
        while (s.step()) {
            rows.push(s.getAsObject());
        }
        s.free();

        return rows;
    },
    all: function(id, query, params, callback) {
        var res = {};
        var rows = MockDbDriver._query(id, query, params);
        res.rows = rows;

        if (callback) {
            callback(res);
        }

        return res;
    },
    get: function(id, query, params, callback) {
        var rows = MockDbDriver._query(id, query, params);
        if (callback) {
            callback({
                row: rows[0]
            });
        }

        return rows[0]
    },
    download: function(id, callback) {
        var db = MockDbDriver.sqliteDBs[id];
        var uint8Array = db.export();
        callback(uint8Array);
        return uint8Array;
    }
};

module.exports = MockDbDriver;
