import assert from 'assert-plus';
import { buildTestBmpr1, buildTestBmpr2, buildTestBmprNestedComments, buildTestBmprNestedCommentsTrashed, closeDB } from './bmpr_data'
import {composeImagePermalinkUrl, isPermalinkUrlValid} from "../../src/js/BmprUtils";
var BalsamiqArchive = require('../../src/js/BalsamiqArchive');
var MockDbDriver = require('./mockdbdriver');
var BARConstants = require('../../src/js/BalsamiqArchiveConstants');
var UUID = require('uuid-js');
var Consts = require("../../src/js/BalsamiqArchiveConstants");
var BmprUtils = require('../../src/js/BmprUtils');

// TODO: merge with CoreObject.createNewUUID
var createNewUUID = function() {
    return UUID.create().toString().toUpperCase();
};

suite("Bmpr tests", function() {
    let db1;
    let db2;
    let db3;
    let db4;

    setup(function (done) {
        db1 = buildTestBmpr1();
        db2 = buildTestBmpr2();
        db3 = buildTestBmprNestedComments();
        db4 = buildTestBmprNestedCommentsTrashed();
        done();
    });

    teardown(function (done) {        
        if (db1) {
            closeDB(db1);
            db1 = null;
        }
        if (db2) {
            closeDB(db2);
            db2 = null
        }
        if (db3) {
            closeDB(db3);
            db3 = null
        }
        if (db4) {
            closeDB(db4);
            db4 = null
        }
        done();
    });

    test("Open 2.0 Schema", function (done) {

        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver);
        archive.open(db1, () => {
            archive.dump(BARConstants.Branch.AllBranches, (res) => {
                assert.equal(res.dump.Info.ArchiveRevision, 3121);
                assert.equal(res.dump.Info.SchemaVersion, '2.0');
                assert.equal(res.dump.Info.ArchiveFormat, "bmpr");
                assert.object(res.dump.Info.ArchiveAttributes);
                assert.equal(res.dump.Comments.length, 4);
                assert.equal(res.dump.Users.length, 3);
                done();
            });
        });
    });

    test("Open 1.2 Schema", function (done) {

        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver);
        archive.open(db2, () => {

            archive.dump(BARConstants.Branch.AllBranches, (res) => {

                assert.equal(res.dump.Info.SchemaVersion, '2.0');
                done();
            });
        });
    });

    test("Get TOC at open", function (done) {

        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver);
        archive.open(db1, (res) => {

            assert.equal(res.dump.Info.ArchiveRevision, 3121);
            assert.equal(res.dump.Info.SchemaVersion, '2.0');
            assert.equal(res.dump.Info.ArchiveFormat, "bmpr");
            assert.object(res.dump.Info.ArchiveAttributes);
            assert.equal(res.dump.Comments.length, 4);
            assert.equal(res.dump.Users.length, 3);
            done();
        });
    });

    test("Get TOC at open 2", function (done) {

        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver);
        archive.open(db2, (res) => {
            assert.equal(res.dump.Info.ArchiveRevision, 3121);
            assert.equal(res.dump.Info.SchemaVersion, '2.0');
            assert.equal(res.dump.Info.ArchiveFormat, "bmpr");
            assert.object(res.dump.Info.ArchiveAttributes);
            assert.equal(res.dump.Comments.length, 0);
            assert.equal(res.dump.Users.length, 0);
            done();
        });
    });

    test("Get Comments Data", function (done) {

        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver);
        archive.open(db1, (res) => {
            archive.getCommentsData([res.dump.Comments[0].ID, res.dump.Comments[1].ID, res.dump.Comments[2].ID], (res) => {
                assert.equal(res.comments.length, 3);
                assert.string(res.comments[0].DATA);
                assert.string(res.comments[1].DATA);
                assert.string(res.comments[2].DATA);
                done();
            });
        });
    });

    test("Create comment", function (done) {

        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver);
        archive.open(db1, (res) => {
            let myResource = res.dump.Resources[0];
            let myText = "This is a test text";
            let commentID = createNewUUID();
            archive.createComment(commentID, myResource.ID, myResource.BRANCHID, null, JSON.stringify({text: myText}), (res) => {
                archive.getCommentsData([commentID], (res) => {
                    assert.equal(JSON.parse(res.comments[0].DATA).text, myText);
                    done();
                });
            });
        });
    });

    test("can import comment", function (done) {
        const userInfo = {name: "pluto", avatarURL: ""};
        const archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);
        archive.open(db1, (res) => {
            const myResource = res.dump.Resources[0];
            const dateTime = Date.now();
            const comment = {
                id: createNewUUID(),
                data: {
                    text: "This is a test text",
                    callouts: [],
                },
                attributes: {
                    [Consts.CommentAttributes.ParentID]: "",
                    [Consts.CommentAttributes.ReadBy]: ["pippo"],
                    [Consts.CommentAttributes.Timestamp]: dateTime,
                    [Consts.CommentAttributes.Trashed]: false,
                    [Consts.CommentAttributes.TrashedBy]: "",
                    [Consts.CommentAttributes.LikedBy]: [],
                    [Consts.CommentAttributes.Timestamps]: {},
                },
                originalAuthorDisplayName: "obama",
            };
            archive.importComment(myResource.ID, myResource.BRANCHID, comment, (res) => {
                assert.equal(res.commentID, comment.id);

                archive.getCommentsData([comment.id], (res) => {
                    assert.equal(JSON.parse(res.comments[0].DATA).text, "This is a test text");
                    const commentAttributes = res.comments[0].ATTRIBUTES;
                    assert.deepEqual(commentAttributes.readBy, [userInfo.name], "only current user must be in the readBy array");
                    assert.equal(commentAttributes.imported, true, "comment must be flagged as imported");
                    assert.equal(commentAttributes.originalAuthor, "obama", "the passed in originalAuthorDisplayName must be used to set the original author");
                    done();
                });
            });
        });
    });

    test("Not adding user on opening", function (done) {

        let userInfo = {name: "myNewUser", avatarURL: ""};
        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);

        archive.open(db1, (toc) => {

            let userID = archive.getArchiveUsersList((res) => {
                let userIndex = res.users.findIndex((user) => {
                    return user.ATTRIBUTES.userName === userInfo.name
                });
                assert.equal(userIndex, -1);
                done();
            })
        });
    });

    test("Explicitly adding a user", function (done) {

        let userInfo = {name: "myNewUser", avatarURL: "", displayName: "John Doe"};

        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);

        archive.open(db1, (toc) => {

            let userID = archive.getArchiveUsersList((res) => {
                let userIndex = res.users.findIndex((user) => {
                    return user.ATTRIBUTES.userName === userInfo.name
                });

                assert.equal(userIndex, -1);

                var attributes = {
                    userName: userInfo.name,
                    displayName: userInfo.displayName,
                };

                archive.createMyUser(attributes, (res) => {

                    let userID = archive.getArchiveUsersList((res) => {
                        let userIndex = res.users.findIndex((user) => {
                            return user.ATTRIBUTES.userName === userInfo.name
                        });

                        assert.notEqual(userIndex, -1);
                        done();
                    });
                });
            });
        });
    });


    test("Adding and updating a user", function (done) {

        let userInfo = {name: "myNewUser", avatarURL: "", displayName: "John Doe"};

        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);

        archive.open(db1, (toc) => {

            var attributes = {
                userName: userInfo.name,
                displayName: userInfo.displayName,
            };

            archive.createMyUser(attributes, (res) => {

                archive.updateMyUser({userName: userInfo.name, displayName: "Donald Duck"}, () => {
                    let userID = archive.getArchiveUsersList((res) => {
                        let userIndex = res.users.findIndex((user) => {
                            return user.ATTRIBUTES.userName === userInfo.name
                        });

                        assert.notEqual(userIndex, -1);
                        assert.equal(res.users[userIndex].ATTRIBUTES.displayName, "Donald Duck");
                        done();
                    });
                });
            });
        });
    });

    test("Set comment read", function (done) {

        let userInfo = {name: "<EMAIL>", avatarURL: ""};
        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);

        archive.open(db1, (toc) => {
            var attributes = {};
            attributes[BARConstants.CommentAttributesSetters.setReadStatus] = true;
            archive.updateCommentsAttributes([toc.dump.Comments[0].ID], [attributes], (obj) => {
                // note: using internal API, only for unit testing!!
                assert.notEqual(obj.attributes[0].attributes.readBy.findIndex( (user) => user === userInfo.name), -1);
                archive._internalGetCommentsToc((res) => {
                    let readBy = res.comments[0].ATTRIBUTES.readBy;
                    let userIndex = readBy.findIndex((user) => {
                        return user === "<EMAIL>"
                    });
                    assert.notEqual(userIndex, -1);
                    done();
                });
            });
        });
    });

    test("Set comment read fails", function (done) {

        let userInfo = {name: "<EMAIL>", avatarURL: ""};
        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);

        archive.open(db1, (toc) => {
            var attributes = {};
            attributes[BARConstants.CommentAttributesSetters.setReadStatus] = true;
            archive.updateCommentsAttributes([toc.dump.Comments[0].ID], [attributes], (obj) => {
                assert(obj.attributes.length === 0);
                done();
            });
        });
    });

    test("Set comment unread", function (done) {

        let userInfo = {name: "<EMAIL>", avatarURL: ""};

        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);

        archive.open(db1, (toc) => {

            // comment read status exists
            let userIndex = toc.dump.Comments[0].ATTRIBUTES.readBy.findIndex((user) => {
                return user === userInfo.name
            });
            assert.notEqual(userIndex, -1);

            var attributes = {};
            attributes[BARConstants.CommentAttributesSetters.setReadStatus] = false;
            archive.updateCommentsAttributes([toc.dump.Comments[0].ID], [attributes], (obj) => {
                assert.equal(obj.attributes[0].attributes.readBy.findIndex( (user) => user === userInfo.name), -1);
                assert.ok(!obj.attributes[0].attributesChangedData);
                // note: using internal API, only for unit testing!!
                archive._internalGetCommentsToc((res) => {

                    // comment read status does not exists anymore
                    let readBy = res.comments[0].ATTRIBUTES.readBy;
                    userIndex = readBy.findIndex((user) => {
                        return user === userInfo.name
                    });
                    assert.equal(userIndex, -1);
                    done();
                });
            });
        });
    });

    test("Set comment unread fails", function (done) {

        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, { name: "<EMAIL>"});

        archive.open(db1, (toc) => {
            var attributes = {};
            attributes[BARConstants.CommentAttributesSetters.setReadStatus] = false;
            archive.updateCommentsAttributes([toc.dump.Comments[0].ID], [attributes], (obj) => {
                assert(obj.attributes.length === 0);
                done();
            });
        });
    });

    test("Set comment liked", function (done) {

        let userInfo = {name: "<EMAIL>", avatarURL: ""};
        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);

        archive.open(db1, (toc) => {
            var attributes = {};
            attributes[BARConstants.CommentAttributesSetters.setLikedStatus] = true;
            archive.updateCommentsAttributes([toc.dump.Comments[0].ID], [attributes], (obj) => {
                assert.notEqual(obj.attributes[0].attributes.likedBy.findIndex( (user) => user === userInfo.name), -1);
                // note: using internal API, only for unit testing!!
                archive._internalGetCommentsToc((res) => {
                    let likedBy = res.comments[0].ATTRIBUTES.likedBy;
                    let userIndex = likedBy.findIndex((user) => {
                        return user === "<EMAIL>"
                    });
                    assert.notEqual(userIndex, -1);
                    done();
                });
            });
        });
    });

    test("Set comment liked fails", function (done) {

        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, { name: "<EMAIL>"});

        archive.open(db1, (toc) => {
            var attributes = {};
            attributes[BARConstants.CommentAttributesSetters.setLikedStatus] = true;
            archive.updateCommentsAttributes([toc.dump.Comments[0].ID], [attributes], (obj) => {
                assert(obj.attributes.length === 0);
                done();
            });
        });
    });

    test("Set comment unliked", function (done) {

        let userInfo = {name: "<EMAIL>", avatarURL: ""};
        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);

        archive.open(db1, (toc) => {

            // comment like status exists
            let userIndex = toc.dump.Comments[0].ATTRIBUTES.likedBy.findIndex((user) => {
                return user === userInfo.name
            });
            assert.notEqual(userIndex, -1);

            var attributes = {};
            attributes[BARConstants.CommentAttributesSetters.setLikedStatus] = false;
            archive.updateCommentsAttributes([toc.dump.Comments[0].ID], [attributes], (obj) => {

                assert.equal(obj.attributes[0].attributes.likedBy.findIndex( (user) => user === userInfo.name), -1);
                // note: using internal API, only for unit testing!!
                archive._internalGetCommentsToc((res) => {

                    // comment like status does not exists anymore
                    let likedBy = res.comments[0].ATTRIBUTES.likedBy;
                    userIndex = likedBy.findIndex((user) => {
                        return user === userInfo.name
                    });
                    assert.equal(userIndex, -1);
                    done();
                });
            });
        });
    });

    test("Set comment unliked fails", function (done) {

        let userInfo = {name: "<EMAIL>", avatarURL: ""};
        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);

        archive.open(db1, (toc) => {

            var attributes = {};
            attributes[BARConstants.CommentAttributesSetters.setLikedStatus] = false;
            archive.updateCommentsAttributes([toc.dump.Comments[0].ID], [attributes], (obj) => {
                assert(obj.attributes.length === 0);
                done();
            });
        });
    });

    test("Set comment trashed", function (done) {

        let userInfo = {name: "<EMAIL>", avatarURL: ""};
        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);

        archive.open(db1, (toc) => {
            var attributes = {};
            attributes[BARConstants.CommentAttributesSetters.setTrashedStatus] = true;
            archive.updateCommentsAttributes([toc.dump.Comments[0].ID], [attributes], (obj) => {

                assert.equal(obj.attributes[0].attributes.trashedBy, userInfo.name);
                assert.equal(obj.attributes[0].attributes.trashed, true);

                // note: using internal API, only for unit testing!!
                archive._internalGetCommentsToc((res) => {
                    let trashedBy = res.comments[0].ATTRIBUTES.trashedBy;
                    assert.equal(trashedBy, "<EMAIL>");
                    assert.equal(res.comments[0].ATTRIBUTES.trashed, true);
                    done();
                });
            });
        });
    });

    test("Set comment trashed fails", function (done) {

        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, { name: "<EMAIL>"});

        archive.open(db1, (toc) => {
            var attributes = {};
            attributes[BARConstants.CommentAttributesSetters.setTrashedStatus] = true;
            archive.updateCommentsAttributes([toc.dump.Comments[2].ID], [attributes], (obj) => {
                assert(obj.attributes.length === 0);
                done();
            });
        });
    });

    test("Set comment trashed - no permission", function (done) {

        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, { name: "<EMAIL>"});
        archive.setUserRole(BARConstants.Role.ROLE_VIEWER);

        archive.open(db1, (toc) => {
            var attributes = {};
            attributes[BARConstants.CommentAttributesSetters.setTrashedStatus] = true;
            archive.updateCommentsAttributes([toc.dump.Comments[2].ID], [attributes], (obj) => {
                assert.ok(obj.error, "VIEWER can not set comment attributes");
                done();
            });
        });
    });

    test("Set comment untrashed", function (done) {

        let userInfo = {name: "<EMAIL>", avatarURL: ""};
        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);

        archive.open(db1, (toc) => {

            var attributes = {};
            attributes[BARConstants.CommentAttributesSetters.setTrashedStatus] = false;
            archive.updateCommentsAttributes([toc.dump.Comments[2].ID], [attributes], (obj) => {

                assert.equal(obj.attributes[0].attributes.trashedBy, "");
                assert.equal(obj.attributes[0].attributes.trashed, false);
                assert.equal(obj.attributes[0].attributesChangedData, '{"text": "Naaa, look at the alternate!"}');

                // note: using internal API, only for unit testing!!
                archive._internalGetCommentsToc((res) => {
                    assert.equal(res.comments[2].ATTRIBUTES.trashed, false);
                    done();
                });
            });
        });
    });

    test("Set comment untrashed fails", function (done) {

        let userInfo = {name: "<EMAIL>", avatarURL: ""};

        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);

        archive.open(db1, (toc) => {

            var attributes = {};
            attributes[BARConstants.CommentAttributesSetters.setTrashedStatus] = false;
            archive.updateCommentsAttributes([toc.dump.Comments[1].ID], [attributes], (obj) => {
                assert(obj.attributes.length === 0);
                done();
            });
        });
    });

    test("Set multiple comment attributes", function (done) {

        let userInfo = {name: "<EMAIL>", avatarURL: ""};
        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);

        archive.open(db1, (toc) => {

            var attributes = {};
            attributes[BARConstants.CommentAttributesSetters.setTrashedStatus] = true;
            attributes[BARConstants.CommentAttributesSetters.setLikedStatus] = true;
            archive.updateCommentsAttributes([toc.dump.Comments[1].ID], [attributes], (obj) => {

                assert.equal(obj.attributes[0].attributes.trashedBy, userInfo.name);
                assert.equal(obj.attributes[0].attributes.trashed, true);
                assert.notEqual(obj.attributes[0].attributes.likedBy.findIndex( (user) => user === userInfo.name), -1);

                // note: using internal API, only for unit testing!!
                archive._internalGetCommentsToc((res) => {
                    assert.equal(res.comments[1].ATTRIBUTES.trashed, true);
                    assert.equal(res.comments[1].ATTRIBUTES.trashedBy, userInfo.name);
                    var userIndexInLikedBy = res.comments[1].ATTRIBUTES.likedBy.findIndex( (name) => { return name === userInfo.name});
                    assert.notEqual(userIndexInLikedBy, -1);
                    done();
                });
            });
        });
    });

    test("Set multiple attributes on multiple comments", function (done) {

        let userInfo = {name: "<EMAIL>", avatarURL: ""};
        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);

        archive.open(db1, (toc) => {

            var attributesArray = [];

            var attr1 = {};
            attr1[BARConstants.CommentAttributesSetters.setTrashedStatus] = true;
            attr1[BARConstants.CommentAttributesSetters.setLikedStatus] = true;

            var attr2 = {};
            attr2[BARConstants.CommentAttributesSetters.setTrashedStatus] = false;
            attr2[BARConstants.CommentAttributesSetters.setLikedStatus] = true;

            var attr3 = {};
            attr3[BARConstants.CommentAttributesSetters.setTrashedStatus] = true;
            attr3[BARConstants.CommentAttributesSetters.setLikedStatus] = true;

            attributesArray.push(attr1);
            attributesArray.push(attr2);
            attributesArray.push(attr3);

            var commentsArray = [toc.dump.Comments[1].ID, toc.dump.Comments[2].ID, toc.dump.Comments[3].ID];

            archive.updateCommentsAttributes(commentsArray,
                attributesArray, (obj) => {
                // note: using internal API, only for unit testing!!
                archive._internalGetCommentsToc((res) => {
                    assert.equal(res.comments[1].ATTRIBUTES.trashed, true);
                    assert.equal(res.comments[1].ATTRIBUTES.trashedBy, userInfo.name);
                    var userIndexInLikedBy = res.comments[1].ATTRIBUTES.likedBy.findIndex( (name) => { return name === userInfo.name});
                    assert.notEqual(userIndexInLikedBy, -1);

                    assert.equal(res.comments[2].ATTRIBUTES.trashed, false);
                    assert.equal(res.comments[2].ATTRIBUTES.trashedBy, "");
                    userIndexInLikedBy = res.comments[2].ATTRIBUTES.likedBy.findIndex( (name) => { return name === userInfo.name});
                    assert.notEqual(userIndexInLikedBy, -1);

                    assert.equal(res.comments[3].ATTRIBUTES.trashed, true);
                    assert.equal(res.comments[3].ATTRIBUTES.trashedBy, userInfo.name);
                    userIndexInLikedBy = res.comments[3].ATTRIBUTES.likedBy.findIndex( (name) => { return name === userInfo.name});
                    assert.notEqual(userIndexInLikedBy, -1);
                    done();
                });
            });
        });
    });

    test("Set multiple attributes on multiple comments fails", function (done) {

        let userInfo = {name: "<EMAIL>", avatarURL: ""};

        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);

        archive.open(db1, (toc) => {

            var attributesArray = [];

            var attr1 = {
                [BARConstants.CommentAttributesSetters.setTrashedStatus]: false,
                [BARConstants.CommentAttributesSetters.setLikedStatus]: false
            };
            var attr2 = {
                [BARConstants.CommentAttributesSetters.setTrashedStatus]: true
            };
            var attr3 = {
                [BARConstants.CommentAttributesSetters.setTrashedStatus]: false,
                [BARConstants.CommentAttributesSetters.setTrashedStatus]: false
            };

            attributesArray.push(attr1);
            attributesArray.push(attr2);
            attributesArray.push(attr3);

            var commentsArray = [toc.dump.Comments[1].ID, toc.dump.Comments[2].ID, toc.dump.Comments[3].ID];

            archive.updateCommentsAttributes(commentsArray,
                attributesArray, (obj) => {
                    assert.equal(obj.attributes.length, 0);
                    done();
            });
        });
    });

    test("Set comment data", function (done) {
        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, { name: "<EMAIL>"});

        archive.open(db1, (toc) => {
            // id 2: done and ready by logged user
            archive.getCommentsData([toc.dump.Comments[2].ID], (res) => {
                var newData =  {
                    text: "The alternate is much better"
                };

                archive.setCommentData(toc.dump.Comments[2].ID, JSON.stringify(newData), (res) => {
                    assert.equal(res.attributes.readBy.length, 1);
                    assert.equal(res.attributes.readBy[0], "<EMAIL>");

                    done();
                })
            });
        });
    });

    test("Set comment data with no permission fails silently and returns the same archive revision", function (done) {
        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, { name: "<EMAIL>"});
        archive.setUserRole(BARConstants.Role.ROLE_VIEWER);

        archive.open(db1, (toc) => {
            // id 2: done and ready by logged user
            archive._getArchiveRevision(function (archiveRevision) {
                archive.getCommentsData([toc.dump.Comments[2].ID], (res) => {
                    var newData =  {
                        text: "The alternate is much better"
                    };

                    archive.setCommentData(toc.dump.Comments[2].ID, JSON.stringify(newData), (res) => {
                        assert.deepEqual(res, { archiveRevision: archiveRevision });

                        done();
                    })
                });
            })
        });
    });

    test("Returns the default comment data for trash comments", (done) => {
        const archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, { name: "<EMAIL>" });

        archive.open(db1, (toc) => {
            archive.getCommentsData([toc.dump.Comments[2].ID], (res) => {
                const comment = res.comments[0];
                assert.equal(comment.ATTRIBUTES[Consts.CommentAttributes.Trashed], true);
                assert.equal(comment.DATA, "{}");
                done();
            });
        });
    });

    test("Comments prevent a resource to be deleted", function (done) {
        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, { name: "<EMAIL>"});

        archive.open(db1, (toc) => {

            archive.deleteResources(['55ECC516-0F00-19DD-7570-B1409DBCC586' ], 'Master', (obj) => {
                assert.string(obj.error);
                done();
            });
        });
    });

    test("Delete Comments to delete a resource", function (done) {
        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, { name: "<EMAIL>"});

        archive.open(db1, (toc) => {
            archive.deleteComments(["0e29271084a8431884caf9e1f28ffef2", "d6e79000a631472aaa1b030d0e2ad05b"], (obj) => {
                archive.deleteResources(['55ECC516-0F00-19DD-7570-B1409DBCC586' ], 'Master', (obj) => {
                    assert(obj.error === undefined);
                    done();
                });
            })
        });
    });

    test("Delete Comments - no permission", function (done) {
        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, { name: "<EMAIL>"});
        archive.setUserRole(BARConstants.Role.ROLE_VIEWER);

        archive.open(db1, (toc) => {
            archive.deleteComments(["0e29271084a8431884caf9e1f28ffef2", "d6e79000a631472aaa1b030d0e2ad05b"], (obj) => {
                assert.ok(obj.error, "VIEWER can not delete comments");
                done();
            })
        });
    });

    test("Setting timestamps", function (done) {

        let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, { name: "<EMAIL>"});

        archive.open(db1, (toc) => {
            var attributes = {};
            attributes[BARConstants.CommentAttributesSetters.setTimestamps] = ["time", "callout"];
            archive.updateCommentsAttributes([toc.dump.Comments[0].ID], [attributes], (obj) => {
                assert(obj.attributes[0].attributes.timestamps.time);
                assert(obj.attributes[0].attributes.timestamps.callout);
                done();
            });
        });
    });

    suite("Set Resource Branch", function () {
        test("Moving resource branch id, with comments", function (done) {
            var archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, { name: "<EMAIL>" });

            var resourceID = "55ECC516-0F00-19DD-7570-B1409DBCC586";
            var branchIDFrom = "71A404CF-096B-A3D7-A7B2-B14108F708E0";
            var branchIDTo = "FBE786C4-D0E8-3A34-8E14-D60902952B3F";

            archive.open(db1, (toc) => {
                archive.setResourceBranchID(resourceID, branchIDFrom, branchIDTo, (obj) => {
                    archive.dump(BARConstants.Branch.AllBranches, (res) => {
                        assert.equal(res.dump.Resources.filter(r => r.ID === resourceID && r.BRANCHID === branchIDTo).length, 1);
                        assert.equal(res.dump.Resources.filter(r => r.ID === resourceID && r.BRANCHID === branchIDFrom).length, 0);
                        assert.equal(res.dump.Comments.filter(c => c.RESOURCEID === resourceID && c.BRANCHID === branchIDTo).length, 1);
                        assert.equal(res.dump.Comments.filter(c => c.RESOURCEID === resourceID && c.BRANCHID === branchIDFrom).length, 0);
                        done();
                    })
                });
            });
        });

        test("Moving resource branch id, without comments", function (done) {
            var archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, { name: "<EMAIL>" });

            var resourceID = "E474EDE7-903E-D4A4-9DE0-B141A4027A62";
            var branchIDFrom = "71A404CF-096B-A3D7-A7B2-B14108F708E0";
            var branchIDTo = "FBE786C4-D0E8-3A34-8E14-D60902952B3F";

            archive.open(db1, (toc) => {
                archive.setResourceBranchID(resourceID, branchIDFrom, branchIDTo, (obj) => {
                    archive.dump(BARConstants.Branch.AllBranches, (res) => {
                        assert.equal(res.dump.Resources.filter(r => r.ID === resourceID && r.BRANCHID === branchIDTo).length, 1);
                        assert.equal(res.dump.Resources.filter(r => r.ID === resourceID && r.BRANCHID === branchIDFrom).length, 0);
                        assert.equal(res.dump.Comments.filter(c => c.RESOURCEID === resourceID && c.BRANCHID === branchIDTo).length, 0);
                        done();
                    })
                });
            });
        });
    });

    suite("Thumbnail", function () {
        test("it returns a thumbnail", function (done) {
            var archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver);

            archive.open(db1, (toc) => {
                archive.getThumbnail("30D213DD-391C-3CDD-81F1-B1409DCACADE", (obj) => {
                    assert.equal(obj.ID, "30D213DD-391C-3CDD-81F1-B1409DCACADE");
                    assert.equal(obj.ATTRIBUTES.resourceID, "55ECC516-0F00-19DD-7570-B1409DBCC586");
                    assert.equal(obj.ATTRIBUTES.branchID, "Master");
                    assert.string(obj.ATTRIBUTES.image);
                    done();
                });
            });
        });

        test("it returns an error when no thumbnail", function (done) {
            var archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver);
            var nonExsistingResourceID = "30D213DD-391C-3CDD-0000-B1409DCACADE";

            archive.open(db1, (toc) => {
                archive.getThumbnail(nonExsistingResourceID, (obj) => {
                    assert.equal(obj.error, "Balsamiq Archive error: Unexpected database response while getting thumbnail: {}");
                    done();
                });
            });
        });
    });

    suite("User roles", function () {
        test("canUserUpdateCommentAttributes", function () {
            assert.equal(BalsamiqArchive.canUserUpdateCommentAttributes("<EMAIL>", BARConstants.Role.ROLE_VIEWER, { [BARConstants.CommentAttributesSetters.setLikedStatus]: true }, "<EMAIL>"), false, "VIEWER can't update any comment attribute");
            
            assert.equal(BalsamiqArchive.canUserUpdateCommentAttributes("<EMAIL>", BARConstants.Role.ROLE_COMMENTER, { [BARConstants.CommentAttributesSetters.setLikedStatus]: true }, "<EMAIL>"), true, "COMMENTER can like all comments");
            assert.equal(BalsamiqArchive.canUserUpdateCommentAttributes("<EMAIL>", BARConstants.Role.ROLE_EDITOR, { [BARConstants.CommentAttributesSetters.setLikedStatus]: true }, "<EMAIL>"), true, "EDITOR can like all comments");
            assert.equal(BalsamiqArchive.canUserUpdateCommentAttributes("<EMAIL>", BARConstants.Role.ROLE_ADMIN, { [BARConstants.CommentAttributesSetters.setLikedStatus]: true }, "<EMAIL>"), true, "ADMIN can like all comments");
            
            assert.equal(BalsamiqArchive.canUserUpdateCommentAttributes("<EMAIL>", BARConstants.Role.ROLE_COMMENTER, { [BARConstants.CommentAttributesSetters.setReadStatus]: true }, "<EMAIL>"), true, "COMMENTER can set as read all comments");
            assert.equal(BalsamiqArchive.canUserUpdateCommentAttributes("<EMAIL>", BARConstants.Role.ROLE_EDITOR, { [BARConstants.CommentAttributesSetters.setReadStatus]: true }, "<EMAIL>"), true, "EDITOR can set as read all comments");
            assert.equal(BalsamiqArchive.canUserUpdateCommentAttributes("<EMAIL>", BARConstants.Role.ROLE_ADMIN, { [BARConstants.CommentAttributesSetters.setReadStatus]: true }, "<EMAIL>"), true, "ADMIN can set as read all comments");

            assert.equal(BalsamiqArchive.canUserUpdateCommentAttributes("<EMAIL>", BARConstants.Role.ROLE_COMMENTER, { [BARConstants.CommentAttributesSetters.setTrashedStatus]: true }, "<EMAIL>"), true, "COMMENTER can set as trashed his comments");
            assert.equal(BalsamiqArchive.canUserUpdateCommentAttributes("<EMAIL>", BARConstants.Role.ROLE_COMMENTER, { [BARConstants.CommentAttributesSetters.setTrashedStatus]: false }, "<EMAIL>"), false, "COMMENTER can't set as trashed others' comments");
            assert.equal(BalsamiqArchive.canUserUpdateCommentAttributes("<EMAIL>", BARConstants.Role.ROLE_EDITOR, { [BARConstants.CommentAttributesSetters.setTrashedStatus]: true }, "<EMAIL>"), true, "EDITOR can set as trashed all comments");
            assert.equal(BalsamiqArchive.canUserUpdateCommentAttributes("<EMAIL>", BARConstants.Role.ROLE_ADMIN, { [BARConstants.CommentAttributesSetters.setTrashedStatus]: true }, "<EMAIL>"), true, "ADMIN can set as trashed all comments");

            assert.equal(BalsamiqArchive.canUserUpdateCommentAttributes("<EMAIL>", BARConstants.Role.ROLE_COMMENTER, { [BARConstants.CommentAttributesSetters.setTimestamps]: true }, "<EMAIL>"), true, "COMMENTER can set timestamps of his comments");
            assert.equal(BalsamiqArchive.canUserUpdateCommentAttributes("<EMAIL>", BARConstants.Role.ROLE_COMMENTER, { [BARConstants.CommentAttributesSetters.setTimestamps]: false }, "<EMAIL>"), false, "COMMENTER can't set timestamps of others' comments");
            assert.equal(BalsamiqArchive.canUserUpdateCommentAttributes("<EMAIL>", BARConstants.Role.ROLE_EDITOR, { [BARConstants.CommentAttributesSetters.setTimestamps]: true }, "<EMAIL>"), true, "EDITOR can set timestamps of all comments");
            assert.equal(BalsamiqArchive.canUserUpdateCommentAttributes("<EMAIL>", BARConstants.Role.ROLE_ADMIN, { [BARConstants.CommentAttributesSetters.setTimestamps]: true }, "<EMAIL>"), true, "ADMIN can set timestamps of all comments");
        });

        test("canUserSetCommentData", function () {
            assert.equal(BalsamiqArchive.canUserSetCommentData("<EMAIL>", BARConstants.Role.ROLE_VIEWER, "<EMAIL>"), false, "VIEWER can't set comment data");
            assert.equal(BalsamiqArchive.canUserSetCommentData("<EMAIL>", BARConstants.Role.ROLE_COMMENTER, "<EMAIL>"), false, "COMMENTER can't set data of others' comment");
            assert.equal(BalsamiqArchive.canUserSetCommentData("<EMAIL>", BARConstants.Role.ROLE_COMMENTER, "<EMAIL>"), true, "COMMENTER can set data of his own comment");
            assert.equal(BalsamiqArchive.canUserSetCommentData("<EMAIL>", BARConstants.Role.ROLE_EDITOR, "<EMAIL>"), true, "EDITOR can't set data of others' comment");
            assert.equal(BalsamiqArchive.canUserSetCommentData("<EMAIL>", BARConstants.Role.ROLE_EDITOR, "<EMAIL>"), true, "EDITOR can set data of all comments");
            assert.equal(BalsamiqArchive.canUserSetCommentData("<EMAIL>", BARConstants.Role.ROLE_ADMIN, "<EMAIL>"), true, "ADMIN can't set data of others' comment");
            assert.equal(BalsamiqArchive.canUserSetCommentData("<EMAIL>", BARConstants.Role.ROLE_ADMIN, "<EMAIL>"), true, "ADMIN can set data of all comments");
        });

        test("canUserDeleteComment", function () {
            assert.equal(BalsamiqArchive.canUserDeleteComment("<EMAIL>", BARConstants.Role.ROLE_VIEWER, "<EMAIL>"), false, "VIEWER can't delete comments");
            assert.equal(BalsamiqArchive.canUserDeleteComment("<EMAIL>", BARConstants.Role.ROLE_COMMENTER, "<EMAIL>"), false, "COMMENTER can't delete others' comments");
            assert.equal(BalsamiqArchive.canUserDeleteComment("<EMAIL>", BARConstants.Role.ROLE_COMMENTER, "<EMAIL>"), true, "COMMENTER can delete his own comments");
            assert.equal(BalsamiqArchive.canUserDeleteComment("<EMAIL>", BARConstants.Role.ROLE_EDITOR, "<EMAIL>"), true, "EDITOR can delete all comments");
            assert.equal(BalsamiqArchive.canUserDeleteComment("<EMAIL>", BARConstants.Role.ROLE_ADMIN, "<EMAIL>"), true, "ADMIN can delete all comments");
        });
    });

    suite("Purge Comments", function () {
        test("sets the trashed comment data to empty object", function (done) {
            const userInfo = { name: "<EMAIL>", avatarURL: "" };
            const archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);

            archive.open(db1, (toc) => {
                var attributes = {};
                attributes[BARConstants.CommentAttributesSetters.setTrashedStatus] = true;

                archive.updateCommentsAttributes([toc.dump.Comments[1].ID], [attributes], (res) => {
                    assert.ok(!res.error, "update comments attributes has failed" + res.error);

                    archive.purgeComments("<EMAIL>", (res) => {
                        assert.equal(res.purgedCommentIDs.length, 2);

                        archive.getCommentsData(res.purgedCommentIDs, (res) => {
                            assert.deepEqual(
                                res.comments.map((c) => c.DATA),
                                ["{}", "{}"]
                            );
                            done();
                        });
                    });
                });
            });
        });

        test("doesn't purge comments' data of comments trashed by others", function (done) {
            const userInfo = { name: "<EMAIL>", avatarURL: "" };
            const archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);

            archive.open(db1, (toc) => {
                var attributes = {};
                attributes[BARConstants.CommentAttributesSetters.setTrashedStatus] = true;

                archive.updateCommentsAttributes([toc.dump.Comments[1].ID], [attributes], (res) => {
                    assert.ok(!res.error, "update comments attributes has failed" + res.error);

                    archive.purgeComments("<EMAIL>", (res) => {
                        assert.equal(res.purgedCommentIDs.length, 1);
                        done();
                    });
                });
            });
        });

        test("calls _purgeCommentsAndUsers() on close", function (done) {
            const userInfo = { name: "<EMAIL>", avatarURL: "" };
            const archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver, userInfo);
            const purgeCommentsAndUsersCalls = [];
            archive._purgeCommentsAndUsers = function (callback) {
                purgeCommentsAndUsersCalls.push(arguments);
                callback({});
            };

            archive.open(db1, () => {
                archive.close(db1, (res) => {
                    db1 = null;
                    assert.equal(purgeCommentsAndUsersCalls.length, 1);                 
                    done();
                });
            });
        });

        test("it purges comments and users, preserves trashed ancestors of a non trashed comment", function (done) {
            let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver);        
            archive.open(db3, (res) => {                
                archive.purgeCommentsAndUsers((res) => {
                        assert.equal(res.purgedComments.length, 1);                 
                        assert.equal(res.purgedUsers.length, 1);                 
                        assert.equal(res.purgedUsers[0], "<EMAIL>");                 
                        done();
                });
            });               
        });

        test("it purges comments and users, removes nested trashed comments", function (done) {
            let archive = new BalsamiqArchive.BalsamiqArchive(MockDbDriver);            
            archive.open(db4, (res) => {                
                archive.purgeCommentsAndUsers((res) => {
                        assert.equal(res.purgedComments.length, 4);                 
                        assert.equal(res.purgedUsers.length, 1);                 
                        done();
                });
            });               
        });
    });

    suite('composeImagePermalinkUrl', function() {
        test('returns correct URL with default format', function() {
            const result = composeImagePermalinkUrl('https://example.com', '12345', 'default');
            assert.strictEqual(result, 'https://example.com/p/12345.png');
        });

        test('returns correct URL with specified format', function() {
            const result = composeImagePermalinkUrl('https://example.com', '12345', 'default', 'jpg');
            assert.strictEqual(result, 'https://example.com/p/12345.jpg');
        });

        test('returns correct URL with different permalinkKind', function() {
            assert.strictEqual(composeImagePermalinkUrl('https://example.com', '12345', 'cloud'), 'https://example.com/c/12345.png');
            assert.strictEqual(composeImagePermalinkUrl('https://example.com', '12345', 'confluence'), 'https://example.com/a/12345.png');
            assert.strictEqual(composeImagePermalinkUrl('https://example.com', '12345', 'jira'), 'https://example.com/j/12345.png');
            assert.strictEqual(composeImagePermalinkUrl('https://example.com', '12345', 'gd'), 'https://example.com/g/12345.png');
        });

        test('returns correct URL with unknown permalinkKind', function() {
            const result = composeImagePermalinkUrl('https://example.com', '12345', 'unknown');
            assert.strictEqual(result, 'https://example.com/p/12345.png');
        });

        test('returns correct URL with empty permalinkKind', function() {
            const result = composeImagePermalinkUrl('https://example.com', '12345', '');
            assert.strictEqual(result, 'https://example.com/p/12345.png');
        });

        test('returns correct URL with null permalinkKind', function() {
            const result = composeImagePermalinkUrl('https://example.com', '12345', null);
            assert.strictEqual(result, 'https://example.com/p/12345.png');
        });

        test('returns correct URL with undefined permalinkKind', function() {
            const result = composeImagePermalinkUrl('https://example.com', '12345', undefined);
            assert.strictEqual(result, 'https://example.com/p/12345.png');
        });
    });

    suite('isPermalinkUrlValid', function() {
        test('returns true for valid permalink URL with default format', function() {
            const result = isPermalinkUrlValid('https://example.com', 'https://example.com/p/12345.png');
            assert.strictEqual(result, true);
        });

        test('returns true for valid permalink URL with specified format', function() {
            const result = isPermalinkUrlValid('https://example.com', 'https://example.com/p/12345.jpg', 'jpg');
            assert.strictEqual(result, true);
        });

        test('returns true for valid permalink URL with different permalinkKind', function() {
            assert.strictEqual(isPermalinkUrlValid('https://example.com', 'https://example.com/c/12345.png'), true);
            assert.strictEqual(isPermalinkUrlValid('https://example.com', 'https://example.com/a/12345.png'), true);
            assert.strictEqual(isPermalinkUrlValid('https://example.com', 'https://example.com/j/12345.png'), true);
            assert.strictEqual(isPermalinkUrlValid('https://example.com', 'https://example.com/g/12345.png'), true);
        });

        test('returns false for invalid permalink URL with incorrect format', function() {
            const result = isPermalinkUrlValid('https://example.com', 'https://example.com/p/12345.gif', 'png');
            assert.strictEqual(result, false);
        });

        test('returns false for invalid permalink URL with incorrect host', function() {
            const result = isPermalinkUrlValid('https://example.com', 'https://invalid.com/p/12345.png');
            assert.strictEqual(result, false);
        });

        test('returns false for invalid permalink URL with incorrect path', function() {
            const result = isPermalinkUrlValid('https://example.com', 'https://example.com/x/12345.png');
            assert.strictEqual(result, false);
        });

        test('returns false for invalid permalink URL with missing format', function() {
            const result = isPermalinkUrlValid('https://example.com', 'https://example.com/p/12345');
            assert.strictEqual(result, false);
        });
    });

    test("getMockupsOrderFromDump handles undefined trashed attribute", function () {
        const dump = {
            Branch: [
                { branchID: Consts.Branch.MasterBranchID, ATTRIBUTES: {} }
            ],
            Resources: [
                {
                    ID: "mockup1",
                    BRANCHID: Consts.Branch.MasterBranchID,
                    ATTRIBUTES: { kind: "mockup", order: 1, trashed: undefined }
                }
            ]
        };

        const mockupsOrder = BmprUtils.getMockupsOrderFromDump(dump);
        assert.notEqual(mockupsOrder.length, 0, "mockupsOrder should not be empty");
        assert.strictEqual(mockupsOrder[0], "mockup1", "mockupsOrder should contain the mockup ID");
    });

    test("getMockupsOrderFromDumpExt handles undefined trashed attribute", function () {
        const dump = {
            Branch: [
                { branchID: Consts.Branch.MasterBranchID, ATTRIBUTES: {} }
            ],
            Resources: [
                {
                    ID: "mockup1",
                    BRANCHID: Consts.Branch.MasterBranchID,
                    ATTRIBUTES: { kind: "mockup", order: 1, trashed: undefined }
                }
            ]
        };

        const mockupsOrder = BmprUtils.getMockupsOrderFromDumpExt(dump);
        assert.notEqual(mockupsOrder.length, 0, "mockupsOrder should not be empty");
        assert.strictEqual(mockupsOrder[0], "mockup1|Master", "mockupsOrder should contain the mockup ID");
    });
});
