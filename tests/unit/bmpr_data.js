var assert = require('assert-plus');
var MockDbDriver = require('./mockdbdriver');
var BARConstants = require('../../src/js/BalsamiqArchiveConstants');

// CREATE TABLE INFO (NAME VARCHAR(255) PRIMARY KEY, VALUE TEXT);
// INSERT INTO INFO VALUES('ArchiveFormat','bmpr');
// INSERT INTO INFO VALUES('ArchiveRevisionUUID','409C51A3-BEF7-41D2-8977-B1434F5440FA');
// INSERT INTO INFO VALUES('ArchiveAttributes','{"creationDate":1430744249151,"name":"Web Demo Project"}');
// INSERT INTO INFO VALUES('ArchiveRevision','3121');
// INSERT INTO INFO VALUES('SchemaVersion','2.0');
// CREATE TABLE BRANCHES (ID VARCHAR(255) PRIMARY KEY, ATTRIBUTES TEXT);
// INSERT INTO BRANCHES VALUES('Master','{"selectionColor":9813234,"fontSize":13,"linkColor":545684,"projectDescription":"This is a *sample project* to help you learn your way around. \r\rFeel free to delete it or use some of these controls for your own projects.\r\rIf you want to save your data, *Download the Project BMPR* (from the Project menu) and open it in *Balsamiq for Desktop*.","skinName":"sketch","fontFace":"Balsamiq Sans",}');
// INSERT INTO BRANCHES VALUES('71A404CF-096B-A3D7-A7B2-B14108F708E0','{"branchName":"Alternate control placement"}');
// CREATE TABLE RESOURCES (ID VARCHAR(255), BRANCHID VARCHAR(255), ATTRIBUTES TEXT, DATA LONGTEXT, PRIMARY KEY (ID, BRANCHID), FOREIGN KEY (BRANCHID) REFERENCES BRANCHES(ID));
// INSERT INTO RESOURCES VALUES('55ECC516-0F00-19DD-7570-B1409DBCC586','Master','{"order":3853740.2934019943,"importedFrom":"","name":"Mockup #1","kind":"mockup","trashed":false,"creationDate":0,"notes":null}','{"mockup":{"controls":{"control":[{"ID":"0","measuredH":"27","measuredW":"63","typeID":"Button","x":"50","y":"50","zOrder":"0"},{"ID":"1","measuredH":"27","measuredW":"156","properties":{"text":"One, Two, Three"},"typeID":"ButtonBar","x":"50","y":"100","zOrder":"1"}]},"measuredH":"127","measuredW":"206","mockupH":"77","mockupW":"156","version":"1.0"}}');
// INSERT INTO RESOURCES VALUES('55ECC516-0F00-19DD-7570-B1409DBCC586','71A404CF-096B-A3D7-A7B2-B14108F708E0','{"importedFrom":"","creationDate":0,"notes":null}','{"mockup":{"controls":{"control":[{"ID":"0","measuredH":"27","measuredW":"63","typeID":"Button","x":"141","y":"147","zOrder":"0"},{"ID":"1","measuredH":"27","measuredW":"156","properties":{"text":"One, Two, Three"},"typeID":"ButtonBar","x":"50","y":"100","zOrder":"1"}]},"measuredH":"174","measuredW":"206","mockupH":"74","mockupW":"156","version":"1.0"}}');
// INSERT INTO RESOURCES VALUES('E474EDE7-903E-D4A4-9DE0-B141A4027A62','Master','{"order":4952475.8368469635,"importedFrom":"","name":"Mockup #2","kind":"mockup","trashed":false,"creationDate":0,"notes":null}','{"mockup":{"controls":{"control":[{"ID":"0","measuredH":"100","measuredW":"150","properties":{"curvature":"0","direction":"top","p0":{"x":0,"y":0},"p1":{"x":0.5,"y":0},"p2":{"x":150,"y":100},"shape":"bezier"},"typeID":"Arrow","x":"50","y":"50","zOrder":"0"},{"ID":"1","measuredH":"112","measuredW":"200","typeID":"Alert","x":"220","y":"158","zOrder":"1"},{"ID":"2","measuredH":"27","measuredW":"93","typeID":"ComboBox","x":"58","y":"315","zOrder":"2"}]},"measuredH":"342","measuredW":"420","mockupH":"292","mockupW":"370","version":"1.0"}}');
// INSERT INTO RESOURCES VALUES('E474EDE7-903E-D4A4-9DE0-B141A4027A62','71A404CF-096B-A3D7-A7B2-B14108F708E0','{"importedFrom":"","creationDate":0,"notes":null}','{"mockup":{"controls":{"control":[{"ID":"0","measuredH":"100","measuredW":"150","properties":{"curvature":"0","direction":"top","p0":{"x":0,"y":0},"p1":{"x":0.5,"y":0},"p2":{"x":150,"y":100},"shape":"bezier"},"typeID":"Arrow","x":"50","y":"50","zOrder":"0"},{"ID":"1","measuredH":"112","measuredW":"200","typeID":"Alert","x":"220","y":"158","zOrder":"1"},{"ID":"2","measuredH":"27","measuredW":"93","typeID":"ComboBox","x":"337","y":"34","zOrder":"2"}]},"measuredH":"270","measuredW":"430","mockupH":"236","mockupW":"380","version":"1.0"}}');
// CREATE TABLE THUMBNAILS (ID VARCHAR(255) PRIMARY KEY, ATTRIBUTES MEDIUMTEXT);
// INSERT INTO THUMBNAILS VALUES('30D213DD-391C-3CDD-81F1-B1409DCACADE','{"resourceID":"55ECC516-0F00-19DD-7570-B1409DBCC586","branchID":"Master","image":"iVBORw0KGgoAAAANSUhEUgAAAJ0AAABOCAYAAAA6jBRXAAALa0lEQVR42u1daUxUWRauv2ZMGro7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\nyjCoQgAAAABJRU5ErkJggg=="}');
// INSERT INTO THUMBNAILS VALUES('2CD5E071-1F19-410B-0EF1-B14108F94D54','{"resourceID":"55ECC516-0F00-19DD-7570-B1409DBCC586","branchID":"71A404CF-096B-A3D7-A7B2-B14108F708E0","image":"iVBORw0KGgoAAAANSUhEUgAAAJ0AAABLCAYAAABqQYXkAAALa0lEQVR42u1daUxUWRauv2ZMGro7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\nNNCI8gAAAABJRU5ErkJggg=="}');
// INSERT INTO THUMBNAILS VALUES('36E0B440-F141-6CA6-B578-B141A41F40B4','{"resourceID":"E474EDE7-903E-D4A4-9DE0-B141A4027A62","branchID":"Master","image":"iVBORw0KGgoAAAANSUhEUgAAAMQAAACcCAYAAAAgewTxAAARA0lEQVR42u2dCXRNVxfHry/xfYaa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\ny/8B3vX4At6SeKAAAAAASUVORK5CYII="}');
// INSERT INTO THUMBNAILS VALUES('73E0A5C9-8D23-B531-2A7A-B1420400771F','{"resourceID":"E474EDE7-903E-D4A4-9DE0-B141A4027A62","branchID":"71A404CF-096B-A3D7-A7B2-B14108F708E0","image":"iVBORw0KGgoAAAANSUhEUgAAAMQAAAB5CAYAAABm8o7AAAAQm0lEQVR42u1dCVRV1Rq+Kg9nNE1R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\nrcXDouyBAwcKJIT/A08N4QnPi5MLAAAAAElFTkSuQmCC"}');
// CREATE TABLE USERS (ID TEXT PRIMARY KEY, ATTRIBUTES TEXT);
// INSERT INTO USERS VALUES('<EMAIL>','{"displayName": "algnappis", "userName": "<EMAIL>", "avatar": "", "platformSiteID": "37f48f0b84484bffbe01aad1d93496ea", "platformKind": "cloud"}');
// INSERT INTO USERS VALUES(' <EMAIL>','{"displayName": "marco", "userName": "<EMAIL>", "avatar": "", "platformSiteID": "37f48f0b84484bffbe01aad1d93496ea", "platformKind": "cloud"}');
// INSERT INTO USERS VALUES(' <EMAIL>','{"displayName": "andrea", "userName": "<EMAIL>", "avatar": "", "platformSiteID": "37f48f0b84484bffbe01aad1d93496ea", "platformKind": "cloud"}');
// CREATE TABLE COMMENTS (ID TEXT PRIMARY KEY, BRANCHID TEXT, RESOURCEID TEXT, DATA TEXT, USERID TEXT, ATTRIBUTES TEXT);
// INSERT INTO COMMENTS VALUES('1802617a36aa49f78274dcf5eb3c5547','FBE786C4-D0E8-3A34-8E14-D60902952B3F','55ECC516-0F00-19DD-7570-B1409DBCC586','{"text": "I like this one!"}','<EMAIL>','{ "readBy": ["<EMAIL>", "<EMAIL>"], "likedBy":  ["<EMAIL>"], "trashed": false, "trashedBy":"",  "parentID": "", "timestamp": " 1471532300"}');
// INSERT INTO COMMENTS VALUES('ae736994654f4913976f9923e97eccf4','FBE786C4-D0E8-3A34-8E14-D60902952B3F','55ECC516-0F00-19DD-7570-B1409DBCC586','{"text": "+1 for me"}','<EMAIL>','{ "readBy": ["<EMAIL>", "<EMAIL>"], "likedBy":  ["<EMAIL>"], "trashed": false, "trashedBy":"", "parentID": "1802617a36aa49f78274dcf5eb3c5547", "timestamp": " 1471532330"}');

function buildTestBmpr(dbId) {
    MockDbDriver.open(dbId);
    MockDbDriver.run(dbId, "CREATE TABLE INFO (NAME VARCHAR(255) PRIMARY KEY, VALUE TEXT);", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO INFO VALUES('ArchiveFormat','bmpr');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO INFO VALUES('ArchiveRevisionUUID','409C51A3-BEF7-41D2-8977-B1434F5440FA');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO INFO VALUES('ArchiveAttributes','{\"creationDate\":1430744249151,\"name\":\"Web Demo Project\"}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO INFO VALUES('ArchiveRevision','3121');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO INFO VALUES('SchemaVersion','2.0');", [], function() {} );
    MockDbDriver.run(dbId, "CREATE TABLE BRANCHES (ID VARCHAR(255) PRIMARY KEY, ATTRIBUTES TEXT);", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO BRANCHES VALUES('Master','{\"selectionColor\":9813234,\"modifiedBy\":[],\"fontSize\":13,\"linkColor\":545684,\"projectDescription\":\"This is a *sample project* to help you learn your way around. \\r\\rFeel free to delete it or use some of these controls for your own projects.\\r\\rIf you want to save your data, *Download the Project BMPR* (from the Project menu) and open it in *Balsamiq for Desktop*.\",\"skinName\":\"sketch\",\"creationDate\":1430744249151,\"fontFace\":\"Balsamiq Sans\",\"symbolLibraryID\":\"CDAE8C27-FA47-80F4-90F1-EF0C533390D4\"}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO BRANCHES VALUES('71A404CF-096B-A3D7-A7B2-B14108F708E0','{\"branchName\":\"Alternate control placement\"}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO BRANCHES VALUES('FBE786C4-D0E8-3A34-8E14-D60902952B3F','{\"branchName\":\"Alternte branch\"}');", [], function() {} );
    MockDbDriver.run(dbId, "CREATE TABLE RESOURCES (ID VARCHAR(255), BRANCHID VARCHAR(255), ATTRIBUTES TEXT, DATA LONGTEXT, PRIMARY KEY (ID, BRANCHID), FOREIGN KEY (BRANCHID) REFERENCES BRANCHES(ID));", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO RESOURCES VALUES('55ECC516-0F00-19DD-7570-B1409DBCC586','Master','{\"order\":3853740.2934019943,\"modifiedBy\":null,\"importedFrom\":\"\",\"name\":\"Mockup #1\",\"kind\":\"mockup\",\"trashed\":false,\"creationDate\":0,\"notes\":null}','{\"mockup\":{\"controls\":{\"control\":[{\"ID\":\"0\",\"measuredH\":\"27\",\"measuredW\":\"63\",\"typeID\":\"Button\",\"x\":\"50\",\"y\":\"50\",\"zOrder\":\"0\"},{\"ID\":\"1\",\"measuredH\":\"27\",\"measuredW\":\"156\",\"properties\":{\"text\":\"One, Two, Three\"},\"typeID\":\"ButtonBar\",\"x\":\"50\",\"y\":\"100\",\"zOrder\":\"1\"}]},\"measuredH\":\"127\",\"measuredW\":\"206\",\"mockupH\":\"77\",\"mockupW\":\"156\",\"version\":\"1.0\"}}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO RESOURCES VALUES('55ECC516-0F00-19DD-7570-B1409DBCC586','71A404CF-096B-A3D7-A7B2-B14108F708E0','{\"importedFrom\":\"\",\"modifiedBy\":null,\"creationDate\":0,\"notes\":null}','{\"mockup\":{\"controls\":{\"control\":[{\"ID\":\"0\",\"measuredH\":\"27\",\"measuredW\":\"63\",\"typeID\":\"Button\",\"x\":\"141\",\"y\":\"147\",\"zOrder\":\"0\"},{\"ID\":\"1\",\"measuredH\":\"27\",\"measuredW\":\"156\",\"properties\":{\"text\":\"One, Two, Three\"},\"typeID\":\"ButtonBar\",\"x\":\"50\",\"y\":\"100\",\"zOrder\":\"1\"}]},\"measuredH\":\"174\",\"measuredW\":\"206\",\"mockupH\":\"74\",\"mockupW\":\"156\",\"version\":\"1.0\"}}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO RESOURCES VALUES('E474EDE7-903E-D4A4-9DE0-B141A4027A62','Master','{\"order\":4952475.8368469635,\"modifiedBy\":null,\"importedFrom\":\"\",\"name\":\"Mockup #2\",\"kind\":\"mockup\",\"trashed\":false,\"creationDate\":0,\"notes\":null}','{\"mockup\":{\"controls\":{\"control\":[{\"ID\":\"0\",\"measuredH\":\"100\",\"measuredW\":\"150\",\"properties\":{\"curvature\":\"0\",\"direction\":\"top\",\"p0\":{\"x\":0,\"y\":0},\"p1\":{\"x\":0.5,\"y\":0},\"p2\":{\"x\":150,\"y\":100},\"shape\":\"bezier\"},\"typeID\":\"Arrow\",\"x\":\"50\",\"y\":\"50\",\"zOrder\":\"0\"},{\"ID\":\"1\",\"measuredH\":\"112\",\"measuredW\":\"200\",\"typeID\":\"Alert\",\"x\":\"220\",\"y\":\"158\",\"zOrder\":\"1\"},{\"ID\":\"2\",\"measuredH\":\"27\",\"measuredW\":\"93\",\"typeID\":\"ComboBox\",\"x\":\"58\",\"y\":\"315\",\"zOrder\":\"2\"}]},\"measuredH\":\"342\",\"measuredW\":\"420\",\"mockupH\":\"292\",\"mockupW\":\"370\",\"version\":\"1.0\"}}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO RESOURCES VALUES('E474EDE7-903E-D4A4-9DE0-B141A4027A62','71A404CF-096B-A3D7-A7B2-B14108F708E0','{\"importedFrom\":\"\",\"modifiedBy\":null,\"creationDate\":0,\"notes\":null}','{\"mockup\":{\"controls\":{\"control\":[{\"ID\":\"0\",\"measuredH\":\"100\",\"measuredW\":\"150\",\"properties\":{\"curvature\":\"0\",\"direction\":\"top\",\"p0\":{\"x\":0,\"y\":0},\"p1\":{\"x\":0.5,\"y\":0},\"p2\":{\"x\":150,\"y\":100},\"shape\":\"bezier\"},\"typeID\":\"Arrow\",\"x\":\"50\",\"y\":\"50\",\"zOrder\":\"0\"},{\"ID\":\"1\",\"measuredH\":\"112\",\"measuredW\":\"200\",\"typeID\":\"Alert\",\"x\":\"220\",\"y\":\"158\",\"zOrder\":\"1\"},{\"ID\":\"2\",\"measuredH\":\"27\",\"measuredW\":\"93\",\"typeID\":\"ComboBox\",\"x\":\"337\",\"y\":\"34\",\"zOrder\":\"2\"}]},\"measuredH\":\"270\",\"measuredW\":\"430\",\"mockupH\":\"236\",\"mockupW\":\"380\",\"version\":\"1.0\"}}');", [], function() {} );
    MockDbDriver.run(dbId, "CREATE TABLE THUMBNAILS (ID VARCHAR(255) PRIMARY KEY, ATTRIBUTES MEDIUMTEXT);", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO THUMBNAILS VALUES('30D213DD-391C-3CDD-81F1-B1409DCACADE','{\"resourceID\":\"55ECC516-0F00-19DD-7570-B1409DBCC586\",\"branchID\":\"Master\",\"image\":\"iVBORw0KGgoAAAANSUhEUgAAAJ0AAABOCAYAAAA6jBRXAAALa0lEQVR42u1daUxUWRauv2ZMGro7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\\nyjCoQgAAAABJRU5ErkJggg==\"}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO THUMBNAILS VALUES('2CD5E071-1F19-410B-0EF1-B14108F94D54','{\"resourceID\":\"55ECC516-0F00-19DD-7570-B1409DBCC586\",\"branchID\":\"71A404CF-096B-A3D7-A7B2-B14108F708E0\",\"image\":\"iVBORw0KGgoAAAANSUhEUgAAAJ0AAABLCAYAAABqQYXkAAALa0lEQVR42u1daUxUWRauv2ZMGro7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\\nNNCI8gAAAABJRU5ErkJggg==\"}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO THUMBNAILS VALUES('36E0B440-F141-6CA6-B578-B141A41F40B4','{\"resourceID\":\"E474EDE7-903E-D4A4-9DE0-B141A4027A62\",\"branchID\":\"Master\",\"image\":\"iVBORw0KGgoAAAANSUhEUgAAAMQAAACcCAYAAAAgewTxAAARA0lEQVR42u2dCXRNVxfHry/xfYaa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\\ny/8B3vX4At6SeKAAAAAASUVORK5CYII=\"}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO THUMBNAILS VALUES('73E0A5C9-8D23-B531-2A7A-B1420400771F','{\"resourceID\":\"E474EDE7-903E-D4A4-9DE0-B141A4027A62\",\"branchID\":\"71A404CF-096B-A3D7-A7B2-B14108F708E0\",\"image\":\"iVBORw0KGgoAAAANSUhEUgAAAMQAAAB5CAYAAABm8o7AAAAQm0lEQVR42u1dCVRV1Rq+Kg9nNE1R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\\nrcXDouyBAwcKJIT/A08N4QnPi5MLAAAAAElFTkSuQmCC\"}');", [], function() {} );
    MockDbDriver.run(dbId, "CREATE TABLE USERS (ID TEXT PRIMARY KEY, ATTRIBUTES TEXT);", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO USERS VALUES('<EMAIL>','{\"displayName\": \"algnappis\", \"userName\": \"<EMAIL>\", \"avatar\": \"\", \"platformSiteID\": \"37f48f0b84484bffbe01aad1d93496ea\", \"platformKind\": \"cloud\"}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO USERS VALUES('<EMAIL>','{\"displayName\": \"marco\", \"userName\": \"<EMAIL>\", \"avatar\": \"\", \"platformSiteID\": \"37f48f0b84484bffbe01aad1d93496ea\", \"platformKind\": \"cloud\"}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO USERS VALUES('<EMAIL>','{\"displayName\": \"andrea\", \"userName\": \"<EMAIL>\", \"avatar\": \"\", \"platformSiteID\": \"37f48f0b84484bffbe01aad1d93496ea\", \"platformKind\": \"cloud\"}');", [], function() {} );
    MockDbDriver.run(dbId, "CREATE TABLE COMMENTS (ID VARCHAR(255) PRIMARY KEY, BRANCHID VARCHAR(255), RESOURCEID VARCHAR(255), DATA LONGTEXT, USERID VARCHAR(255), ATTRIBUTES TEXT, FOREIGN KEY (USERID) REFERENCES USERS(ID), FOREIGN KEY (RESOURCEID, BRANCHID) REFERENCES RESOURCES(ID, BRANCHID));", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO COMMENTS VALUES('1802617a36aa49f78274dcf5eb3c5547','71A404CF-096B-A3D7-A7B2-B14108F708E0','55ECC516-0F00-19DD-7570-B1409DBCC586','{\"text\": \"I like this one, instead\"}','<EMAIL>','{ \"readBy\": [\"<EMAIL>\", \"<EMAIL>\"], \"likedBy\":  [\"<EMAIL>\"], \"trashed\": false, \"trashedBy\":\"\", \"parentID\": \"\", \"timestamp\": 1471532320}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO COMMENTS VALUES('0e29271084a8431884caf9e1f28ffef2','Master','55ECC516-0F00-19DD-7570-B1409DBCC586','{\"text\": \"This is my favourite\"}',     '<EMAIL>','{ \"readBy\": [\"<EMAIL>\", \"<EMAIL>\"], \"likedBy\":  [\"<EMAIL>\"], \"trashed\": false, \"trashedBy\":\"\", \"parentID\": \"\", \"timestamp\": 1471532300}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO COMMENTS VALUES('d6e79000a631472aaa1b030d0e2ad05b','Master','55ECC516-0F00-19DD-7570-B1409DBCC586','{\"text\": \"Naaa, look at the alternate!\"}','<EMAIL>','{ \"readBy\": [\"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\"], \"likedBy\":  [\"<EMAIL>\"], \"trashed\": true, \"trashedBy\":\"<EMAIL>\", \"parentID\": \"0e29271084a8431884caf9e1f28ffef2\", \"timestamp\": 1471532380}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO COMMENTS VALUES('08c979be6dc648ddbfc4c5b56daed027','Master','E474EDE7-903E-D4A4-9DE0-B141A4027A62','{\"text\": \"This is my favourite (II)\"}','<EMAIL>','{ \"readBy\": [\"<EMAIL>\", \"<EMAIL>\"], \"likedBy\":  [\"<EMAIL>\"], \"trashed\": false, \"trashedBy\":\"\", \"parentID\": \"\", \"timestamp\": **********}');", [], function() {} );

    return dbId;
}


function buildTestBmpr1() {
    return buildTestBmpr("TestBmpr1");
}

function buildTestBmpr2() {
    var dbId = "TestBmpr2";
    MockDbDriver.open(dbId);
    MockDbDriver.run(dbId, "CREATE TABLE INFO (NAME VARCHAR(255) PRIMARY KEY, VALUE TEXT);", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO INFO VALUES('ArchiveFormat','bmpr');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO INFO VALUES('ArchiveRevisionUUID','409C51A3-BEF7-41D2-8977-B1434F5440FA');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO INFO VALUES('ArchiveAttributes','{\"creationDate\":1430744249151,\"name\":\"Web Demo Project\"}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO INFO VALUES('ArchiveRevision','3121');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO INFO VALUES('SchemaVersion','1.2');", [], function() {} );
    MockDbDriver.run(dbId, "CREATE TABLE BRANCHES (ID VARCHAR(255) PRIMARY KEY, ATTRIBUTES TEXT);", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO BRANCHES VALUES('Master','{\"selectionColor\":9813234,\"modifiedBy\":[],\"fontSize\":13,\"linkColor\":545684,\"projectDescription\":\"This is a *sample project* to help you learn your way around. \\r\\rFeel free to delete it or use some of these controls for your own projects.\\r\\rIf you want to save your data, *Download the Project BMPR* (from the Project menu) and open it in *Balsamiq for Desktop*.\",\"skinName\":\"sketch\",\"creationDate\":1430744249151,\"fontFace\":\"Balsamiq Sans\",\"symbolLibraryID\":\"CDAE8C27-FA47-80F4-90F1-EF0C533390D4\"}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO BRANCHES VALUES('71A404CF-096B-A3D7-A7B2-B14108F708E0','{\"branchName\":\"Alternate control placement\"}');", [], function() {} );
    MockDbDriver.run(dbId, "CREATE TABLE RESOURCES (ID VARCHAR(255), BRANCHID VARCHAR(255), ATTRIBUTES TEXT, DATA LONGTEXT, PRIMARY KEY (ID, BRANCHID), FOREIGN KEY (BRANCHID) REFERENCES BRANCHES(ID));", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO RESOURCES VALUES('55ECC516-0F00-19DD-7570-B1409DBCC586','Master','{\"order\":3853740.2934019943,\"modifiedBy\":null,\"importedFrom\":\"\",\"name\":\"Mockup #1\",\"kind\":\"mockup\",\"trashed\":false,\"creationDate\":0,\"notes\":null}','{\"mockup\":{\"controls\":{\"control\":[{\"ID\":\"0\",\"measuredH\":\"27\",\"measuredW\":\"63\",\"typeID\":\"Button\",\"x\":\"50\",\"y\":\"50\",\"zOrder\":\"0\"},{\"ID\":\"1\",\"measuredH\":\"27\",\"measuredW\":\"156\",\"properties\":{\"text\":\"One, Two, Three\"},\"typeID\":\"ButtonBar\",\"x\":\"50\",\"y\":\"100\",\"zOrder\":\"1\"}]},\"measuredH\":\"127\",\"measuredW\":\"206\",\"mockupH\":\"77\",\"mockupW\":\"156\",\"version\":\"1.0\"}}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO RESOURCES VALUES('55ECC516-0F00-19DD-7570-B1409DBCC586','71A404CF-096B-A3D7-A7B2-B14108F708E0','{\"importedFrom\":\"\",\"modifiedBy\":null,\"creationDate\":0,\"notes\":null}','{\"mockup\":{\"controls\":{\"control\":[{\"ID\":\"0\",\"measuredH\":\"27\",\"measuredW\":\"63\",\"typeID\":\"Button\",\"x\":\"141\",\"y\":\"147\",\"zOrder\":\"0\"},{\"ID\":\"1\",\"measuredH\":\"27\",\"measuredW\":\"156\",\"properties\":{\"text\":\"One, Two, Three\"},\"typeID\":\"ButtonBar\",\"x\":\"50\",\"y\":\"100\",\"zOrder\":\"1\"}]},\"measuredH\":\"174\",\"measuredW\":\"206\",\"mockupH\":\"74\",\"mockupW\":\"156\",\"version\":\"1.0\"}}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO RESOURCES VALUES('E474EDE7-903E-D4A4-9DE0-B141A4027A62','Master','{\"order\":4952475.8368469635,\"modifiedBy\":null,\"importedFrom\":\"\",\"name\":\"Mockup #2\",\"kind\":\"mockup\",\"trashed\":false,\"creationDate\":0,\"notes\":null}','{\"mockup\":{\"controls\":{\"control\":[{\"ID\":\"0\",\"measuredH\":\"100\",\"measuredW\":\"150\",\"properties\":{\"curvature\":\"0\",\"direction\":\"top\",\"p0\":{\"x\":0,\"y\":0},\"p1\":{\"x\":0.5,\"y\":0},\"p2\":{\"x\":150,\"y\":100},\"shape\":\"bezier\"},\"typeID\":\"Arrow\",\"x\":\"50\",\"y\":\"50\",\"zOrder\":\"0\"},{\"ID\":\"1\",\"measuredH\":\"112\",\"measuredW\":\"200\",\"typeID\":\"Alert\",\"x\":\"220\",\"y\":\"158\",\"zOrder\":\"1\"},{\"ID\":\"2\",\"measuredH\":\"27\",\"measuredW\":\"93\",\"typeID\":\"ComboBox\",\"x\":\"58\",\"y\":\"315\",\"zOrder\":\"2\"}]},\"measuredH\":\"342\",\"measuredW\":\"420\",\"mockupH\":\"292\",\"mockupW\":\"370\",\"version\":\"1.0\"}}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO RESOURCES VALUES('E474EDE7-903E-D4A4-9DE0-B141A4027A62','71A404CF-096B-A3D7-A7B2-B14108F708E0','{\"importedFrom\":\"\",\"modifiedBy\":null,\"creationDate\":0,\"notes\":null}','{\"mockup\":{\"controls\":{\"control\":[{\"ID\":\"0\",\"measuredH\":\"100\",\"measuredW\":\"150\",\"properties\":{\"curvature\":\"0\",\"direction\":\"top\",\"p0\":{\"x\":0,\"y\":0},\"p1\":{\"x\":0.5,\"y\":0},\"p2\":{\"x\":150,\"y\":100},\"shape\":\"bezier\"},\"typeID\":\"Arrow\",\"x\":\"50\",\"y\":\"50\",\"zOrder\":\"0\"},{\"ID\":\"1\",\"measuredH\":\"112\",\"measuredW\":\"200\",\"typeID\":\"Alert\",\"x\":\"220\",\"y\":\"158\",\"zOrder\":\"1\"},{\"ID\":\"2\",\"measuredH\":\"27\",\"measuredW\":\"93\",\"typeID\":\"ComboBox\",\"x\":\"337\",\"y\":\"34\",\"zOrder\":\"2\"}]},\"measuredH\":\"270\",\"measuredW\":\"430\",\"mockupH\":\"236\",\"mockupW\":\"380\",\"version\":\"1.0\"}}');", [], function() {} );
    MockDbDriver.run(dbId, "CREATE TABLE THUMBNAILS (ID VARCHAR(255) PRIMARY KEY, ATTRIBUTES MEDIUMTEXT);", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO THUMBNAILS VALUES('30D213DD-391C-3CDD-81F1-B1409DCACADE','{\"resourceID\":\"55ECC516-0F00-19DD-7570-B1409DBCC586\",\"branchID\":\"Master\",\"image\":\"iVBORw0KGgoAAAANSUhEUgAAAJ0AAABOCAYAAAA6jBRXAAALa0lEQVR42u1daUxUWRauv2ZMGro7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\\nyjCoQgAAAABJRU5ErkJggg==\"}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO THUMBNAILS VALUES('2CD5E071-1F19-410B-0EF1-B14108F94D54','{\"resourceID\":\"55ECC516-0F00-19DD-7570-B1409DBCC586\",\"branchID\":\"71A404CF-096B-A3D7-A7B2-B14108F708E0\",\"image\":\"iVBORw0KGgoAAAANSUhEUgAAAJ0AAABLCAYAAABqQYXkAAALa0lEQVR42u1daUxUWRauv2ZMGro7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\\nNNCI8gAAAABJRU5ErkJggg==\"}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO THUMBNAILS VALUES('36E0B440-F141-6CA6-B578-B141A41F40B4','{\"resourceID\":\"E474EDE7-903E-D4A4-9DE0-B141A4027A62\",\"branchID\":\"Master\",\"image\":\"iVBORw0KGgoAAAANSUhEUgAAAMQAAACcCAYAAAAgewTxAAARA0lEQVR42u2dCXRNVxfHry/xfYaa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\\ny/8B3vX4At6SeKAAAAAASUVORK5CYII=\"}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO THUMBNAILS VALUES('73E0A5C9-8D23-B531-2A7A-B1420400771F','{\"resourceID\":\"E474EDE7-903E-D4A4-9DE0-B141A4027A62\",\"branchID\":\"71A404CF-096B-A3D7-A7B2-B14108F708E0\",\"image\":\"iVBORw0KGgoAAAANSUhEUgAAAMQAAAB5CAYAAABm8o7AAAAQm0lEQVR42u1dCVRV1Rq+Kg9nNE1R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\\nrcXDouyBAwcKJIT/A08N4QnPi5MLAAAAAElFTkSuQmCC\"}');", [], function() {} );
    return dbId;
}

function buildTestBmprNestedComments() {
    var dbId = buildTestBmpr("TestBmpr3");    
    MockDbDriver.run(dbId, "INSERT INTO USERS VALUES('<EMAIL>','{\"displayName\": \"pippo\", \"userName\": \"<EMAIL>\", \"avatar\": \"\", \"platformSiteID\": \"37f48f0b84484bffbe01aad1d93496ea\", \"platformKind\": \"cloud\"}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO COMMENTS VALUES('08c979be6dc648ddbfc4c5b56daed028','Master','E474EDE7-903E-D4A4-9DE0-B141A4027A62','{\"text\": \"Bla\"}','<EMAIL>','{ \"readBy\": [\"<EMAIL>\", \"<EMAIL>\"], \"likedBy\":  [\"<EMAIL>\"], \"trashed\": true, \"trashedBy\":\"\", \"parentID\": \"\", \"timestamp\": **********}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO COMMENTS VALUES('08c979be6dc648ddbfc4c5b56daed029','Master','E474EDE7-903E-D4A4-9DE0-B141A4027A62','{\"text\": \"Bla Bla\"}','<EMAIL>','{ \"readBy\": [\"<EMAIL>\", \"<EMAIL>\"], \"likedBy\":  [\"<EMAIL>\"], \"trashed\": true, \"trashedBy\":\"\", \"parentID\": \"08c979be6dc648ddbfc4c5b56daed028\", \"timestamp\": **********}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO COMMENTS VALUES('08c979be6dc648ddbfc4c5b56daed030','Master','E474EDE7-903E-D4A4-9DE0-B141A4027A62','{\"text\": \"Bla Bla Bla\"}','<EMAIL>','{ \"readBy\": [\"<EMAIL>\", \"<EMAIL>\"], \"likedBy\":  [\"<EMAIL>\"], \"trashed\": false, \"trashedBy\":\"\", \"parentID\": \"08c979be6dc648ddbfc4c5b56daed029\", \"timestamp\": **********}');", [], function() {} );
    return dbId;
}

function buildTestBmprNestedCommentsTrashed() {
    var dbId = buildTestBmpr("TestBmpr4");    
    MockDbDriver.run(dbId, "INSERT INTO USERS VALUES('<EMAIL>','{\"displayName\": \"pippo\", \"userName\": \"<EMAIL>\", \"avatar\": \"\", \"platformSiteID\": \"37f48f0b84484bffbe01aad1d93496ea\", \"platformKind\": \"cloud\"}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO COMMENTS VALUES('08c979be6dc648ddbfc4c5b56daed028','Master','E474EDE7-903E-D4A4-9DE0-B141A4027A62','{\"text\": \"Bla\"}','<EMAIL>','{ \"readBy\": [\"<EMAIL>\", \"<EMAIL>\"], \"likedBy\":  [\"<EMAIL>\"], \"trashed\": true, \"trashedBy\":\"\", \"parentID\": \"\", \"timestamp\": **********}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO COMMENTS VALUES('08c979be6dc648ddbfc4c5b56daed029','Master','E474EDE7-903E-D4A4-9DE0-B141A4027A62','{\"text\": \"Bla Bla\"}','<EMAIL>','{ \"readBy\": [\"<EMAIL>\", \"<EMAIL>\"], \"likedBy\":  [\"<EMAIL>\"], \"trashed\": true, \"trashedBy\":\"\", \"parentID\": \"08c979be6dc648ddbfc4c5b56daed028\", \"timestamp\": **********}');", [], function() {} );
    MockDbDriver.run(dbId, "INSERT INTO COMMENTS VALUES('08c979be6dc648ddbfc4c5b56daed030','Master','E474EDE7-903E-D4A4-9DE0-B141A4027A62','{\"text\": \"Bla Bla Bla\"}','<EMAIL>','{ \"readBy\": [\"<EMAIL>\", \"<EMAIL>\"], \"likedBy\":  [\"<EMAIL>\"], \"trashed\": true, \"trashedBy\":\"\", \"parentID\": \"08c979be6dc648ddbfc4c5b56daed029\", \"timestamp\": **********}');", [], function() {} );
    return dbId;
}

function closeDB(dbId) {
    MockDbDriver.close(dbId);
}

module.exports = {
    buildTestBmpr1,
    buildTestBmpr2,
    buildTestBmprNestedComments,
    buildTestBmprNestedCommentsTrashed,
    closeDB: closeDB
};
