# balsamiq-archive-server

A node.js server that speaks BAR and integrates with other platforms

Master list of APIs: https://balsamiq.atlassian.net/wiki/x/iwFBAg

## How to run BAS locally

In order to run BAS you need these running services:

- MySQL, running on port 3306 (default)
- Redis running on port 6379 (default).
- RTC server, running remotely
- `envchain` fro env setup, `nvm` to select the required node version
- The BAS node server

Configure access to the Balsamiq private npm repository: add to your local `~/.npmrc` the following line:

     //npm.balsamiq.com/:_authToken="<TOKEN>"

Where `<TOKEN>` is stored in 1Password, at the record `Private Verdaccio NPM registry`.

### Required environment variables

Set up a new BAS envchain using the following variables

```bash
BAS_DB_DATABASE="BAS"
BAS_DB_HOST="127.0.0.1"
BAS_DB_PASSWORD=""
BAS_DB_PERMALINKS_DATABASE="PERMALINKS"
BAS_DB_USER="root"
BAS_ENV="local"
RTC_SECRET="[the whole plaintext value of balsamiqsrlinternal@aws/eu-west-1/secret_manager/rtc-staging-managedConfig]"
```

### How to run the BAS node server

```bash
cd src
nvm use
npm install --build-from-source --python=/usr/bin/python3 sqlite3 && npm install
envchain [YOUR_BAS_ENVCHAIN] npm start
```

### How to run the independent gardening processes

- from the `src` directory run `node entrypoint_main_gardening.js` (Main gardening)
- from the `src` directory run `node --import=./register-ts-node.js entrypoint_cloud_gardening.ts` (Cloud gardening)
- from the `src` directory run `node --import=./register-ts-node.js entrypoint_apps_watcher.ts` (Listener for Atlassian and GD web hook and Atlassian event watcher)

### Using mysql docker image to setup a local db

**Setup**: create the folder in which the data will be persisted and pull down `mysql` image

```bash
docker pull mysql
```

**Launch** `mysql` on one terminal

```bash
docker run --rm --name bas-mysql -v $(pwd)/mysql/data:/var/lib/mysql -v $(pwd)/mysql/config:/etc/mysql/conf.d -p 3306:3306 -e MYSQL_ROOT_PASSWORD=my-secret-pw mysql
```

and the server in another one

```bash
BAS_DB_PASSWORD=my-secret-pw npm start
```

** If you want to work with Cloud you need to setup its connector in the MySql database **

```sql
INSERT INTO BAS.CONNECTOR_DATA (ID, DATA)
VALUES ('cloud_cloud-server', 'PUT THE JSON SECRETS HERE')
```

### How to set up BAS development environment

Detailed instruction can be found here:

https://balsamiq.atlassian.net/wiki/x/GoDlAg

### How to launch BAS for atlassian development

- update entries in `proxyConfig` of `config-local.ts` to use `[YOUR_NGROK_DOMAIN]` as host
- launch BAS as usual with `envchain [YOUR_BAS_ENVCHAIN] npm start`
- launch grunt in `bw-atlassian` repo using `npx grunt` [and make sure bw-atlassian is configured for local developent](https://balsamiq.atlassian.net/wiki/spaces/OS/pages/48594970/BAS+-+How+to+setup+the+development+env+mostly+for+bw-atlassian#Prepare-Atlassian-Development-Environment)
- launch nginx using the script (or the config file) in `bas/local_script` folder
- launch ngrok to expose nginx to the web `ngrok http --domain [YOUR_NGROK_DOMAIN] 8080`

#### How it works

- Confluence/Jira Web Client: creates signed requests to fetch the resources and sends them to `[YOUR_NGROK_DOMAIN]/(jira|confluence)/endpoint/[name_of_resource]?jwt=...`
- BAS: verifies the jwt's signature and forward the requests to the `host` in `proxyConfig`
- NGINX: It's the only endpoint exposed via ngrok and receives requests from
  - the Web Client, and forwards them to BAS
  - BAS, and forwards them to bw-atlassian's static server
- bw-atlassian: serve the static files that Web Client needs, and returns them via NGINX to BAS and finally to the Web Client.
