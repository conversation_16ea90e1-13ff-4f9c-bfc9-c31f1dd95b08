# BMPR [![semantic-release](https://img.shields.io/badge/%20%20%F0%9F%93%A6%F0%9F%9A%80-semantic--release-e10079.svg)](https://github.com/semantic-release/semantic-release)

[CHANGELOG.md](https://github.com/balsamiq/bmpr/blob/master/CHANGELOG.md)

This library is published on https://npm.balsamiq.com/-/web/detail/@balsamiq/bmpr, so if you want to use it, make sure you have a `.npmrc` next to your `package.json` with this content:
```
registry=https://npm.balsamiq.com
//npm.balsamiq.com/:_authToken=${PRIVATE_NPM_AUTH_TOKEN}
```

To install it:
```sh
$ npm i @balsamiq/bmpr
```

## Develop
Make sure `node` and `npm` are installed.
Also make sure that the environment variable `PRIVATE_NPM_AUTH_TOKEN`. Consider using [envchain](https://balsamiq.atlassian.net/wiki/x/LIBJn) to make things secure and easy.

Install the dependencies via `npm i`.

The source code is in the `src` folder. `lib` is the output folder.

To immediately see and use the changes you're making in this repository from another project that uses it, you need to create a symbolic link via `npm link` see [here](https://docs.npmjs.com/cli/link) for documentation.

In this repo:

```sh
$ npm link
```

also run `npm run build:watch` in order to watch the src folder and compile files from `src` to `lib`.

In the downstream project that uses this package:

```sh
$ npm link @balsamiq/bmpr
```

To revert to non-development mode, remove the symlink from the downstream project by simply running the install command:

```sh
$ npm install @balsamiq/bmpr
```

## Release
A new package will be automatically released on every push to master depending on the conventional commits.

When merging into the `master` branch a [Github Action](https://github.com/balsamiq/bmpr/actions) will be triggered and it will end up publishing a package in our private repo https://npm.balsamiq.com/-/web/detail/@balsamiq/bmpr.

In addition to this, it creates a release commit in the same release branch with the text `[skip ci]` so it won't trigger a new build, a release package with release notes and it updates [CHANGELOG.md](https://github.com/balsamiq/bmpr/blob/release/CHANGELOG.md).

## Videos
To introduce people to these new concepts, you can find some videos [here](https://www.youtube.com/watch?v=dJ0lG5mWGGA&list=PLKs5PPwoIXSiUusgM5zUEySaNVJHVWOSN), explaining:
- how to use the produced npm package locally;
- how to develop a new feature and immediately see the changes in the downstream projects;
- how create a new PR for some new development;
- ~~how to release.~~ *Obsolete*****
