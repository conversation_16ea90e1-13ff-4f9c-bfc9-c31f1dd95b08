BAS Build Process
===================

The command-line tool `build.py` is used to:

 - Build BAS in production
 - Build BAS in staging
 - Run tests
 
 Run `build.py -h` to get help.
 
 In general, the idea is that `build.py` always takes at least 2 mandatory arguments:
 
  - The environment (`staging`, `ondeck` or `production`)
  - The "mode", that identifies the set of operations to execute. It maps 1-1 with <PERSON>: each mode is meant to cover one specific Jenkins job.
