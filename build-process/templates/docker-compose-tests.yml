version: '2'
services:
  bas:
    image: "$IMAGE"
    command: bash -c "rm -rf /target/* && sleep 5 && npm run coverage -- --forbid-only && mv coverage /target"
    volumes:
      - ./target:/target
    links:
      - redis
      - mysql
    environment:
      - BAS_DB_HOST=mysql
      - BAS_DB_USER=root
      - BAS_DB_PASSWORD=root
      - REDIS_HOST=redis
  redis:
    image: redis:latest
  mysql:
    image: mysql:5.7
    environment:
      - MYSQL_ROOT_PASSWORD=root
