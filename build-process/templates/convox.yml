services:
  bas:
    domain: [[DOMAINS]]
    image: [[IMAGE]]
    command: "AWS_INSTANCE_ID=`curl -s http://***************/latest/meta-data/instance-id` /usr/local/bin/node --loader ts-node/esm monitor.ts runForever server.js"
    environment:
      - BAS_DB_PASSWORD
      - BAS_DB_USER
      - BAS_DB_HOST
      - LOGGER_URL
      - RTC_SUBSCRIBE_KEY
      - RTC_PUBLISH_KEY
      - NEW_LAMBDA_RTC_USE_JWT=false
      - RTC_AUTH_TOKEN_SECRET
      - REDIS_PORT
      - REDIS_URL
      - DEBUG=
      - OUTPUT_JSON_FOR_ES=true
      - BUILD_NUMBER=[[BUILD_NUMBER]]
    port: 4000
    sticky: false
    health:
      path: /health?db=true
      interval: 30
      grace: 60

  apps-watcher:
    image: [[IMAGE]]
    command: "AWS_INSTANCE_ID=`curl -s http://***************/latest/meta-data/instance-id` /usr/local/bin/node --import=./register-ts-node.js entrypoint_apps_watcher.ts"
    environment:
      - BAS_DB_PASSWORD
      - BAS_DB_USER
      - BAS_DB_HOST
      - LOGGER_URL
      - RTC_SUBSCRIBE_KEY
      - RTC_PUBLISH_KEY
      - NEW_LAMBDA_RTC_USE_JWT=false
      - RTC_AUTH_TOKEN_SECRET
      - REDIS_PORT
      - REDIS_URL
      - OUTPUT_JSON_FOR_ES=true
      - BUILD_NUMBER=[[BUILD_NUMBER]]

  cronjob:
    image: [[IMAGE]]
    environment:
      - BAS_DB_PASSWORD
      - BAS_DB_USER
      - BAS_DB_HOST
      - LOGGER_URL
      - RTC_SUBSCRIBE_KEY
      - RTC_PUBLISH_KEY
      - NEW_LAMBDA_RTC_USE_JWT=false
      - RTC_AUTH_TOKEN_SECRET
      - REDIS_PORT
      - REDIS_URL
      - DEBUG=
      - OUTPUT_JSON_FOR_ES=true
      - BUILD_NUMBER=[[BUILD_NUMBER]]

timers:
  cloud-gardening:
    command: "AWS_INSTANCE_ID=`curl -s http://***************/latest/meta-data/instance-id` /usr/local/bin/node --import=./register-ts-node.js monitor.ts runOnce entrypoint_cloud_gardening.ts"
    schedule: "[[CLOUD_GARDENING_SCHEDULE]]"
    service: cronjob
  main-gardening:
    command: "AWS_INSTANCE_ID=`curl -s http://***************/latest/meta-data/instance-id` /usr/local/bin/node --import=./register-ts-node.js monitor.ts runOnce entrypoint_main_gardening.js"
    schedule: "[[MAIN_GARDENING_SCHEDULE]]"
    service: cronjob
