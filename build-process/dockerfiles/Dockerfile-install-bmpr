# Variables passed at build-time with "docker build"
ARG BASE_IMAGE
# Base image (different until we will run both the app and the tests with Node 12)
FROM ${BASE_IMAGE}

ARG PRIVATE_NPM_AUTH_TOKEN

# OS dependencies
RUN apk update && apk upgrade
RUN apk add --no-cache git python3 make g++ bash curl

# Setting NODE_ENV this way will prevent node dev-dependencies from being installed
# and certain libraries to behave more "production" like
ENV NODE_ENV=production

# Install BAS node dependencies
COPY bas/src/.npmrc /tmp/dependencies/
COPY bas/src/package.json /tmp/dependencies/
COPY bas/src/package-lock.json /tmp/dependencies/
RUN cd /tmp/dependencies &&\
    export PYTHON=/usr/bin/python3 &&\
    npm install --save @balsamiq/bmpr@latest &&\
    mkdir -p /app/bas/src &&\
    cp /tmp/dependencies/package.json /app/bas/src/ &&\
    cp /tmp/dependencies/package-lock.json /app/bas/src/ &&\
    rm -rf /tmp/dependencies

WORKDIR /app/bas/src
