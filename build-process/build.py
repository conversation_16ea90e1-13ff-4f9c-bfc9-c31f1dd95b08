#!/usr/bin/env python3

import argparse
from contextlib import contextmanager
import os
import subprocess
import time


# -----------------------------------------------------------------------------------------------
# -- Environment constants
# --

class Staging(object):
    name = 'staging'
    convox_host = 'convox-staging.balsamiq.com'
    convox_password = os.environ.get('CONVOX_RACK_STAGING_PASSWORD')
    aws_dkr_url = '924748183198.dkr.ecr.us-east-1.amazonaws.com'
    docker_image_base_url = '{0}/convo-regis-196e0zz4zbkuu'.format(aws_dkr_url)
    docker_image_region = 'us-east-1'
    domains = [
        'bas20-staging.balsamiq.com',
        'share-staging.balsamiq.com',
    ]
    main_gardening_schedule = "*/5 * * * ? *"
    cloud_gardening_schedule = "*/10 * * * ? *"
    use_latest_bmpr = True

class Ondeck(object):
    name = 'ondeck'
    convox_host = 'convox-eu-6.balsamiq.com'
    convox_password = os.environ.get('CONVOX_RACK_EU_6_PASSWORD')
    aws_dkr_url = '924748183198.dkr.ecr.eu-west-1.amazonaws.com'
    docker_image_base_url = '{0}/convox-eu-6-bas-registry-whbh05v9sfhg'.format(aws_dkr_url)
    docker_image_region = 'eu-west-1'
    domains = [
        'bas20-ondeck.balsamiq.com',
        'share-ondeck.balsamiq.com',
    ]
    main_gardening_schedule = "0 * * * ? *"
    cloud_gardening_schedule = "*/15 * * * ? *"
    # main_gardening_schedule = "* * 30 2 ? *"
    # cloud_gardening_schedule = "* * 30 2 ? *"
    use_latest_bmpr = False

class Production(object):
    name = 'production'
    convox_host = 'convox-production.balsamiq.com'
    convox_password = os.environ.get('CONVOX_RACK_PRODUCTION_PASSWORD')
    aws_dkr_url = '924748183198.dkr.ecr.us-east-1.amazonaws.com'
    docker_image_base_url = '{0}/convo-regis-1qiv5ksacc1o9'.format(aws_dkr_url)
    docker_image_region = 'us-east-1'
    domains = [
        'bas20.balsamiq.com',
        'share.balsamiq.com',
    ]
    main_gardening_schedule = "0 * * * ? *"
    cloud_gardening_schedule = "*/15 * * * ? *"
    use_latest_bmpr = False


class Local(object):
    name = 'local'


# -----------------------------------------------------------------------------------------------
# -- Main building logic
# --

BASE_IMAGE_FOR_RUNTIME = 'node:22.12.0-alpine3.20'
BASE_IMAGE_FOR_TESTS = 'node:22.12.0-alpine3.20'


class Builder(object):
    log = None

    def __init__(self, opts):
        self.environment = None
        if opts.environment == 'production':
            self.environment = Production()
        elif opts.environment == 'ondeck':
            self.environment = Ondeck()
        elif opts.environment == 'staging':
            self.environment = Staging()
        elif opts.environment == 'local':
            self.environment = Local()
        if hasattr(self.environment, 'convox_host'):
            self.convox = ConvoxRunner(self.environment.convox_host, self.environment.convox_password, 'bas')
            self.dependencies_for_tests_image_name = self.build_docker_image_name('dependencies_tests')
            self.dependencies_image_name = self.build_docker_image_name('dependencies')
            self.tests_image_name = self.build_docker_image_name('run_tests')
            self.server_image_name = self.build_docker_image_name('server')
            self.logged_in_acr = False
        self.private_npm_auth_token = os.environ.get('PRIVATE_NPM_AUTH_TOKEN')
        if not self.private_npm_auth_token:
            raise Exception('Environment variable PRIVATE_NPM_AUTH_TOKEN not defined')
        self.files = Files()

    def build(self, opts):
        with timed_execution() as log:
            self.log = log
            self.files.chdir_to_repos_root()
            try:
                opts.build_method(self, opts)
            finally:
                pass
                # self.files.remove_temporary_files()

    # -------------------------------------------------------------------
    # -- Main entrypoints, one for each different jenkins job

    def first_build(self, opts):
        self.login_to_aws_ecr()
        convox_release_id = self.first_build_with_convox(opts.build_number)
        print('convox_release_id is ' + convox_release_id)
        self.promote_convox_release(convox_release_id)
        # self.files.write_environment_variables_file(convox_release_id)
        self.tag_git_repos(opts.build_number)

    def build_complete(self, opts):
        self.login_to_aws_ecr()
        convox_release_id = self.build_with_convox(opts.build_number)
        if opts.tests == 'true':
            self.run_tests_for_release_id(convox_release_id)
        self.promote_convox_release(convox_release_id)
        self.tag_git_repos(opts.build_number)

    def build_only(self, opts):
        self.login_to_aws_ecr()
        convox_release_id = self.build_with_convox(opts.build_number)
        self.promote_convox_release(convox_release_id)
        self.files.write_environment_variables_file(convox_release_id)

    def run_tests(self, opts):
        self.login_to_aws_ecr()
        self.run_tests_for_release_id(opts.convox_release)

    def prepare_anchore(self, opts):
        self.login_to_aws_ecr()
        docker_image_name = self.read_image_name_from_release(opts.convox_release)
        self.pull_docker_image_from_ecr('server application', docker_image_name)
        docker_image_id = self.get_docker_image_id_from_name(docker_image_name)
        self.files.write_anchore_images_file(docker_image_id)

    def build_only_locally(self, opts):
        self.files.write_config_json(self.environment.name)
        if self.environment.use_latest_bmpr:
            self.add_latest_bmpr_version()
        self.build_with_docker('dependencies', 'Dockerfile-dependencies', 'bas-dependencies', {
            'BASE_IMAGE': BASE_IMAGE_FOR_RUNTIME,
            'PRIVATE_NPM_AUTH_TOKEN': self.private_npm_auth_token,
        })
        self.build_with_docker('server', 'Dockerfile-server', 'bas-local', {
            'DEPENDENCIES_IMAGE': 'bas-dependencies',
        })


    # -------------------------------------------------------------------
    # -- Build steps

    def tag_git_repos(self, build_number):
        self.log('Tag git repos')
        tag = 'bas-{0}'.format(build_number)
        subprocess_run(['git', 'tag', tag], cwd=self.files.get_repo_dir('bas'), check=False)
        subprocess_run(['git', 'push', '**************:balsamiq/bas.git', '--tags'], cwd=self.files.get_repo_dir('bas'), check=False)

    def promote_convox_release(self, convox_release):
        self.log('Promoting convox release {0}'.format(convox_release))
        self.convox(['releases', 'promote', convox_release, '--wait'])

    def add_latest_bmpr_version(self):
        title = 'install bmpr'
        build_args = {
            'BASE_IMAGE': BASE_IMAGE_FOR_RUNTIME,
            'PRIVATE_NPM_AUTH_TOKEN': self.private_npm_auth_token,
        }
        temporary_image_name = 'balsamiq/{0}_build:latest'.format(title.replace(' ', '_'))
        temporary_container_name = 'bas_{0}'.format(title.replace(' ', '_'))
        self.build_with_docker(title, 'Dockerfile-install-bmpr', temporary_image_name, build_args=build_args)
        self.log('Extracting {0} from temporary container'.format(title))
        subprocess_run(['docker', 'container', 'create', '--name', temporary_container_name, temporary_image_name], check=True)
        try:
            subprocess_run(['docker', 'cp', '{0}:/app/bas/src/.'.format(temporary_container_name), BAS_SRC_DIR], check=True)
        finally:
            subprocess_run(['docker', 'container', 'rm', '-f', temporary_container_name], check=False)

    def first_build_with_convox(self, build_number):
        self.files.write_config_json(self.environment.name)
        if self.environment.use_latest_bmpr:
            self.add_latest_bmpr_version()
        self.build_and_sync_image_with_ecr('dependencies', 'Dockerfile-dependencies', self.dependencies_image_name, {
            'BASE_IMAGE': BASE_IMAGE_FOR_RUNTIME,
            'PRIVATE_NPM_AUTH_TOKEN': self.private_npm_auth_token,
        })
        self.build_and_sync_image_with_ecr('server', 'Dockerfile-server', self.server_image_name, {
            'DEPENDENCIES_IMAGE': self.dependencies_image_name,
        })

        self.log('Building first server image with Convox')
        convox_yml = self.files.read_convox_yml_server_template()
        convox_yml = convox_yml.replace('[[IMAGE]]', self.server_image_name)
        convox_yml = convox_yml.replace('[[CLOUD_GARDENING_SCHEDULE]]', self.environment.cloud_gardening_schedule)
        convox_yml = convox_yml.replace('[[MAIN_GARDENING_SCHEDULE]]', self.environment.main_gardening_schedule)
        convox_yml = convox_yml.replace('[[BUILD_NUMBER]]', build_number)
        for idx, domain in enumerate(self.environment.domains):
            convox_yml = convox_yml.replace('[[DOMAIN_{0}]]'.format(idx+1), domain)
        self.files.write_convox_yml(convox_yml)

        result = self.convox(['build', '--id', '--no-cache'], with_output=True)  # --no-cache
        convox_release = result.strip()
        time.sleep(2)  # Wait a sec so we avoid a missing image id later
        return convox_release

    def build_with_convox(self, build_number):
        self.files.write_config_json(self.environment.name)
        if self.environment.use_latest_bmpr:
            self.add_latest_bmpr_version()
        self.build_and_sync_image_with_ecr('dependencies', 'Dockerfile-dependencies', self.dependencies_image_name, {
            'BASE_IMAGE': BASE_IMAGE_FOR_RUNTIME,
            'PRIVATE_NPM_AUTH_TOKEN': self.private_npm_auth_token,
        })
        self.build_and_sync_image_with_ecr('server', 'Dockerfile-server', self.server_image_name, {
            'DEPENDENCIES_IMAGE': self.dependencies_image_name,
        })

        self.log('Building server image with Convox')
        convox_yml = self.files.read_convox_yml_server_template()
        convox_yml = convox_yml.replace('[[IMAGE]]', self.server_image_name)
        convox_yml = convox_yml.replace('[[CLOUD_GARDENING_SCHEDULE]]', self.environment.cloud_gardening_schedule)
        convox_yml = convox_yml.replace('[[MAIN_GARDENING_SCHEDULE]]', self.environment.main_gardening_schedule)
        convox_yml = convox_yml.replace('[[BUILD_NUMBER]]', build_number)
        convox_yml = convox_yml.replace('[[DOMAINS]]', '[{0}]'.format(', '.join(self.environment.domains)))
        self.files.write_convox_yml(convox_yml)

        result = self.convox(['build', '--id'], with_output=True)  # --no-cache
        convox_release = result.strip()
        time.sleep(2)  # Wait a sec so we avoid a missing image id later
        return convox_release

    def run_tests_for_release_id(self, convox_release):
        # TODO
        # We can use convox_release instead of building an image from scratch only when both app and tests will run on Node 12
        # docker_image_name = self.read_image_name_from_release(convox_release)
        # self.pull_docker_image_from_ecr('server application', docker_image_name)
        if self.environment.use_latest_bmpr:
            self.add_latest_bmpr_version()
        self.build_and_sync_image_with_ecr('dependencies for tests', 'Dockerfile-dependencies', self.dependencies_for_tests_image_name, {
            'BASE_IMAGE': BASE_IMAGE_FOR_TESTS,
            'PRIVATE_NPM_AUTH_TOKEN': self.private_npm_auth_token,
        })
        self.build_and_sync_image_with_ecr('tests', 'Dockerfile-tests', self.tests_image_name, {
            'DEPENDENCIES_IMAGE': self.dependencies_for_tests_image_name,
            'PRIVATE_NPM_AUTH_TOKEN': self.private_npm_auth_token,
        })
        self.run_tests_in_docker(self.tests_image_name)

    def read_image_name_from_release(self, convox_release):
        self.log('Reading release info for {0}'.format(convox_release))
        result = self.convox(['releases', 'info', convox_release], with_output=True)
        build_lines = [line for line in result.split('\n') if line.startswith('Build')]
        if len(build_lines) != 1:
            raise Exception('Unexpected output from "convox releases info": {0}'.format(result.stdout))
        convox_build_id = build_lines[0].split(' ')[-1]
        docker_image_name = self.build_docker_image_name(convox_build_id)
        print('Convox Build ID: {0}'.format(convox_build_id))
        print('Docker image name: {0}'.format(docker_image_name))
        return docker_image_name

    def get_docker_image_id_from_name(self, docker_image_name):
        self.log('Reading docker image ID')
        result = subprocess_run(
            ['docker', 'image', 'ls', docker_image_name, '--format', '{{ .ID }}'],
            stdout=subprocess.PIPE,  # Capture stdout (but not stderr)
            check=True,  # Raise exception if exit code <> 0
            universal_newlines=True  # Process output as text (instead of binary)
        )
        docker_image_id = result.stdout.strip()
        print('Docker image ID: {0}'.format(docker_image_id))
        return docker_image_id

    def build_docker_image_name(self, name):
        return '{0}:bas.{1}'.format(self.environment.docker_image_base_url, name)

    def login_to_aws_ecr(self):
        if self.logged_in_acr:
            return
        self.log('Logging in with Docker to AWS ECR')
        result = subprocess_run(
            ['aws', 'ecr', 'get-login-password', '--region', self.environment.docker_image_region],
            stdout=subprocess.PIPE,  # Capture stdout (but not stderr)
            check=True,  # Raise exception if exit code <> 0
            universal_newlines=True  # Process output as text (instead of binary)
        )
        aws_login_password = result.stdout.strip()
        subprocess_run(
            'docker login --username AWS --password-stdin {0}'.format(self.environment.aws_dkr_url),
            input=aws_login_password,
            encoding='utf-8',
            shell=True,
            check=True,  # Raise exception if exit code <> 0
        )
        self.logged_in_acr = True

    def run_tests_in_docker(self, docker_image_name):
        self.log('Running tests')

        self.files.write_docker_compose_for_tests()
        env = os.environ.copy()
        env['IMAGE'] = docker_image_name

        subprocess_run('docker compose up -d redis mysql', shell=True, env=env, check=True)
        try:
            subprocess_run('docker compose up --abort-on-container-exit bas', shell=True, env=env, check=True)
        finally:
            subprocess_run('docker compose down', shell=True, env=env, check=True)

    def build_and_sync_image_with_ecr(self, title, dockerfile, image_name, build_args=None):
        self.pull_docker_image_from_ecr(title, image_name, error_if_missing=False)
        self.build_with_docker(title, dockerfile, image_name, build_args=build_args)
        self.push_docker_image_to_ecr(title, image_name)

    def build_with_docker(self, title, dockerfile, image_name, build_args=None):
        self.log('Build {0} - image ID: {1}'.format(title, image_name))
        self.files.write_dockerignore_file()
        self.files.write_dockerfile(dockerfile)
        params = ['docker', 'build', '-t', image_name, '--network', 'host']
        if build_args:
            for name, value in build_args.items():
                params.extend(['--build-arg', '{0}={1}'.format(name, value)])
        params.append('.')
        subprocess_run(params, check=True)

    def pull_docker_image_from_ecr(self, title, docker_image_name, error_if_missing=True):
        self.log('Pulling {0} image from convox registry'.format(title))
        print('Pulling image {0}'.format(docker_image_name))
        subprocess_run(
            ['docker', 'pull', docker_image_name],
            check=error_if_missing,  # Raise exception if exit code <> 0
        )

    def push_docker_image_to_ecr(self, title, docker_image_name):
        self.log('Pushing {0} image to convox registry'.format(title))
        print('Pushing image {0}'.format(docker_image_name))
        subprocess_run(
            ['docker', 'push', docker_image_name],
            check=True,  # Raise exception if exit code <> 0
        )


# -----------------------------------------------------------------------------------------------
# -- Convox utilities
# --

class ConvoxRunner(object):
    def __init__(self, convox_host, convox_password, app):
        self.env = os.environ.copy()
        assert convox_host, 'Unspecified convox host'
        self.env['CONVOX_HOST'] = convox_host
        if convox_password:
            self.env['CONVOX_PASSWORD'] = convox_password
        self.app = app

    def __call__(self, cmd, with_output=False):
        kw = dict(
            env=self.env,
            check=True,
        )
        if with_output:
            kw['stdout'] = subprocess.PIPE  # Capture stdout (but not stderr)
            kw['universal_newlines'] = True  # Process output as text (instead of binary)
        result = subprocess_run(['convox'] + cmd + ['--app', self.app], **kw)
        if with_output:
            return result.stdout


# -----------------------------------------------------------------------------------------------
# -- Logging and timing utilities
# --

@contextmanager
def timed_execution():
    sections_timings = []
    last_section = {}

    def time_section_end(_now):
        if last_section.get('ts', None):
            _elapsed = _now - last_section['ts']
            sections_timings.append((last_section['message'], _elapsed))

    def log(_message):
        print()
        print()
        print('-----------------------------------------------------------------------------')
        print('-- {0}'.format(_message))
        print()
        _now = time.time()
        time_section_end(_now)
        last_section['ts'] = _now
        last_section['message'] = _message

    start_ts = time.time()
    yield log
    now = time.time()
    total_elapsed_time = now - start_ts
    time_section_end(now)
    print()
    print()
    print('-----------------------------------------------------------------------------')
    print('-- Total elapsed time: {0}'.format(format_elapsed(total_elapsed_time)))
    print()
    for message, elapsed in sections_timings:
        print('  {0} - {1}'.format(format_elapsed(elapsed), message))


def format_elapsed(elapsed_sec):
    min, sec = divmod(int(elapsed_sec), 60)
    return '{0}:{1:02}'.format(min, sec)


# -----------------------------------------------------------------------------------------------
# -- Files management utilities
# --

WORK_DIR = os.path.abspath(os.path.join(__file__, '..', '..', '..'))
BAS_DIR = os.path.abspath(os.path.join(WORK_DIR, 'bas'))
BAS_SRC_DIR = os.path.abspath(os.path.join(BAS_DIR, 'src'))
DOCKERFILES_DIR = os.path.join(BAS_DIR, 'build-process', 'dockerfiles')
TEMPLATES_DIR = os.path.join(BAS_DIR, 'build-process', 'templates')
CONFIGFILES_DIR = os.path.join(BAS_DIR, 'build-process', 'config')


class Files(object):
    def __init__(self):
        self.files_to_remove = set()

    def remove_temporary_files(self):
        for filepath in self.files_to_remove:
            os.remove(filepath)

    def write_convox_yml(self, content):
        assert content
        convox_yml_filepath = os.path.join(WORK_DIR, 'convox.yml')
        print(convox_yml_filepath)
        write_ascii_file(convox_yml_filepath, content)
        self.files_to_remove.add(convox_yml_filepath)

    def write_dockerfile(self, dockerfile_name):
        assert dockerfile_name
        dockerfile_filepath = os.path.join(WORK_DIR, 'Dockerfile')
        copy_file(os.path.join(DOCKERFILES_DIR, dockerfile_name), dockerfile_filepath)
        self.files_to_remove.add(dockerfile_filepath)

    def write_config_json(self, environment_name):
        filepath = os.path.join(WORK_DIR, 'config-environment.txt')
        write_ascii_file(filepath, environment_name)
        self.files_to_remove.add(filepath)

    def read_convox_yml_server_template(self):
        return read_ascii_file(os.path.join(TEMPLATES_DIR, 'convox.yml'))

    def write_docker_compose_for_tests(self):
        docker_compose_filepath = os.path.join(WORK_DIR, 'docker-compose.yml')
        copy_file(os.path.join(TEMPLATES_DIR, 'docker-compose-tests.yml'), docker_compose_filepath)
        self.files_to_remove.add(docker_compose_filepath)

    def write_dockerignore_file(self):
        dockerignore_filepath = os.path.join(WORK_DIR, '.dockerignore')
        copy_file(os.path.join(TEMPLATES_DIR, 'dockerignore'), dockerignore_filepath)
        self.files_to_remove.add(dockerignore_filepath)

    def get_repo_dir(self, repo):
        assert repo
        return os.path.join(WORK_DIR, repo)

    def chdir_to_repos_root(self):
        os.chdir(WORK_DIR)

    def write_environment_variables_file(self, convox_release_id):
        content = 'CONVOX_RELEASE={0}'.format(convox_release_id)
        write_ascii_file(os.path.join(WORK_DIR, 'next_build_params.txt'), content)

    def write_anchore_images_file(self, image_id):
        assert image_id
        write_ascii_file(os.path.join(WORK_DIR, 'anchore_images'), image_id)


def write_ascii_file(filepath, content):
    print(content, file=open(filepath, 'w', encoding='ascii'))


def read_ascii_file(filepath):
    return open(filepath, 'r', encoding='ascii').read()


def copy_file(src_filepath, dst_filepath):
    open(dst_filepath, 'w').write(open(src_filepath, 'r').read())


def empty_directory_content(dir_path):
    subprocess_run('rm -rf {0}/*'.format(dir_path), shell=True, check=True)


def subprocess_run(cmd, *args, **kw):
    cmdline = cmd if isinstance(cmd, str) else ' '.join(cmd)
    print('> {0}'.format(cmdline))
    return subprocess.run(cmd, *args, **kw)


# -----------------------------------------------------------------------------------------------
# -- Command line parameters parsing and validation
# --

def main():
    parser = argparse.ArgumentParser(prog='build.py', description='Cloud Build CLI')
    parser.add_argument('environment', help='Environment (staging, ondeck, production, local)', choices=['staging', 'ondeck', 'production', 'local'], metavar='<environment>')
    subparsers = parser.add_subparsers(title='mode', description='The Jenkins job to execute', metavar='<mode>')

    mode_complete_parser = subparsers.add_parser('complete', help='Run complete deployment pipeline (for production)')
    mode_complete_parser.set_defaults(build_method=Builder.build_complete, tests='true')
    mode_complete_parser.add_argument('build_number', help='Build number', metavar='<build_number>')
    mode_complete_parser.add_argument('-t', '--tests', help='Run tests (true, false)', choices=['true', 'false'], metavar='BOOLEAN')

    mode_deploy_parser = subparsers.add_parser('deploy', help='Build only. No tests, no anchore checks')
    mode_deploy_parser.set_defaults(build_method=Builder.build_only)
    mode_deploy_parser.add_argument('build_number', help='Build number', metavar='<build_number>')

    mode_staging_parser = subparsers.add_parser('tests', help='Run tests for staging')
    mode_staging_parser.add_argument('convox_release', help='Convox release ID', metavar='<convox_release_id>')
    mode_staging_parser.set_defaults(build_method=Builder.run_tests)

    mode_anchore_parser = subparsers.add_parser('anchore', help='Run anchore checks for staging')
    mode_anchore_parser.add_argument('convox_release', help='Convox release ID', metavar='<convox_release_id>')
    mode_anchore_parser.set_defaults(build_method=Builder.prepare_anchore)

    mode_local_parser = subparsers.add_parser('build', help='Build Docker only, locally')
    mode_local_parser.set_defaults(build_method=Builder.build_only_locally)

    mode_first_parser = subparsers.add_parser('first_build', help='First convox build only')
    mode_first_parser.set_defaults(build_method=Builder.first_build, build_number="1")

    opts = parser.parse_args()

    if 'build_method' not in opts:
        parser.error('You must specify <mode>')

    Builder(opts).build(opts)


if __name__ == '__main__':
    main()
