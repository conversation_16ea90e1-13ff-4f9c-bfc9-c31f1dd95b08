#!/bin/bash

# Set the container name
CONTAINER_NAME="bas-mysql"

# Function to handle script termination
cleanup() {
  echo "Stopping Docker container..."
  docker stop "$CONTAINER_NAME"
  exit 0
}

# Trap SIGINT (CTRL+C) to run the cleanup function
trap cleanup SIGINT

# Function to create Docker volumes if not present
create_volumes() {
  echo "Ensuring Docker volumes are present..."

  if ! docker volume inspect mysql-config >/dev/null 2>&1; then
    echo "Creating Docker volume mysql-config..."
    docker volume create mysql-config
  else
    echo "Docker volume mysql-config already exists."
  fi

  if ! docker volume inspect mysql-data >/dev/null 2>&1; then
    echo "Creating Docker volume mysql-data..."
    docker volume create mysql-data
  else
    echo "Docker volume mysql-data already exists."
  fi
}

# Function to start the MySQL Docker container
start_docker() {
  echo "Starting MySQL Docker container..."
  docker run --rm --name "$CONTAINER_NAME" \
    -v mysql-data:/var/lib/mysql \
    -v mysql-config:/etc/mysql/conf.d \
    -p 3306:3306 \
    -e MYSQL_ALLOW_EMPTY_PASSWORD=yes \
    -d mysql:8.0.40

  # Check if the container started successfully
  if [ $? -ne 0 ]; then
    echo "Failed to start MySQL Docker container. Exiting."
    exit 1
  fi

  echo "Waiting for MySQL to initialize..."
  for i in {1..5}; do
    if docker exec "$CONTAINER_NAME" mysqladmin ping --silent &>/dev/null; then
      echo "MySQL is ready."
      break
    fi
    echo -n "."
    sleep 1
  done

  # Check and set MySQL root password authentication if not already set
  echo "Checking and configuring MySQL authentication..."
  docker exec "$CONTAINER_NAME" mysql -uroot -e "SELECT user, host, plugin FROM mysql.user WHERE user='root' AND plugin='mysql_native_password';" | grep -q "mysql_native_password"
  if [ $? -ne 0 ]; then
    echo "Setting root user to use mysql_native_password..."
    docker exec "$CONTAINER_NAME" mysql -uroot -e "ALTER USER 'root'@'%' IDENTIFIED WITH mysql_native_password BY ''; FLUSH PRIVILEGES;"
  else
    echo "Root user already configured with mysql_native_password."
  fi
}

# Function to start the Redis server
start_redis() {
  echo "Starting Redis server..."
  redis-server "$(brew --prefix)/etc/redis.conf" &

  # Wait for Redis to initialize
  sleep 1

  if ! pgrep -f redis-server >/dev/null; then
    echo "Failed to start Redis server. Exiting."
    exit 1
  fi

  echo "Redis server is running."
}

# Function to start the Node.js application
start_node() {
  cd src
  echo "Starting Node.js application with BAS_ENV=local..."
  BAS_ENV=local npm run start
}

# Create Docker volumes if needed
create_volumes

# Check script arguments
if [[ "$1" == "--docker-only" ]]; then
  start_docker
  start_redis
  echo "MySQL Docker container and Redis server are running. Use CTRL+C to stop."
  # Keep the script running
  tail -f /dev/null
else
  start_docker
  start_redis
  start_node
fi

# Cleanup is handled automatically when CTRL+C is pressed
