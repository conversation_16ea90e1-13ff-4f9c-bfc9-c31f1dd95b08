#!/bin/bash

domain=$1
port=$2

if [ -z "$domain" ]; then
    domain="bas"
fi

if [ -z "$port" ]; then
    port=8080
fi

# Check ngrok version
version=$(ngrok version | cut -d ' ' -f 3)
major_version=$(echo $version | cut -d '.' -f 1)
minor_version=$(echo $version | cut -d '.' -f 2)

if [ "$major_version" -ge 3 ]; then
    if [ "$minor_version" -ge 16 ]; then
        # For version >= 3.16
        domain=$domain".ngrok.io"
        ngrok http --url $domain $port
    else
        # For version < 3.16 but still >= 3
        domain=$domain".ngrok.io"
        ngrok http --domain=$domain $port
    fi
else
    # For version < 3
    ngrok http -subdomain=$domain $port
fi

