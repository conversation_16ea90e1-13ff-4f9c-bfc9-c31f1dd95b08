#!/bin/bash

domain=$1
if [ -z "$domain" ]; then
    domain="bas"
fi

user=$2
if [ -z "$user" ]; then
    user=''
fi

ngrok_default="https://bas.ngrok.io"
ngrok_domain="https://${domain}.ngrok.io"

DPATH="./*.*"
TFILE="/tmp/out.tmp.$$"

function cleanup() {
    echo "Cleaning up..."
    pkill -f "nginx -c"
    pkill -f launchRedis.sh
    pkill -f launchNgrok.sh
    exit 0
}

# Register the trap early to ensure it is captured
trap cleanup EXIT

# Debug message to confirm the script has reached this point
echo "Trap registered. Starting processes..."

function replace()
{
    OLD="${OLD//\//\\/}"
    NEW="${NEW//\//\\/}"
    for f in $DPATH
    do
      if [ -f $f -a -r $f ]; then
       sed "s/$OLD/$NEW/g" "$f" > $TFILE && mv $TFILE "$f"
      else
       echo "Error: Cannot read $f"
      fi
    done
}

DPATH="../src/config-local.ts"
OLD="${ngrok_default}"  NEW="${ngrok_domain}" replace

# needed to accept cookies from a different domain
if [ "$user" = "andrea" ]; then
  OLD="http://localhost:9000"  NEW="https://cloud-andrea.ngrok.io" replace
  DPATH="../src/server_utils.js"
  OLD="maxAge: 86400,"  NEW="maxAge: 86400, origin: true, credentials: true," replace
fi

if [ "$user" = "bwgd-sax" ]; then
  DPATH="../src/server_utils.js"
  OLD="maxAge: 86400,"  NEW="maxAge: 86400, origin: true, credentials: true," replace
fi


if [ "$user" = "andrea" ]; then
  ./launchRedis.sh & ./launchNginxProxy.sh $user & ./launchNgrok.sh $domain & ./launchNgrok.sh cloud-andrea 9000
elif [ "$user" = "forge" ]; then
  ./launchNginxProxy.sh & ./launchNgrok.sh $domain
else
  ./launchRedis.sh & ./launchNginxProxy.sh $user & ./launchNgrok.sh $domain
fi

wait

DPATH="../src/config-local.ts"
OLD="${ngrok_domain}"  NEW="${ngrok_default}" replace

if [ "$user" = "andrea" ]; then
  OLD="https://cloud-andrea.ngrok.io"  NEW="http://localhost:9000" replace
  DPATH="../src/server_utils.js"
  OLD="maxAge: 86400, origin: true, credentials: true,"  NEW="maxAge: 86400," replace
fi

if [ "$user" = "bwgd-sax" ]; then
  DPATH="../src/server_utils.js"
  OLD="maxAge: 86400, origin: true, credentials: true,"  NEW="maxAge: 86400," replace
fi

echo "Script completed successfully."
