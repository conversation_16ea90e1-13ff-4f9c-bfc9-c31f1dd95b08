worker_processes 1;

events {
        worker_connections 768;
}

http {
        sendfile on;
        keepalive_timeout 65;

        server {
                listen 8080;
                server_name localhost;
                proxy_buffering off;
                proxy_buffer_size 16k;
                proxy_buffers 8 16k;
                access_log off;
                error_log off;
                client_max_body_size 100m;
                location /bw-trello/ {
                        expires 365d;
                        proxy_pass http://localhost:8095/;
                }

                location /bw-atlassian-local/ {
                        expires 365d;
                        proxy_pass http://localhost:8085/;
                }

                location /bw-atlassian/editor/ {
                        expires 365d;
                        proxy_pass http://localhost:8085/editor/;
                }

                location / {
                        proxy_pass http://127.0.0.1:4000;
                        proxy_http_version 1.1;
                        proxy_set_header Upgrade $http_upgrade;
                        proxy_set_header Connection "upgrade";
                        proxy_set_header Host $host;
                }
        }
}
