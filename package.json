{"name": "@balsamiq/bmpr", "version": "3.15.1", "devDependencies": {"@semantic-release/changelog": "5.0.1", "@semantic-release/git": "9.0.0", "@types/node": "^18.19.86", "assert-plus": "^1.0.0", "babel-core": "^6.22.1", "babel-eslint": "10.1.0", "babel-preset-es2015": "^6.22.0", "concurrently": "^8.2.2", "conventional-changelog-conventionalcommits": "4.4.0", "cpx": "1.5.0", "eslint": "4.19.1", "eslint-plugin-es6": "1.0.0", "husky": "4.3.0", "lint-staged": "10.4.0", "mocha": "^3.2.0", "semantic-release": "^17.1.2", "sql.js": "^0.4.0", "typescript": "^5.8.3"}, "dependencies": {"jsuri": "~1.3.1", "uuid-js": "~0.7.5"}, "scripts": {"build": "rimraf lib && tsc && cpx './src/js/**/*.{js,d.ts}' lib --verbose && tsc --noEmit lib/*.ts", "build:watch": "concurrently \"tsc --watch\" \"cpx './src/js/**/*.{js,d.ts}' lib --watch --verbose\"", "test": "mocha -u tdd --compilers js:babel-core/register tests/unit/index.js", "test:watch": "npm run test -- --watch", "lint": "eslint src/**/*.js && tsc --noEmit", "presemantic-release": "npm run lint && npm t && npm run build", "semantic-release": "semantic-release"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": "eslint"}, "main": "lib/index.js", "repository": {"type": "git", "url": "git+https://github.com/balsamiq/bmpr.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/balsamiq/bmpr/issues"}, "homepage": "https://github.com/balsamiq/bmpr#readme", "description": "The library to interact with Balsamiq Archive", "publishConfig": {"registry": "https://npm.balsamiq.com/"}, "release": {"branches": ["master"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/github", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm"], "preset": "conventionalcommits"}, "files": ["lib/**/*.*"]}